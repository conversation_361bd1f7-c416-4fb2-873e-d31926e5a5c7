import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { DatePipe } from '@angular/common';
import { TestBed } from '@angular/core/testing';
import { ValueGetterParams } from 'ag-grid-community';
import { of } from 'rxjs';

import { ConstantSettingFilterOptionGetRequest, Rule1ConstantSetting } from '../interfaces/constant-setting';
import {
  MOCK_COC_PARAMETER_AUDIT_TRAIL_RESPONSE,
  MOCK_COC_PARAMETER_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_CREATE_SERVICE_TICKET_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_PEER_GROUP_SIZE_AUDIT_TRAIL_RESPONSE,
  MOCK_PEER_GROUP_SIZE_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_REQUIRED_QC_DATA_POINTS_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_RULE_1_CONSTANT_SETTING_AUDIT_TRAIL_RESPONSE,
  MOCK_RULE_1_CONSTANT_SETTING_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_RULE_2_to_5_CONSTANT_SETTING_AUDIT_TRAIL_RESPONSE,
  MOCK_RULE_2_to_5_CONSTANT_SETTING_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_RULE_AUDIT_TRAIL_RESPONSE,
  MOCK_RULE_BALANCE_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_RULE_LIMIT_AUDIT_TRAIL_RESPONSE,
  MOCK_RULE_LIMIT_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_RULE_MULTIPLIER_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_RULE_NUMBER_SETTING_AUDIT_TRAIL_RESPONSE,
  MOCK_RULE_NUMBER_SETTING_AUDIT_TRAIL_TABLE_ROWS,
  MOCK_RULE_PARAMETER_AUDIT_TRAIL_RESPONSE,
} from '../mocks/audit-trail';
import { PEER_GROUP_SIZE_SETTINGS, RULE_STANDARD_SETTINGS } from '../mocks/admin-parameters';
import { RuleConfig, RuleTableData } from '../interfaces/rule';
import { AppConfigService } from 'src/app/services/app-config.service';
import { AuditTrailLogType } from 'src/app/enums/audit-trail';
import { AuditTrailQueryParams } from 'src/app/interfaces/audit-trail';
import { AuditTrailService } from 'src/app/services/audit-trail.service';
import { DateTimeUtilities } from 'src/app/helpers/date-time-utilities';
import { NumberInputCellComponent } from '../components/cell-template/number-input-cell/number-input-cell.component';
import { NumberSetting } from '../interfaces/number-setting';
import { RULES } from '../constants';
import { RequiredQcDataPointsAuditTrailTableRow } from '../interfaces/rule-configuration-audit-trail';
import { Rule12To13ParamSetting } from '../interfaces/rule-param-setting';
import { RuleConfigurationService } from './rule-configuration.service';
import { RuleStandardSettings } from '../interfaces/admin-parameters';
import { SIX_MONTHS } from 'src/app/constant';
import { SysmexService } from 'src/app/services/sysmex.service';

const mockRuleConfig: RuleConfig = {
  constantSettingsTable: {
    dataMappingFunc: jest.fn(),
    editableColumns: [],
  },
  multiplierTeaTable: {
    dataMappingFunc: jest.fn(),
    editableColumns: [],
  },
  balanceTeaTable: {
    dataMappingFunc: jest.fn(),
    editableColumns: [],
  },
  upperLowerLimitTable: {
    dataMappingFunc: jest.fn(),
    editableColumns: [],
  },
  ruleCode: '',
  componentsConfig: {},
};

describe('Service: RuleConfiguration', () => {
  let service: RuleConfigurationService;

  let httpMock: HttpTestingController;

  let appConfigServiceMock: Partial<AppConfigService>;

  let mockAuditTrailService: AuditTrailService;

  const mockFormatCollection = {
    DATE_MONTH_YEAR: 'MM/dd/YYYY',
    DATE_MONTH_YEAR_REPORT: 'MMDDYYYY',
    FORMAT_1: 'HH:mm:ss',
    FORMAT_4: 'MM/dd/YYYY HH:mm:ss',
    API_FORMAT: 'YYYY-MM-dd',
  };

  const mockSysmex = {
    formatDate: jest.fn().mockImplementation((_, date) => {
      const datePipe = new DatePipe('en-US');

      return datePipe.transform(date, 'MMM/dd/YYYY HH:mm:ss a');
    }),
    DATES: mockFormatCollection,
  };

  beforeEach(() => {
    appConfigServiceMock = {
      getConfig: jest.fn().mockReturnValue({ webApiUrl: 'http://localhost' }),
    };

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        RuleConfigurationService,
        {
          provide: AppConfigService,
          useValue: appConfigServiceMock,
        },
        {
          provide: SysmexService,
          useValue: mockSysmex,
        },
      ],
    });

    service = TestBed.inject(RuleConfigurationService);

    httpMock = TestBed.inject(HttpTestingController);

    mockAuditTrailService = TestBed.inject(AuditTrailService);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get peer group size settings', () => {
    const mockResponse = PEER_GROUP_SIZE_SETTINGS; // Add expected mock response

    service.getPeerGroupSizeSettings('rule1').subscribe((settings) => {
      expect(settings).toEqual(mockResponse);
    });

    const req = httpMock.expectOne('http://localhost/api/ruleconfiguration/rule1/peergroupsize');

    expect(req.request.method).toEqual('GET');

    req.flush(mockResponse);
  });

  it('should send a PUT request to update peer group size settings', () => {
    const mockPeerGroupSizeSettings = PEER_GROUP_SIZE_SETTINGS;
    const apiUrl = 'http://localhost/api/ruleconfiguration/rule1/peergroupsize';

    service.updatePeerGroupSizeSettings('rule1', mockPeerGroupSizeSettings).subscribe((response) => {
      expect(response).toEqual(mockPeerGroupSizeSettings);
    });

    const req = httpMock.expectOne(apiUrl);

    expect(req.request.method).toBe('PUT');

    expect(req.request.body).toEqual(JSON.stringify(mockPeerGroupSizeSettings));
  });

  it('should retrieve standard settings for a given rule code', () => {
    const expectedSettings: RuleStandardSettings = RULE_STANDARD_SETTINGS[0];

    const testRuleCode = RULES.rule1;

    service.getStandardSettings(testRuleCode).subscribe((settings) => {
      expect(settings).toEqual(expectedSettings);
    });

    const req = httpMock.expectOne(
      (req) =>
        req.url.includes('http://localhost/api/ruleconfiguration/standard') &&
        req.params.get('ruleCode') === testRuleCode,
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedSettings);
  });

  it('should update required QC data points', () => {
    const requiredQcPoints = 5;

    service.updateRequiredQcDataPoint(requiredQcPoints).subscribe((response) => {
      expect(response).toBeTruthy();
    });

    const req = httpMock.expectOne('http://localhost/api/ruleconfiguration/requireddatapoints');

    expect(req.request.method).toBe('PUT');

    expect(req.request.body).toEqual({ requiredQcPoints });

    req.flush({});
  });

  it('should return rule constant settings for rule1', () => {
    const mockConstantSettings: Rule1ConstantSetting[] = [
      {
        parameterId: 1,
        parameterDisplayName: 'paramName',
        parameterType: 'paramType',
        ebqcl: 1,
        teaMultiplier: 1,
        keyName: 'paramType',
      },
    ];

    const filterOptions: ConstantSettingFilterOptionGetRequest = {
      modelGroup: 'group1',
      materialControl: 'control1',
      level: 1,
    };

    service.getRuleConstantSettings('rule1', filterOptions).subscribe((data) => {
      expect(data).toEqual(mockConstantSettings);
    });

    const reqPath = `${service['apiUrl']}/api/ruleconfiguration/rule1/constantsetting`;
    const reqParamsUrl =
      `modelgroup=${filterOptions.modelGroup}` +
      `&materialcontrol=${filterOptions.materialControl}` +
      `&level=${filterOptions.level}`;

    const req = httpMock.expectOne(`${reqPath}?${reqParamsUrl}`);

    expect(req.request.method).toEqual('GET');

    req.flush(mockConstantSettings);
  });

  it('should send a PUT request with the correct URL and body', () => {
    const ruleCode = 'testRuleCode';

    const data: NumberSetting[] = [
      {
        settingKey: 'testSettingKey',
        settingValue: 10,
      },
    ];

    const expectedUrl = `${service['apiUrl']}/api/ruleconfiguration/${ruleCode}`;

    service.updateNumberSettings(ruleCode, data).subscribe((response) => {
      expect(response).toEqual([]);
    });

    const req = httpMock.expectOne(expectedUrl);

    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(data);

    req.flush([]);
  });

  it('should return the expected rule parameter settings', () => {
    const ruleCode = 'rule12';
    const expectedResponse: Rule12To13ParamSetting = {
      balanceTEAs: [
        {
          balanceTEA: 1,
          parameterCode: 'MCV',
        },
      ],
      multiplierAndClinicalTEAs: [
        {
          clinicalTEA: 1.1,
          multiplier: 2,
          parameterCode: 'MCV',
        },
      ],
      upperAndLowerLimits: [
        {
          lowerLimit: 1.1,
          parameterCode: 'MCV',
          upperLimit: 10,
        },
      ],
    };

    service.getRuleParameterSettings(ruleCode).subscribe((response) => {
      expect(response).toEqual(expectedResponse);
    });

    const req = httpMock.expectOne(`${service['apiUrl']}${service['ruleParameterSettingPath']}/${ruleCode}`);

    expect(req.request.method).toBe('GET');
    req.flush(expectedResponse);
  });

  it('should calculate TEA correctly', () => {
    // 3rd party library class, must using mock object by casting type
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const params = {
      data: {
        clinicalTEA: 10,
        multiplier: 2.5,
      },
    } as ValueGetterParams;

    const result = service.calculateTEA(params);

    expect(result).toBe('25.0000');
  });

  it('should return 0 when clinicalTEA or multiplier is 0', () => {
    // 3rd party library class, must using mock object by casting type
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const params = {
      data: {
        clinicalTEA: 0,
        multiplier: 5,
      },
    } as ValueGetterParams;

    const result = service.calculateTEA(params);

    expect(result).toBe('0.0000');

    params.data.clinicalTEA = 10;
    params.data.multiplier = 0;

    const resultWithZeroMultiplier = service.calculateTEA(params);

    expect(resultWithZeroMultiplier).toBe('0.0000');
  });

  describe('getRuleParametersTable', () => {
    it('should return undefined if ruleConfig is undefined', () => {
      expect(service.getRuleParametersTable(undefined, 'constantSettingsTable')).toBeUndefined();
    });

    it('should return constantSettingsTable when tableId is "constantSettingsTable"', () => {
      expect(service.getRuleParametersTable(mockRuleConfig, 'constantSettingsTable')).toBe(
        mockRuleConfig.constantSettingsTable,
      );
    });

    it('should return multiplierTeaTable when tableId is "multiplierTeaSettings"', () => {
      expect(service.getRuleParametersTable(mockRuleConfig, 'multiplierTeaSettings')).toBe(
        mockRuleConfig.multiplierTeaTable,
      );
    });

    it('should return balanceTeaTable when tableId is "balanceTeaSettings"', () => {
      expect(service.getRuleParametersTable(mockRuleConfig, 'balanceTeaSettings')).toBe(mockRuleConfig.balanceTeaTable);
    });

    it('should return upperLowerLimitTable when tableId is "upperLowerLimitSettings"', () => {
      expect(service.getRuleParametersTable(mockRuleConfig, 'upperLowerLimitSettings')).toBe(
        mockRuleConfig.upperLowerLimitTable,
      );
    });
  });

  describe('getRuleConfigurationAuditTrailLog', () => {
    it('should return mapped audit trail data', () => {
      type mockReturnType = RequiredQcDataPointsAuditTrailTableRow;

      const logType = AuditTrailLogType.Rule;
      const key = 'rule1';
      const query: AuditTrailQueryParams = {
        startDate: DateTimeUtilities.getDateWithTimezone(DateTimeUtilities.addMonths(new Date(), -SIX_MONTHS)),
        endDate: DateTimeUtilities.getDateWithTimezone(new Date()),
      };
      const mapperFn = service.mapRequiredQcDataPointsAuditTrailData;

      const mockResponse = MOCK_RULE_AUDIT_TRAIL_RESPONSE;
      const mockTableRows = MOCK_REQUIRED_QC_DATA_POINTS_AUDIT_TRAIL_TABLE_ROWS;

      const getHistoryListsSpy = jest.spyOn(mockAuditTrailService, 'getHistoryLists').mockReturnValue(of(mockResponse));

      const mapAuditTrailDataSpy = jest
        .spyOn(service, 'mapRequiredQcDataPointsAuditTrailData')
        .mockReturnValue(mockTableRows);

      service.getRuleConfigurationAuditTrailLog<mockReturnType>(logType, key, query, mapperFn).subscribe((res) => {
        expect(getHistoryListsSpy).toHaveBeenCalledWith(logType, key, query);
        expect(mapAuditTrailDataSpy).toHaveBeenCalledWith(mockResponse);
        expect(res).toEqual(mockTableRows);
      });
    });
  });

  describe('mapRequiredQcDataPointsAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapRequiredQcDataPointsAuditTrailData(MOCK_RULE_AUDIT_TRAIL_RESPONSE);

      expect(mockResult).toEqual(MOCK_REQUIRED_QC_DATA_POINTS_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapRequiredQcDataPointsAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('mapCreateServiceTicketAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapCreateServiceTicketAuditTrailData(MOCK_RULE_AUDIT_TRAIL_RESPONSE);

      expect(mockResult).toEqual(MOCK_CREATE_SERVICE_TICKET_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapCreateServiceTicketAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('mapRuleNumberSettingAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapRuleNumberSettingAuditTrailData(MOCK_RULE_NUMBER_SETTING_AUDIT_TRAIL_RESPONSE);

      expect(mockResult).toEqual(MOCK_RULE_NUMBER_SETTING_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapRuleNumberSettingAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('mapRule1ConstantSettingAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapRule1ConstantSettingAuditTrailData(
        MOCK_RULE_1_CONSTANT_SETTING_AUDIT_TRAIL_RESPONSE,
      );

      expect(mockResult).toEqual(MOCK_RULE_1_CONSTANT_SETTING_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapRule1ConstantSettingAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('mapRule2To5ConstantSettingAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapRule2To5ConstantSettingAuditTrailData(
        MOCK_RULE_2_to_5_CONSTANT_SETTING_AUDIT_TRAIL_RESPONSE,
      );

      expect(mockResult).toEqual(MOCK_RULE_2_to_5_CONSTANT_SETTING_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapRule2To5ConstantSettingAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('mapRuleCocParameterAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapRuleCocParameterAuditTrailData(MOCK_COC_PARAMETER_AUDIT_TRAIL_RESPONSE);

      expect(mockResult).toEqual(MOCK_COC_PARAMETER_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapRuleCocParameterAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('mapRuleMultiplierAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapRuleMultiplierAuditTrailData(MOCK_RULE_PARAMETER_AUDIT_TRAIL_RESPONSE);

      expect(mockResult).toEqual(MOCK_RULE_MULTIPLIER_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapRuleMultiplierAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('mapRuleBalanceAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapRuleBalanceAuditTrailData(MOCK_RULE_PARAMETER_AUDIT_TRAIL_RESPONSE);

      expect(mockResult).toEqual(MOCK_RULE_BALANCE_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapRuleBalanceAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('mapRuleLimitAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapRuleLimitAuditTrailData(MOCK_RULE_LIMIT_AUDIT_TRAIL_RESPONSE);

      expect(mockResult).toEqual(MOCK_RULE_LIMIT_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapRuleLimitAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('mapPeerGroupSizeAuditTrailData', () => {
    it('should map audit trail data correctly', () => {
      const mockResult = service.mapPeerGroupSizeAuditTrailData(MOCK_PEER_GROUP_SIZE_AUDIT_TRAIL_RESPONSE);

      expect(mockResult).toEqual(MOCK_PEER_GROUP_SIZE_AUDIT_TRAIL_TABLE_ROWS);
    });

    it('should handle empty data array', () => {
      const mockResult = service.mapPeerGroupSizeAuditTrailData([]);

      expect(mockResult).toEqual([]);
    });
  });

  describe('createRuleColumnDefs', () => {
    it('should return the correct column definitions for a given ruleCode and tableId', () => {
      jest.spyOn(service, 'getCols').mockReturnValue({
        colDefs: [],
        editableCols: [
          {
            id: 'col1',
            label: 'Column 1',
            constraints: {
              allowDecimals: false,
            },
          },
          {
            id: 'col2',
            label: 'Column 2',
            constraints: {
              allowDecimals: false,
            },
          },
        ],
      });

      const result = service.createRuleColumnDefs('rule1', 'table1');

      expect(result).toEqual([
        {
          field: 'col1',
          headerName: 'Column 1',
          cellClass: 'text-left',
          headerClass: 'text-left',
          editable: false,
          autoHeight: true,
          cellRenderer: NumberInputCellComponent,
          cellRendererParams: {
            constraints: {
              allowDecimals: false,
            },
          },
        },
        {
          field: 'col2',
          headerName: 'Column 2',
          cellClass: 'text-left',
          headerClass: 'text-left',
          editable: false,
          autoHeight: true,
          cellRenderer: NumberInputCellComponent,
          cellRendererParams: {
            constraints: {
              allowDecimals: false,
            },
          },
        },
      ]);
    });

    it('should include the editingField parameter for specific rules', () => {
      jest.spyOn(service, 'getCols').mockReturnValue({
        colDefs: [],
        editableCols: [
          {
            id: 'col1',
            label: 'Column 1',
            constraints: { allowDecimals: false },
          },
        ],
      });

      const result = service.createRuleColumnDefs(RULES.rule10, 'table1');

      expect(result).toEqual([
        {
          field: 'col1',
          headerName: 'Column 1',
          cellClass: 'text-left',
          headerClass: 'text-left',
          editable: false,
          autoHeight: true,
          cellRenderer: NumberInputCellComponent,
          cellRendererParams: {
            constraints: {
              allowDecimals: false,
            },
            editingField: 'col1',
          },
        },
      ]);
    });

    it('should add the TEA column definition when tableId is multiplierTeaSettings', () => {
      jest.spyOn(service, 'getCols').mockReturnValue({
        colDefs: [],
        editableCols: [],
      });
      jest.spyOn(service, 'calculateTEA').mockReturnValue('10');

      const result = service.createRuleColumnDefs(RULES.rule12, 'multiplierTeaSettings');

      expect(result).toEqual([
        {
          field: 'rule12Tea',
          headerName: ' Rule 12 TEA (%) (Calculated)',
          cellClass: 'text-left',
          headerClass: 'text-left',
          flex: 1,
          valueGetter: expect.any(Function),
          filter: false,
          floatingFilter: false,
          cellStyle: {
            'align-items': 'center',
            display: 'flex',
            'flex-direction': 'row',
            'justify-content': 'center',
          },
        },
      ]);
    });
  });

  describe('adjustRuleValueIntegerToDecimalZero', () => {
    it('should add .0 to integer values in objects', () => {
      const input: RuleTableData[] = [
        {
          parameterId: 1,
          parameterDisplayName: 'paramName',
          parameterType: 'paramType',
          keyName: 'paramType',
          ebqcl: 5,
          teaMultiplier: 7,
          value: 10,
        },
        {
          cocParameterCode: 'code1',
          lowerLimit: 3,
          upperLimit: 8,
          multiplier: 15,
          clinicalTEA: 20,
        },
      ];

      const expectedOutput = [
        {
          parameterId: 1,
          parameterDisplayName: 'paramName',
          parameterType: 'paramType',
          keyName: 'paramType',
          ebqcl: '5.0000',
          teaMultiplier: '7.0000',
          value: '10.0000',
        },
        {
          cocParameterCode: 'code1',
          lowerLimit: '3.0000',
          upperLimit: '8.0000',
          multiplier: '15.0000',
          clinicalTEA: '20.0000',
        },
      ];

      expect(service.adjustRuleValueIntegerToDecimalZero(input)).toEqual(expectedOutput);
    });
  });
});
