import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MOCK_ANALYZER_INFO_RES, MOCK_TILE_CONFIG_TROUBLESHOOT } from '../../mocks/analyzer';
import { Observable, Subject, of } from 'rxjs';
import { AuthService } from '@auth0/auth0-angular';
import { ChangeDetectorRef } from '@angular/core';
import { DesignSystemTestingModule } from '@sysmex/design-system';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MOCK_ANALYZER_GRID } from '../../mocks/analyzers-grid';
import { Router } from '@angular/router';
import moment from 'moment';

import { AnalyzerResponse, AnalyzerStatus } from '../../interfaces/analyzer';
import { DEFAULT_THUMBNAIL_PATH, EMPTY } from 'src/app/constant';
import { ANALYZER_STATUS } from '../../interfaces/enums';
import { AnalyzerService } from '../../services/analyzer.service';
import { AnalyzerStatusTileComponent } from './analyzer-status-tile.component';
import { AnalyzerTroubleshootingService } from '../../services/analyzer-troubleshooting.service';
import { CountDownService } from 'src/app/services/countdown.service';
import { SiteSettingsService } from 'src/app/services/site.service';
import { UserStoreService } from 'src/app/services/user-store.service';
import { Utils } from 'src/app/helpers/UtilFunctions';

class MockAuthService {
  getAccessTokenSilently(): Observable<string> {
    return of('mock-access-token');
  }
}

describe('AnalyzerStatusTileComponent', () => {
  let analyzerStatusTileComponent: AnalyzerStatusTileComponent;

  let analyzerStatusTileFixture: ComponentFixture<AnalyzerStatusTileComponent>;

  let router: Router;

  let mockAnalyzerDetailContentService: Partial<AnalyzerService>;

  let mockAnalyzerTroubleshootingService: Partial<AnalyzerTroubleshootingService>;
  let qcDueTimerReceiver: Subject<void>;
  let mockCountdownService: Partial<CountDownService>;

  const mockSiteSettingService = {
    getDefaultSiteInfo: jest.fn().mockReturnValue(of({
      siteCode: '2004006649',
      siteName: 'SYSMEX AMERICA CTR FOR LEARN (other)',
      isDefaultSite: true,
      isServiceL2: false,
      timeZone: '+02:00',
      timeZoneName: 'Mountain Standard Time',
      timeZoneAbbreviation: 'MST',
    })),
  }

  beforeEach(async () => {
    qcDueTimerReceiver = new Subject<void>();
    mockAnalyzerDetailContentService = {
      getQCDue: jest.fn(),
      isLoadingIotStatus: jest.fn().mockReturnValue(false),
      defineAnalyzerStatusPriority: jest.fn().mockReturnValue('WB'),
      isSeparatedStatus: jest.fn().mockReturnValue(false),
      handleQcPastDue: jest.fn(),
    };

    mockAnalyzerTroubleshootingService = {
      getTroubleshootingDetails: jest.fn().mockReturnValue(of({})),
      openTroubleshootingDialog: jest.fn(),
    };

    mockCountdownService = {
      registerTimer: jest.fn().mockReturnValue({
        receiver: qcDueTimerReceiver.asObservable(),
      }),
      stopCountDown: jest.fn(),
    };

    await TestBed.configureTestingModule({
      providers: [
        {
          provide: AuthService,
          useClass: MockAuthService,
        },
        {
          provide: AnalyzerService,
          useValue: mockAnalyzerDetailContentService,
        },
        {
          provide: AnalyzerTroubleshootingService,
          useValue: mockAnalyzerTroubleshootingService,
        },
        {
          provide: CountDownService,
          useValue: mockCountdownService,
        },
        {
          provide: SiteSettingsService,
          useValue: mockSiteSettingService,
        },
        {
          provide: UserStoreService,
          useValue: {
            getUserEmail: jest.fn().mockReturnValue(''),
          },
        },
        ChangeDetectorRef,
      ],
      imports: [HttpClientTestingModule, DesignSystemTestingModule],
    }).compileComponents();

    analyzerStatusTileFixture = TestBed.createComponent(AnalyzerStatusTileComponent);

    analyzerStatusTileComponent = analyzerStatusTileFixture.componentInstance;
    mockAnalyzerDetailContentService = TestBed.inject(AnalyzerService);
    mockAnalyzerTroubleshootingService = TestBed.inject(AnalyzerTroubleshootingService);
    mockCountdownService = TestBed.inject(CountDownService);

    router = TestBed.inject(Router);

    analyzerStatusTileComponent.analyzerInfo = MOCK_ANALYZER_INFO_RES;
    analyzerStatusTileComponent.analyzerConf = MOCK_TILE_CONFIG_TROUBLESHOOT;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(analyzerStatusTileComponent).toBeTruthy();
  });

  it('should navigate to analyzer detail page', () => {
    const navigateByUrlSpy = jest.spyOn(router, 'navigateByUrl');

    analyzerStatusTileComponent.isAnalyzerDetail = false;
    analyzerStatusTileComponent.modelCode = 'XN-10';
    analyzerStatusTileComponent.serialNumber = '12345';

    analyzerStatusTileComponent.navigate();

    expect(navigateByUrlSpy).toHaveBeenCalledWith('analyzer-status/analyzer-detail/XN-10/12345');
  });

  it('should navigate to analyzer detail page with xbamr params', () => {
    const navigateSpy = jest.spyOn(router, 'navigate');

    analyzerStatusTileComponent.isAnalyzerDetail = false;
    analyzerStatusTileComponent.modelCode = 'XN-10';
    analyzerStatusTileComponent.serialNumber = '12345';

    //3rd party library class, must using mock object by casting type
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    analyzerStatusTileComponent.navigateToXbarm({
      stopPropagation: jest.fn(),
    } as unknown as Event);

    expect(navigateSpy).toHaveBeenCalledWith(['analyzer-status/analyzer-detail/XN-10/12345'], {
      queryParams: { tab: 'XbarM' },
    });
  });

  it('should set qcStatus, qcStatusText, and timer to EMPTY if qcDue or approachingTime is falsy', () => {
    analyzerStatusTileComponent.analyzerInfo = {
      analyzerStatus: {
        qcDue: EMPTY,
        approachingTime: EMPTY,
        status: ANALYZER_STATUS.InActive,
        isBfWarning: false,
        qcDueStatus: null,
        isCheckXbarmData: false,
        qcDueWbStatus: null,
        qcDueBfStatus: null,
        isWarningCustomerLimit: false,
        isWarningCountryLimit: false,
        isWarningAssayLimit: false,
        isWarningMultiRulesLimit: false,
        isWarningCustomerLimitBf: false,
        isWarningCountryLimitBf: false,
        isWarningAssayLimitBf: false,
        isWarningMultiRulesLimitBf: false,
      },
      modelCode: EMPTY,
      serialNumber: EMPTY,
      modelThumbnail: EMPTY,
      referenceId: EMPTY,
      isEnabled: false,
      dueTime: EMPTY,
      timeBeforeNotify: 0,
      modelGroupId: EMPTY,
      modelGroupDisplay: EMPTY,
      isEnabledXbarm: false,
    };

    expect(analyzerStatusTileComponent.analyzerConf.qcDueWbStatus).toBe(null);
    expect(analyzerStatusTileComponent.qcBfStatusText).toBe(EMPTY);
    expect(analyzerStatusTileComponent.timer).toBe(EMPTY);
  });

  it('should set qcStatus, qcStatusText, and timer to EMPTY if qcDue or approachingTime is falsy', () => {
    analyzerStatusTileComponent.analyzerInfo = {
      analyzerStatus: {
        qcDueStatus: null,
        isBfWarning: false,
        qcDue: EMPTY,
        approachingTime: EMPTY,
        status: ANALYZER_STATUS.InActive,
        isCheckXbarmData: false,
        qcDueWbStatus: null,
        qcDueBfStatus: null,
        isWarningCustomerLimit: false,
        isWarningCountryLimit: false,
        isWarningAssayLimit: false,
        isWarningMultiRulesLimit: false,
        isWarningCustomerLimitBf: false,
        isWarningCountryLimitBf: false,
        isWarningAssayLimitBf: false,
        isWarningMultiRulesLimitBf: false,
      },
      modelCode: EMPTY,
      serialNumber: EMPTY,
      modelThumbnail: EMPTY,
      referenceId: EMPTY,
      isEnabled: false,
      dueTime: EMPTY,
      timeBeforeNotify: 0,
      modelGroupId: EMPTY,
      modelGroupDisplay: EMPTY,
      isEnabledXbarm: false,
    };

    expect(analyzerStatusTileComponent.analyzerConf.qcDueWbStatus).toBe(null);
    expect(analyzerStatusTileComponent.qcWbStatusText).toBe(EMPTY);
    expect(analyzerStatusTileComponent.timer).toBe(EMPTY);
  });

  it('should set qcStatus, qcStatusText, timer, and call countDownQcDueDateTime', () => {
    const analyzerInfo: AnalyzerResponse = {
      referenceId: 'ref123',
      modelThumbnail: 'thumbnail.png',
      isEnabled: true,
      nickname: 'Analyzer',
      modelCode: 'ModelX',
      serialNumber: '12345',
      analyzerStatus: {
        qcDueStatus: 'QcPastDue',
        status: ANALYZER_STATUS.ServiceNotified,
        isBfWarning: false,
        qcDue: EMPTY,
        approachingTime: EMPTY,
        isCheckXbarmData: false,
        qcDueWbStatus: 'QcPastDue',
        qcDueBfStatus: null,
        isWarningCustomerLimit: false,
        isWarningCountryLimit: false,
        isWarningAssayLimit: false,
        isWarningMultiRulesLimit: false,
        isWarningCustomerLimitBf: false,
        isWarningCountryLimitBf: false,
        isWarningAssayLimitBf: false,
        isWarningMultiRulesLimitBf: false,
      },
      dueTime: EMPTY,
      timeBeforeNotify: 0,
      modelGroupId: EMPTY,
      modelGroupDisplay: EMPTY,
      isEnabledXbarm: false,
    };

    analyzerStatusTileComponent.analyzerInfo = analyzerInfo;

    expect(analyzerStatusTileComponent.analyzerConf.qcDueWbStatus).toBeDefined();
    expect(analyzerStatusTileComponent.qcWbStatusText).toBeDefined();
    expect(analyzerStatusTileComponent.timer).toBeDefined();

    const isAfterApproachingDueTime = true;

    if (analyzerInfo.analyzerStatus.qcDue !== null && isAfterApproachingDueTime) {
      analyzerStatusTileComponent.analyzerConf.qcDueWbStatus = analyzerInfo.analyzerStatus.qcDueStatus;
      analyzerStatusTileComponent.qcWbStatusText = analyzerStatusTileComponent.getQcStatus(
        analyzerStatusTileComponent.analyzerConf.qcDueWbStatus,
      );

      analyzerStatusTileComponent.countDownQcDue(analyzerInfo.analyzerStatus.qcDue, false);
    }

    expect(analyzerStatusTileComponent.analyzerConf.qcDueWbStatus).toBe('QcPastDue');
    expect(analyzerStatusTileComponent.qcWbStatusText).toBe(analyzerStatusTileComponent.getQcStatus('QcPastDue'));
  });

  it('should return ANALYZER_STATUS constant', () => {
    expect(analyzerStatusTileComponent.ANALYZER_STATUS).toBe(ANALYZER_STATUS);
  });

  it('should set up countdown timer and update timer property', () => {
    const qcDateTime = '2023-10-01T00:00:00Z';
    const mockTimerValue = '10 minutes';

    jest.spyOn(analyzerStatusTileComponent, 'countDownQcDue').mockImplementation(() => 'execution of countdown');

    jest.spyOn(mockAnalyzerDetailContentService, 'getQCDue').mockReturnValue(mockTimerValue);

    analyzerStatusTileComponent.countDownQcDue(qcDateTime, true);

    expect(analyzerStatusTileComponent.countDownQcDue).toHaveBeenCalled();
  });

  describe('Navigation tests', () => {
    let navigateByUrlSpy: jest.SpyInstance;

    beforeEach(() => {
      navigateByUrlSpy = jest.spyOn(router, 'navigateByUrl');
      analyzerStatusTileComponent.isAnalyzerDetail = false;
      analyzerStatusTileComponent.modelCode = 'XN-10';
      analyzerStatusTileComponent.serialNumber = '12345';
    });

    it('should navigate to analyzer detail page for standard case', () => {
      analyzerStatusTileComponent.navigate();
      expect(navigateByUrlSpy).toHaveBeenCalledWith('analyzer-status/analyzer-detail/XN-10/12345');
    });

    it.each([
      {
        case: 'service notified',
        analyzerInfo: MOCK_ANALYZER_GRID[0],
      },
      {
        case: 'troubleshoot',
        analyzerInfo: MOCK_ANALYZER_GRID[1],
      },
      {
        case: 'troubleshoot bf',
        analyzerInfo: MOCK_ANALYZER_GRID[2],
      },
    ])('should navigate to analyzer detail page case $case', ({ analyzerInfo }) => {
      analyzerStatusTileComponent.analyzerInfo = analyzerInfo;
      analyzerStatusTileComponent.navigate();
      expect(navigateByUrlSpy).toHaveBeenCalledWith('analyzer-status/analyzer-detail/XN-10/12345');
    });

    it('should navigate to analyzer detail page case offline', () => {
      analyzerStatusTileComponent.analyzerInfo = {
        modelCode: 'XN-10',
        serialNumber: '11002',
        modelThumbnail: 'https://unity-assets.sysmex.com/models/xn10.png',
        referenceId: '121583',
        isEnabled: false,
        analyzerStatus: {
          approachingTime: EMPTY,
          isBfWarning: false,
          qcDue: EMPTY,
          status: ANALYZER_STATUS.Offline,
          qcDueStatus: 'QcDue',
          isCheckXbarmData: false,
          qcDueWbStatus: null,
          qcDueBfStatus: null,
          isWarningCustomerLimit: false,
          isWarningCountryLimit: false,
          isWarningAssayLimit: false,
          isWarningMultiRulesLimit: false,
          isWarningCustomerLimitBf: false,
          isWarningCountryLimitBf: false,
          isWarningAssayLimitBf: false,
          isWarningMultiRulesLimitBf: false,
        },
        dueTime: EMPTY,
        timeBeforeNotify: 0,
        modelGroupId: EMPTY,
        modelGroupDisplay: EMPTY,
        isEnabledXbarm: false,
      };

      analyzerStatusTileComponent.navigate();
      expect(navigateByUrlSpy).toHaveBeenCalledWith('analyzer-status/analyzer-detail/XN-10/12345');
    });
  });

  describe('getQcStatus', () => {
    it('should return "QC Past Due" when qcStatus is "QcPastDue"', () => {
      const result = analyzerStatusTileComponent.getQcStatus('QcPastDue');

      expect(result).toBe('QC Past Due');
    });

    it('should return "QC Due" when qcStatus is "QcDue"', () => {
      const result = analyzerStatusTileComponent.getQcStatus('QcDue');

      expect(result).toBe('QC Due');
    });

    it('should return EMPTY when qcStatus is null', () => {
      const result = analyzerStatusTileComponent.getQcStatus(null);

      expect(result).toBe(EMPTY);
    });
  });

  describe('Troubleshooting tests', () => {
    let _analyzerInfo: AnalyzerResponse;

    beforeEach(() => {
      _analyzerInfo = {
        analyzerStatus: {
          qcDueStatus: 'QcPastDue',
          status: ANALYZER_STATUS.Troubleshoot,
          isBfWarning: false,
          qcDue: EMPTY,
          approachingTime: EMPTY,
          enableTroubleshootingTime: '2023-06-05T12:00:00Z',
          isCheckXbarmData: false,
          qcDueWbStatus: null,
          qcDueBfStatus: null,
          isWarningCustomerLimit: false,
          isWarningCountryLimit: false,
          isWarningAssayLimit: false,
          isWarningMultiRulesLimit: false,
          isWarningCustomerLimitBf: false,
          isWarningCountryLimitBf: false,
          isWarningAssayLimitBf: false,
          isWarningMultiRulesLimitBf: false,
        },
        timeBeforeNotify: 0,
        tileConfig: {
          status: ANALYZER_STATUS.ServiceNotified,
          color: EMPTY,
          statusColor: EMPTY,
          statusText: EMPTY,
          secondStatusText: EMPTY,
          statusIcon: EMPTY,
          isTroubleshoot: false,
          isBfWarning: false,
          qcDueStatus: null,
          qcDueBfStatus: null,
          qcDueWbStatus: null,
        },
        modelCode: EMPTY,
        serialNumber: EMPTY,
        modelThumbnail: EMPTY,
        referenceId: EMPTY,
        isEnabled: false,
        dueTime: EMPTY,
        modelGroupId: EMPTY,
        modelGroupDisplay: EMPTY,
        isEnabledXbarm: false,
      };
    });

    it('should set isTroubleshootDisable to false when time difference is negative', () => {
      analyzerStatusTileComponent.analyzerInfo = _analyzerInfo;

      Utils.getCurrentTimeDiff = jest.fn().mockReturnValue({
        asMilliseconds: () => -1,
      });

      analyzerStatusTileComponent.setTroubleshootingCountDown(_analyzerInfo);

      expect(analyzerStatusTileComponent.isTroubleshootDisable).toBe(false);
    });

    it('should keep isTroubleshootDisable as false when already false', () => {
      analyzerStatusTileComponent.analyzerInfo = _analyzerInfo;
      analyzerStatusTileComponent.isTroubleshootDisable = false;

      analyzerStatusTileComponent.setTroubleshootingCountDown(_analyzerInfo);

      expect(analyzerStatusTileComponent.isTroubleshootDisable).toBe(false);
    });

    it('should set isTroubleshootDisable to false when enableTroubleshootingTime is empty', () => {
      _analyzerInfo.analyzerStatus.enableTroubleshootingTime = EMPTY;
      _analyzerInfo.analyzerStatus.qcDueWbStatus = 'QcPastDue';

      analyzerStatusTileComponent.analyzerInfo = _analyzerInfo;

      analyzerStatusTileComponent.setTroubleshootingCountDown(_analyzerInfo);

      expect(analyzerStatusTileComponent.isTroubleshootDisable).toBe(false);
    });
  });

  describe('onClickTroubleshoot', () => {
    let mockMouseEvent: MouseEvent;

    beforeEach(() => {
      mockMouseEvent = new MouseEvent('click');
      jest.spyOn(mockMouseEvent, 'stopPropagation');
      jest.spyOn(mockAnalyzerTroubleshootingService, 'getTroubleshootingDetails');
      jest.spyOn(mockAnalyzerTroubleshootingService, 'openTroubleshootingDialog');

      analyzerStatusTileComponent.analyzerTroubleshootingInformation = {
        analyzerModel: 'model',
        analyzerSerial: 'serial',
      };
    });

    it('should execute troubleshooting when enabled and information is available', () => {
      // Arrange
      analyzerStatusTileComponent.isTroubleshootDisable = false;

      // Act
      analyzerStatusTileComponent.onClickTroubleshoot(mockMouseEvent);

      // Assert
      expect(mockMouseEvent.stopPropagation).toHaveBeenCalled();
      expect(mockAnalyzerTroubleshootingService.getTroubleshootingDetails).toHaveBeenCalledWith(
        analyzerStatusTileComponent.analyzerTroubleshootingInformation,
      );
      expect(mockAnalyzerTroubleshootingService.openTroubleshootingDialog).toHaveBeenCalledWith({});
    });

    it('should not call service methods when troubleshooting is disabled', () => {
      // Arrange
      analyzerStatusTileComponent.isTroubleshootDisable = true;

      // Act
      analyzerStatusTileComponent.onClickTroubleshoot(mockMouseEvent);

      // Assert
      expect(mockMouseEvent.stopPropagation).toHaveBeenCalled();
      expect(mockAnalyzerTroubleshootingService.getTroubleshootingDetails).not.toHaveBeenCalled();
      expect(mockAnalyzerTroubleshootingService.openTroubleshootingDialog).not.toHaveBeenCalled();
    });

    it('should not call service methods when troubleshooting information is missing', () => {
      // Arrange
      analyzerStatusTileComponent.isTroubleshootDisable = false;
      analyzerStatusTileComponent.analyzerTroubleshootingInformation = undefined;

      // Act
      analyzerStatusTileComponent.onClickTroubleshoot(mockMouseEvent);

      // Assert
      expect(mockMouseEvent.stopPropagation).toHaveBeenCalled();
      expect(mockAnalyzerTroubleshootingService.getTroubleshootingDetails).not.toHaveBeenCalled();
      expect(mockAnalyzerTroubleshootingService.openTroubleshootingDialog).not.toHaveBeenCalled();
    });
  });

  it('should set the image src to defaultThumbnail on error', () => {
    const target = {
      src: 'invalid path',
    };
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const event = {
      target,
    } as unknown as Event;

    analyzerStatusTileComponent.onImageError(event);

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    expect((event.target as HTMLImageElement).src).toBe(DEFAULT_THUMBNAIL_PATH);
  });

  describe('handleQCStatus', () => {
    it('should set qcStatus, qcStatusText, and timer to EMPTY if qcDue or approachingTime is false', () => {
      const analyzerInfo: AnalyzerStatus = {
        qcDue: EMPTY,
        approachingTime: EMPTY,
        status: ANALYZER_STATUS.InActive,
        isBfWarning: false,
        qcDueStatus: null,
        isCheckXbarmData: false,
        qcDueWbStatus: null,
        qcDueBfStatus: null,
      };

      analyzerStatusTileComponent.qcDueTimer = {
        receiver: of(),
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
        timer: {} as any,
      };

      analyzerStatusTileComponent.handleQcDueStatus(analyzerInfo);

      expect(mockCountdownService.stopCountDown).toHaveBeenCalled();
      expect(analyzerStatusTileComponent.qcWbStatusText).toBe(EMPTY);
      expect(analyzerStatusTileComponent.timer).toBe(EMPTY);
    });

    it(`should set qcStatus, qcStatusText, and timer correctly when
        qcDue is not null and isAfterApproachingDueTime is true`, () => {
      const analyzerStatus: AnalyzerStatus = {
        qcDueStatus: 'QcPastDue',
        status: ANALYZER_STATUS.ReadyForSamples,
        isBfWarning: false,
        qcDue: '2024-11-21T12:00:00Z',
        approachingTime: moment().subtract(1, 'days').toISOString(),
        isCheckXbarmData: false,
        qcDueWbStatus: 'QcPastDue',
        qcDueBfStatus: null,
        isWarningCustomerLimit: false,
        isWarningCountryLimit: false,
        isWarningAssayLimit: false,
        isWarningMultiRulesLimit: false,
        isWarningCustomerLimitBf: false,
        isWarningCountryLimitBf: false,
        isWarningAssayLimitBf: false,
        isWarningMultiRulesLimitBf: false,
      };

      jest.spyOn(mockAnalyzerDetailContentService, 'getQCDue').mockReturnValue('1 hour');
      jest.spyOn(analyzerStatusTileComponent, 'getQcStatus').mockReturnValue('QC Past Due');
      jest.spyOn(analyzerStatusTileComponent, 'countDownQcDue').mockImplementation();

      analyzerStatusTileComponent.handleQcDueStatus(analyzerStatus);

      expect(analyzerStatusTileComponent.qcWbStatusText).toBe('QC Past Due');
      expect(analyzerStatusTileComponent.timer).toBeDefined();
      expect(analyzerStatusTileComponent.countDownQcDue).toHaveBeenCalled();
    });

    it('should update timerTextProperty and handle QC Past Due logic', () => {
      jest.spyOn(mockAnalyzerDetailContentService, 'getQCDue').mockReturnValue('-10 minutes');

      analyzerStatusTileComponent.analyzerConf.qcDueWbStatus = 'QcDue';
      analyzerStatusTileComponent.countDownQcDue('2023-12-01T00:00:00Z', false);

      qcDueTimerReceiver.next();

      expect(mockAnalyzerDetailContentService.getQCDue).toHaveBeenCalledWith(
        '2023-12-01T00:00:00Z',
        true,
        EMPTY,
        EMPTY,
      );

      expect(mockAnalyzerDetailContentService.handleQcPastDue).toHaveBeenCalled();
      expect(analyzerStatusTileComponent.qcWbStatusText).toBe($localize`:label:QC Past Due`);
    });

    it('should not update if tileConfig is undefined', () => {
      analyzerStatusTileComponent.analyzerInfo.tileConfig = undefined;

      analyzerStatusTileComponent.countDownQcDue('2023-12-01T00:00:00Z', false);

      qcDueTimerReceiver.next();

      expect(mockAnalyzerDetailContentService.getQCDue).not.toHaveBeenCalled();
      expect(mockAnalyzerDetailContentService.handleQcPastDue).not.toHaveBeenCalled();
    });
  });
});
