import { PageAcl } from 'src/app/interfaces/user';

const serivceConfig = {
  serviceLevel: [2, 3],
  specificCountry: null,
};

const nullServiceConfig = {
  serviceLevel: null,
  specificCountry: null,
};

export const MOCK_PMS_RESULTS: PageAcl[] = [
  {
    pageCode: 'Analyzers',
    isMenuItem: true,
    canWrite: false,
    pagePath: '/analyzer-status',
    ...serivceConfig,
    pages: [
      {
        pageCode: 'AnalyzerSummary',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/analyzer-status/summary',
        ...serivceConfig,
        pages: [],
      },
      {
        pageCode: 'AnalyzerDetail',
        isMenuItem: false,
        canWrite: false,
        pagePath: '/analyzer-status/analyzer-detail/:modelCode/:serialNumber',
        ...serivceConfig,
        pages: [
          {
            pageCode: 'BasicChart',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/basic-chart/:modelCode/:serialNumber/',
            serviceLevel: [2],
            specificCountry: null,
            pages: [],
          },
          {
            pageCode: 'AdvancedLJChart',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/advanced-lj-chart/:modelCode/:serialNumber',
            serviceLevel: [3],
            specificCountry: null,
            pages: [],
          },
          {
            pageCode: 'CountrySpecificChart',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/country-specific-chart/:modelCode/:serialNumber',
            serviceLevel: null,
            specificCountry: ['DEU'],
            pages: [],
          },
          {
            pageCode: 'ZScoreChart',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/zscore-chart/:modelCode/:serialNumber',
            serviceLevel: [2],
            specificCountry: null,
            pages: [],
          },
          {
            pageCode: 'XbarMChart',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/xbarm-chart/:modelCode/:serialNumber',
            ...serivceConfig,
            pages: [],
          },
          {
            pageCode: 'ComparisonChartMultiAnalyzers',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/comparison-chart-multi-analyzers/:modelCode/:serialNumber',
            ...serivceConfig,
            pages: [],
          },
          {
            pageCode: 'SDIChartSingleAnalyzer',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/sdi-chart-single-analyzer/:modelCode/:serialNumber',
            serviceLevel: [2],
            specificCountry: null,
            pages: [],
          },
          {
            pageCode: 'SDIChartMultiAnalyzers',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/sdi-chart-multi-analyzers/:modelCode/:serialNumber',
            serviceLevel: [2],
            specificCountry: null,
            pages: [],
          },
          {
            pageCode: 'ComparisonChartToXBarMChart',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/comparison-chart-to-xbarm/:modelCode/:serialNumber',
            serviceLevel: [3],
            specificCountry: null,
            pages: [],
          },
          {
            pageCode: 'ComparisonChartToPreviousLot',
            isMenuItem: false,
            canWrite: false,
            pagePath: '/analyzer-status/analyzer-detail/comparison-chart-to-previous-lot/:modelCode/:serialNumber',
            ...serivceConfig,
            pages: [],
          },
        ],
      },
      {
        pageCode: 'FileUpload',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/analyzer-status/file-upload',
        ...serivceConfig,
        pages: [],
      },
    ],
  },
  {
    pageCode: 'Reports',
    isMenuItem: true,
    canWrite: false,
    pagePath: '/reports',
    ...serivceConfig,
    pages: [
      {
        pageCode: 'BackgroundCount',
        isMenuItem: false,
        canWrite: true,
        pagePath: '/reports/background-counts',
        ...serivceConfig,
        pages: [],
      },
      {
        pageCode: 'CalibrationReport',
        isMenuItem: false,
        canWrite: true,
        pagePath: '/reports/calibration-report',
        ...serivceConfig,
        pages: [],
      },
      {
        pageCode: 'MonthlyRilibakReport',
        isMenuItem: false,
        canWrite: true,
        pagePath: '/reports/calibration-report',
        ...serivceConfig,
        specificCountry: ['DEU'],
        pages: [],
      },
    ],
  },
  {
    pageCode: 'UserActivity',
    isMenuItem: true,
    canWrite: true,
    pagePath: '/user-activity',
    ...serivceConfig,
    pages: [],
  },
  {
    pageCode: 'Settings',
    isMenuItem: true,
    canWrite: false,
    pagePath: '/settings',
    ...serivceConfig,
    pages: [
      {
        pageCode: 'Sites',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/settings/sites',
        serviceLevel: [3],
        specificCountry: null,
        pages: [],
      },
      {
        pageCode: 'QCTargetsAndLimits',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/settings/qc-targets-and-limits',
        ...serivceConfig,
        pages: [
          {
            pageCode: 'AssayLimits',
            isMenuItem: true,
            canWrite: true,
            pagePath: '/settings/qc-targets-and-limits/assay-limits',
            ...serivceConfig,
            pages: [],
          },
          {
            pageCode: 'AssayUpload',
            isMenuItem: true,
            canWrite: true,
            pagePath: '/settings/qc-targets-and-limits/assay-upload',
            ...serivceConfig,
            pages: [],
          },
          {
            pageCode: 'CustomerLimits',
            isMenuItem: true,
            canWrite: false,
            pagePath: '/settings/qc-targets-and-limits/customer-limits',
            ...serivceConfig,
            pages: [],
          },
        ],
      },
      {
        pageCode: 'SysmexRules',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/settings/level3-rules',
        ...nullServiceConfig,
        pages: [],
      },
      {
        pageCode: 'L2Rules',
        isMenuItem: true,
        canWrite: false,
        pagePath: '/settings/level2-rules',
        serviceLevel: [2],
        specificCountry: null,
        pages: [
          {
            pageCode: 'LimitTypeSelection',
            isMenuItem: true,
            canWrite: false,
            pagePath: '/settings/level2-rules/limit-type-selection',
            serviceLevel: [2],
            specificCountry: null,
            pages: [],
          },
          {
            pageCode: 'MultiRulesConfiguration',
            isMenuItem: true,
            canWrite: false,
            pagePath: '/settings/level2-rules/multi-rules-configuration',
            serviceLevel: [2],
            specificCountry: null,
            pages: [],
          },
        ],
      },
      {
        pageCode: 'Group',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/settings/group',
        ...nullServiceConfig,
        pages: [
          {
            pageCode: 'AffiliatedPeerGroup',
            isMenuItem: true,
            canWrite: true,
            pagePath: '/settings/group/affiliated-peer-group',
            ...nullServiceConfig,
            pages: [],
          },
          {
            pageCode: 'ErrorJudgment',
            isMenuItem: true,
            canWrite: true,
            pagePath: '/settings/group/error-judgment',
            ...nullServiceConfig,
            pages: [],
          },
        ],
      },
      {
        pageCode: 'QCParameters',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/settings/qc-parameters',
        ...nullServiceConfig,
        pages: [],
      },
      {
        pageCode: 'Troubleshooting',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/settings/troubleshooting',
        ...nullServiceConfig,
        pages: [
          {
            pageCode: 'InstructionStep',
            isMenuItem: true,
            canWrite: true,
            pagePath: '/settings/troubleshooting/instruction-step',
            ...nullServiceConfig,
            pages: [],
          },
          {
            pageCode: 'ChannelWorkflow',
            isMenuItem: true,
            canWrite: true,
            pagePath: '/settings/troubleshooting/channel-workflow',
            ...nullServiceConfig,
            pages: [],
          },
        ],
      },
    ],
  },
  {
    pageCode: 'XbarM',
    isMenuItem: true,
    canWrite: true,
    pagePath: '/xbarm',
    ...serivceConfig,
    pages: [
      {
        pageCode: 'XbarMTargetsAndLimits',
        isMenuItem: true,
        canWrite: false,
        pagePath: '/xbarm/xbarm-targets-and-limits',
        ...serivceConfig,
        pages: [
          {
            pageCode: 'XbarMSysmexLimit',
            isMenuItem: true,
            canWrite: true,
            pagePath: '/xbarm/xbarm-targets-and-limits/xbarm-sysmex-limit',
            ...serivceConfig,
            pages: [],
          },
          {
            pageCode: 'XbarMCustomerLimits',
            isMenuItem: true,
            canWrite: false,
            pagePath: '/xbarm/xbarm-targets-and-limits/xbarm-customer-limits',
            ...serivceConfig,
            pages: [],
          },
        ],
      },
      {
        pageCode: 'XbarMParameters',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/xbarm/xbarm-parameters',
        ...nullServiceConfig,
        pages: [],
      },
    ],
  },
  {
    pageCode: 'Service',
    isMenuItem: true,
    canWrite: true,
    pagePath: '/service',
    ...nullServiceConfig,
    pages: [
      {
        pageCode: 'ServiceMode',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/service/service-mode',
        ...nullServiceConfig,
        pages: [],
      },
      {
        pageCode: 'ServiceReset',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/service/service-reset',
        ...nullServiceConfig,
        pages: [],
      },
      {
        pageCode: 'LogicReset',
        isMenuItem: true,
        canWrite: true,
        pagePath: '/service/logic-reset',
        ...nullServiceConfig,
        pages: [],
      },
    ],
  },
  {
    pageCode: 'Resources',
    isMenuItem: true,
    canWrite: false,
    pagePath: '/resources',
    ...serivceConfig,
    pages: [],
  },
];
