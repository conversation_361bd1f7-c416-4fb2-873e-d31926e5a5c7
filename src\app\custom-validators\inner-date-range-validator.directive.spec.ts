import { FormControl } from '@angular/forms';
import { InnerDateRangeValidatorDirective } from './inner-date-range-validator.directive';

describe('InnerDateRangeValidatorDirective', () => {
  let directive: InnerDateRangeValidatorDirective;

  beforeEach(() => {
    directive = new InnerDateRangeValidatorDirective();
  });

  it('should return { required: true } if the control value is empty', () => {
    const control = new FormControl('');
    const result = directive.validate(control);


    expect(result).toEqual({ required: true });
  });

  it('should return null if startToEndRange is not defined', () => {
    directive.startToEndRange = undefined;

    const control = new FormControl({
      start: '2023-01-01',
      end: '2023-01-31',
    });
    const result = directive.validate(control);


    expect(result).toBeNull();
  });

  it('should return null if the date range is within the specified range', () => {
    directive.startToEndRange = 1;
    directive.startToEndUnit = 'months';

    const control = new FormControl({
      start: '2023-01-01',
      end: '2023-01-31',
    });
    const result = directive.validate(control);


    expect(result).toBeNull();
  });

  it('should return { invalidInnerRange: true } if the date range exceeds the specified range', () => {
    directive.startToEndRange = 1;
    directive.startToEndUnit = 'months';

    const control = new FormControl({
      start: '2023-01-01',
      end: '2023-03-01',
    });
    const result = directive.validate(control);


    expect(result).toEqual({ invalidInnerRange: true });
  });

  it('should handle different units of time correctly', () => {
    directive.startToEndRange = 7;
    directive.startToEndUnit = 'days';

    const control = new FormControl({
      start: '2023-01-01',
      end: '2023-01-08',
    });
    const result = directive.validate(control);


    expect(result).toBeNull();

    const controlExceeding = new FormControl({
      start: '2023-01-01',
      end: '2023-01-09',
    });
    const resultExceeding = directive.validate(controlExceeding);


    expect(resultExceeding).toEqual({ invalidInnerRange: true });
  });
});
