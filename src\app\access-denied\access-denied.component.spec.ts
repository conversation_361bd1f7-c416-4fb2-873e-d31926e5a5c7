import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AccessDeniedComponent } from './access-denied.component';
import { SecurityService } from '../services/security.service';

class MockSecurityService {
  async logout() {
    // mock logout implement
  }
}

describe('AccessDeniedComponent', () => {
  let component: AccessDeniedComponent;
  let fixture: ComponentFixture<AccessDeniedComponent>;
  let securityService: SecurityService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AccessDeniedComponent],
      providers: [
        {
          provide: SecurityService,
          useClass: MockSecurityService,
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AccessDeniedComponent);
    component = fixture.componentInstance;
    securityService = TestBed.inject(SecurityService);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call logout on backToLogin', async () => {
    const logoutSpy = jest.spyOn(securityService, 'logout');

    await component.backToLogin();

    expect(logoutSpy).toHaveBeenCalled();
  });
});
