import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';

import { Directive } from '@angular/core';
import moment from 'moment';

@Directive({
  selector: '[appCurrentMonthValidatorDirective]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: CurrentMonthValidatorDirective,
      multi: true,
    },
  ],
  standalone: true,
})
export class CurrentMonthValidatorDirective implements Validator {

  validate(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return { required: true }
    }

    const { start, end } = control.value;

    const startMoment = moment(start);

    const endMoment = moment(end);

    const currentMonthStart = moment().startOf('month');

    const currentMonthEnd = moment().endOf('month');

    const isDateRangeInCurrentMonth =
      startMoment.isSameOrAfter(currentMonthStart) &&
      endMoment.isSameOrBefore(currentMonthEnd);

    const isFullMonthSelection =
      startMoment.isSame(startMoment.clone().startOf('month')) &&
      endMoment.isSame(startMoment.clone().endOf('month'));

    if (isDateRangeInCurrentMonth && !isFullMonthSelection) {
      return {
        isDateRangeInCurrentMonth: true,
      }
    }

    return null;
  }

}
