import { NgModel } from '@angular/forms';

export class FormComponent {
  public showErrors(control: <PERSON><PERSON><PERSON><PERSON>, fieldName: string): string | undefined {
    if ((control.dirty ?? control.touched) && control.errors) {
      if (control.errors['required']) {
        return `${fieldName} is required`;
      } else if (control.errors['minlength']) {
        return `${fieldName} must be at least ${control.errors?.['minlength'].requiredLength} characters`;
      }
    }

    return undefined;
  }
}
