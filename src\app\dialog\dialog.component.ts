import { Component, Inject, TemplateRef } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

import { AclService } from '../services/acl.service';
import { EMPTY } from '../constant';

export interface DialogData {
  title: string;
  content: TemplateRef<unknown> | null;
  showCloseIcon?: boolean;
  isDisableClose?: boolean;
  headerClass?: string;
  bodyClass?: string;
  align?: 'start' | 'center' | 'end';
  onCloseIconClick?: (() => void);
  actions: {
    label: string;
    code?: string;
    close?: boolean;
    color: string;
    disable: boolean;
    skipPermission?: boolean;
    class?: string;
    className?: string;
    onClick: (() => void) | undefined;
  }[];
}

@Component({
  selector: 'app-dialog',
  templateUrl: './dialog.component.html',
  styleUrl: './dialog.component.scss',
})
export class DialogComponent {
  public pageCode = EMPTY;
  public parentPageCode = EMPTY;
  public messages = {
    permission: {
      parentCode: EMPTY,
      pageCode: EMPTY,
    },
  };

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    private readonly aclService: AclService,
  ) {
    this.pageCode = this.aclService.pageCode;
    this.parentPageCode = this.aclService.parentCode;
    this.messages = {
      permission: {
        parentCode: this.parentPageCode,
        pageCode: this.pageCode,
      },
    };
  }
}
