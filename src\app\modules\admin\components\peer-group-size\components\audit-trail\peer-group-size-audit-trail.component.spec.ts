import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogService } from 'src/app/services/dialog.service';
import { PeerGroupSizeAuditTrailComponent } from './peer-group-size-audit-trail.component';
import { RuleConfigurationService } from 'src/app/modules/admin/services/rule-configuration.service';

describe('PeerGroupSizeAuditTrailComponent', () => {
  let component: PeerGroupSizeAuditTrailComponent;
  let fixture: ComponentFixture<PeerGroupSizeAuditTrailComponent>;

  const mockRuleConfigurationService = {
    getRuleConfigurationAuditTrailLog: jest.fn(),
  };

  const mockDialogService = {
    openDialog$: { next: jest.fn() },
    dialogRef: { afterClosed: jest.fn() },
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        {
          provide: RuleConfigurationService,
          useValue: mockRuleConfigurationService,
        },
        {
          provide: DialogService,
          useValue: mockDialogService,
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PeerGroupSizeAuditTrailComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
