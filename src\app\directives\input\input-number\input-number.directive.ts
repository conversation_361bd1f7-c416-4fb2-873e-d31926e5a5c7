import { Directive, HostListener, Input } from '@angular/core';

import { NgControl } from '@angular/forms';

import { EMPTY, KEY_CODE, NUMBER_INPUT_PATTERN } from 'src/app/constant';

@Directive({
  selector: '[appNumberInput]',
})
export class NumberInputDirective {
  @Input() decimals = false;

  @Input() negative = false;

  private getPattern(type: 'allowNegative' | 'validate'): RegExp | string {
    switch (type) {
      case 'allowNegative':
        return this.decimals
          ? NUMBER_INPUT_PATTERN.ALLOW_NEGATIVE_WITH_DECIMALS
          : NUMBER_INPUT_PATTERN.ALLOW_NEGATIVE_NO_DECIMALS;

      case 'validate':
        return this.decimals ? NUMBER_INPUT_PATTERN.VALIDATE_WITH_DECIMALS : NUMBER_INPUT_PATTERN.VALIDATE_NO_DECIMALS;

      default:
        throw new Error('Invalid pattern type');
    }
  }

  checkAllowNegative(value: string) {
    return String(value).match(this.getPattern('allowNegative'));
  }

  validateNumber(value: string) {
    return String(value).match(this.getPattern('validate'));
  }

  parseNumber(value: string): string {
    value = value ? String(value) : EMPTY;

    let parsedValue = value.replace(NUMBER_INPUT_PATTERN.PRECEDES_DIGITS, EMPTY);

    if (parsedValue.startsWith('.')) {
      parsedValue = '0' + parsedValue;
    }

    parsedValue = parsedValue.replace(NUMBER_INPUT_PATTERN.TRAILING_PERIOD, EMPTY);

    return parsedValue;
  }

  validateValidInput(oldValue: string) {
    const currentValue: string = this.el.value;

    const allowNegative = this.negative;

    if (allowNegative) {
      if (![EMPTY, '-'].includes(currentValue) && !this.checkAllowNegative(currentValue)) {
        this.el.control?.patchValue(oldValue);
      }
    } else if (currentValue !== EMPTY && !this.validateNumber(currentValue)) {
      this.el.control?.patchValue(oldValue);
    }
  }

  private readonly el: NgControl;

  constructor(private readonly ngControl: NgControl) {
    this.el = ngControl;
  }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    if (this.isAllowedKey(event) || this.isSpecialKeyCombination(event) || this.isDecimalOrNegativeAllowed(event)) {
      return;
    }

    if (this.isInvalidKey(event)) {
      event.preventDefault();
    }
  }

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent) {
    event.preventDefault();

    const pastedInput: string = event?.clipboardData ? event.clipboardData.getData('text/plain') : EMPTY;

    let formattedPastedValue = pastedInput.replace(NUMBER_INPUT_PATTERN.NON_NUMERIC_PERIOD_HYPHEN, EMPTY);

    if (!this.decimals) {
      formattedPastedValue = formattedPastedValue.replace(NUMBER_INPUT_PATTERN.DECIMAL, EMPTY);
    }

    if (!this.negative) {
      formattedPastedValue = formattedPastedValue.replace(NUMBER_INPUT_PATTERN.NEGATIVE, EMPTY);
    }

    const oldValue = this.el.value;

    this.el.control?.patchValue(formattedPastedValue);

    this.validateValidInput(oldValue);
  }

  @HostListener('focusout', ['$event'])
  onFocusOut() {
    this.handleValueChange();
  }

  private handleValueChange() {
    const currentValue: string = this.el.value;

    const allowNegative = this.negative;

    if (allowNegative) {
      const sign = currentValue.startsWith('-') ? '-' : EMPTY;

      const parsedValue = this.parseNumber(currentValue.replace(NUMBER_INPUT_PATTERN.NEGATIVE, EMPTY));

      this.el.control?.patchValue(sign + parsedValue);
    } else {
      this.el.control?.patchValue(this.parseNumber(currentValue));
    }
  }

  private isAllowedKey(event: KeyboardEvent): boolean {
    const allowedKeys = [
      KEY_CODE.KEY_DELETE,
      KEY_CODE.KEY_BACKSPACE,
      KEY_CODE.KEY_TAB,
      KEY_CODE.KEY_ESCAPE,
      KEY_CODE.KEY_ENTER,
    ];

    return allowedKeys.indexOf(event.key) !== -1;
  }

  private isSpecialKeyCombination(event: KeyboardEvent): boolean {
    // Control key combinations for A, C, V, X (select all, copy, paste, cut)
    return (
      (event.ctrlKey &&
        (event.key === KEY_CODE.KEY_A ||
          event.key === KEY_CODE.KEY_C ||
          event.key === KEY_CODE.KEY_V ||
          event.key === KEY_CODE.KEY_X)) ||
      (event.key >= KEY_CODE.KEY_HOME && event.key <= KEY_CODE.KEY_RIGHT)
    );
  }

  private isDecimalOrNegativeAllowed(event: KeyboardEvent): boolean {
    return (
      (event.key === '.' && this.decimals && !this.el.value.includes('.')) ||
      (event.key === '-' && this.negative && this.el.value === EMPTY)
    );
  }

  private isInvalidKey(event: KeyboardEvent): boolean {
    return (
      (event.shiftKey || event.key < KEY_CODE.KEY_NUM_0 || event.key > KEY_CODE.KEY_NUM_9) &&
      (event.key < KEY_CODE.KEY_NUMPAD_0 || event.key > KEY_CODE.KEY_NUMPAD_9)
    );
  }
}
