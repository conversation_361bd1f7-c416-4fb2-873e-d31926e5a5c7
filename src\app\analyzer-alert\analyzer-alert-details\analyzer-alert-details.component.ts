import { Component, Input, OnChanges, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MatDialogConfig } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { map } from 'rxjs';

import { AlertAnalyzerStatusContent, AnalyzerAlertStatusDialog } from '../analyzer-alert.component';
import { DialogType, SORT_DIRECTION } from '../../enums';
import { AclModule } from '../../directives/acl/acl.module';
import { AnalyzerResponse } from '../../modules/analyzer-status/interfaces/analyzer';
import { AnalyzerService } from '../../modules/analyzer-status/services/analyzer.service';
import { AnalyzerStatusTileComponent }
  from '../../modules/analyzer-status/components/analyzer-status-tile/analyzer-status-tile.component';
import { DialogService } from '../../services/dialog.service';
import { EMPTY } from '../../constant';
import { MatIcon } from '@angular/material/icon';
import { NotificationDisplaySettingsParams } from 'src/app/modules/settings/interfaces/site-setting';
import { ReceiveMessage } from 'src/app/interfaces/receiveMessage';
import { Utils } from '../../helpers/UtilFunctions';

const COLOR_ORDER: Record<string, number> = {
  red: 0,
  yellow: 1,
  green: 2,
  gray: 3,
};

@Component({
  selector: 'app-analyzer-alert-details',
  standalone: true,
  imports: [AclModule, AnalyzerStatusTileComponent, MatIcon, MatButton],
  templateUrl: './analyzer-alert-details.component.html',
})
export class AnalyzerAlertDetailsComponent implements OnChanges {
  @Input() openDialog!: { type: number; data: AnalyzerAlertStatusDialog } | null;
  @Input() analyzerAlertStatus: ReceiveMessage<AlertAnalyzerStatusContent>[] = [];
  @ViewChild('analyzerAlertDialog') analyzerAlertDialogContent: TemplateRef<unknown> | undefined;

  currentSiteCode!: string;
  analyzers: AnalyzerResponse[] = [];
  dialogConfig!: MatDialogConfig;
  alertPreferences!: NotificationDisplaySettingsParams;

  constructor(
    private readonly dialogService: DialogService,
    private readonly analyzerService: AnalyzerService,
    private readonly router: Router,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['openDialog']?.currentValue) {
      const { type, data } = changes['openDialog'].currentValue;

      if (!(type in DialogType)) {
        return;
      }

      if (data) {
        const { siteCode, alertPreferences } = data;

        this.currentSiteCode = siteCode;

        if (alertPreferences) {
          this.alertPreferences = alertPreferences;
        }

        this.getAnalyzerStatus(this.currentSiteCode);
      }
    }

    if (changes['analyzerAlertStatus']?.currentValue && this.analyzers.length) {
      this.updateAnalyzersStatus();
    }
  }

  getAnalyzerStatus(siteCode: string) {
    this.analyzerService
      .getAnalyzers(siteCode)
      .pipe(
        map((siteData) => {
          const sites = siteData;

          return sites.parentAnalyzers.reduce(
            (acc, parent) => {
              return acc.concat(parent.analyzers ?? []);
            },
            [...sites.aloneAnalyzers],
          );
        }),
        map((analyzers) => this.sortAlertAnalyzers(analyzers)), // Sort the analyzers
      )
      .subscribe({
        next: (sortedAnalyzers) => {
          this.analyzers = sortedAnalyzers;

          this.openAnalyzerAlertDialog();
        },
      });
  }

  sortAlertAnalyzers(analyzers: AnalyzerResponse[]) {
    const sortedByNameOrSerial = Utils.sort(analyzers, [
      {
        prop: 'nickname',
        direction: SORT_DIRECTION.SORT_DESCENDING,
      },
      {
        prop: 'serialNumber',
        direction: SORT_DIRECTION.SORT_DESCENDING,
      },
    ]);

    const sortedAnalyzerStatus = [...sortedByNameOrSerial].sort((current, next) => {
      // tileConfig.color is in the format of 'red-20', 'yellow-20', 'green-20', 'gray-20'
      // so we need to extract the color part to compare
      const colorOfCurrent = this.getColorOfAnalyzer(current);
      const colorOfNext = this.getColorOfAnalyzer(next);

      return COLOR_ORDER[colorOfCurrent] - COLOR_ORDER[colorOfNext];
    });

    return this.filterAnalyzerAlertByStatusPreference(sortedAnalyzerStatus);
  }

  getColorOfAnalyzer(analyzer: AnalyzerResponse) {
    return analyzer.tileConfig?.color?.substring(0, analyzer.tileConfig?.color.indexOf('-')) ?? EMPTY;
  }

  filterAnalyzerAlertByStatusPreference(analyzers: AnalyzerResponse[]) {
    if (!this.alertPreferences) {
      return analyzers;
    }

    const colorsToFilter: string[] = [];

    if (!this.alertPreferences.isGreen) {
      colorsToFilter.push('green');
    }

    if (!this.alertPreferences.isYellow) {
      colorsToFilter.push('yellow');
    }

    if (!this.alertPreferences.isRed) {
      colorsToFilter.push('red');
    }

    if (!this.alertPreferences.isGray) {
      colorsToFilter.push('gray');
    }

    return analyzers.filter((analyzer) => {
      return !colorsToFilter.includes(this.getColorOfAnalyzer(analyzer));
    });
  }

  navigateToDetail(modelCode: string, serialNumber: string) {
    const targetPath = '/analyzer-status/analyzer-detail';
    const currentUrl = this.router.url;

    if (currentUrl.startsWith(targetPath)) {
      this.router
        .navigateByUrl('/', { skipLocationChange: true })
        .then(() => this.router.navigate([targetPath, modelCode, serialNumber]));
    } else {
      this.router.navigate([targetPath, modelCode, serialNumber]).then();
    }

    this.closeDialog();
  }

  openAnalyzerAlertDialog() {
    this.dialogConfig = {
      panelClass: 'analyzer-alert-dialog',
      disableClose: true,
      autoFocus: false,
      data: {
        title: $localize`:app-app_dialog-title:Analyzer Status Alert`,
        content: this.analyzerAlertDialogContent,
        showCloseIcon: true,
        actions: [
          {
            label: $localize`:@@app-app_button-close:Close`,
            skipPermission: true,
            onClick: () => this.closeDialog(),
          },
        ],
      },
    };

    this.dialogService.openDialog$.next(this.dialogConfig);
  }

  closeDialog() {
    this.dialogService.closeDialog$.next(undefined);
  }

  updateAnalyzersStatus() {
    this.analyzers.forEach((targetItem, index) => {
      const matchingSource = this.analyzerAlertStatus.find(
        (sourceItem) =>
          sourceItem.content.analyzerModel === targetItem.modelCode &&
          sourceItem.content.analyzerSerial === targetItem.serialNumber,
      );

      if (matchingSource) {
        const { content } = matchingSource;

        targetItem.analyzerStatus = {
          ...targetItem.analyzerStatus,
          isIotConnected: content.isIotConnected !== false,
          status: content.statusCode,
          statusBf: content.statusCodeBf,
          approachingTime: content.approachingTime,
          approachingTimeBf: content.approachingTimeBf,
          qcDue: content.qcDue,
          qcDueBf: content.qcDueBf,
          isBfWarning: content.isBfWarning,
          isCheckXbarmData: content.isCheckXbarmData,
          enableTroubleshootingTime: content.enableTroubleshootingTime,
          isWarningAssayLimit: !!content.isWarningAssayLimit,
          isWarningCountryLimit: !!content.isWarningCountryLimit,
          isWarningCustomerLimit: !!content.isWarningCustomerLimit,
          isWarningMultiRulesLimit: !!content.isWarningMultiRulesLimit,
          isWarningAssayLimitBf: !!content.isWarningAssayLimitBf,
          isWarningCountryLimitBf: !!content.isWarningCountryLimitBf,
          isWarningCustomerLimitBf: !!content.isWarningCustomerLimitBf,
          isWarningMultiRulesLimitBf: !!content.isWarningMultiRulesLimitBf,
          hasBfRun: !!content.hasBfRun,
        };

        this.analyzers[index] = {
          ...this.analyzerService.analyzerWithTileConf(targetItem),
        };
      }
    });
  }
}
