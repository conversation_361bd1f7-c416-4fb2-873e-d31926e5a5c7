@if (!termsAndConditions) {
  <div class="app-splash-screen" [ngStyle]="{ left: windowWidth }" *ngIf="showSplash">
    <div class="app-splash-inner">
      <div class="app-logo"></div>
      <div class="app-loader"></div>
    </div>
  </div>
  <div><router-outlet></router-outlet></div>
}

<ng-template #dialogContent>
  @if (termsAndConditions) {
    <div class="border-solid border rounded-xl border-[var(--grey-100)]">
      <div
        class="h-[385px] overflow-y-auto p-2 text-black mx-2"
        [innerHTML]="termsAndConditions | safeHtml"
        #termsConditionsContent
      ></div>
    </div>
  }
</ng-template>
