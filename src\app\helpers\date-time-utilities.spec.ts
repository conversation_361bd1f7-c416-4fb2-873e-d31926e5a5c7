import moment from 'moment';

import { DateTimeUtilities } from './date-time-utilities';
import { EMPTY } from '../constant';

describe('DateTimeUtilities', () => {
  describe('formatDate', () => {
    it('should format date correctly with default format', () => {
      const dateString = '2024-10-11T10:00:40.835629';
      const result = DateTimeUtilities.formatDate(dateString);

      expect(result).toBe('10/11/2024 10:00:40');
    });

    it('should format date correctly with custom format', () => {
      const dateString = '2024-10-11T10:00:40.835629';
      const result = DateTimeUtilities.formatDate(dateString, 'YYYY-MM-DD');

      expect(result).toBe('2024-10-11');
    });

    it('should return "Invalid Date" for invalid date string', () => {
      const invalidDateString = 'invalid-date';
      const result = DateTimeUtilities.formatDate(invalidDateString);

      expect(result).toBe('Invalid Date');
    });
  });

  describe('addDays', () => {
    it('should correctly add a positive number of days to a given date', () => {
      const date = new Date('2023-01-01');
      const numberOfDays = 5;
      const expectedDate = new Date('2023-01-06');

      const result = DateTimeUtilities.addDays(date, numberOfDays);

      expect(result).toEqual(expectedDate);
    });

    it('should correctly add a negative number of days to a given date', () => {
      const date = new Date('2023-01-01');
      const numberOfDays = -5;
      const expectedDate = new Date('2022-12-27');

      const result = DateTimeUtilities.addDays(date, numberOfDays);

      expect(result).toEqual(expectedDate);
    });

    it('should correctly add zero days to a given date', () => {
      const date = new Date('2023-01-01');
      const numberOfDays = 0;
      const expectedDate = new Date('2023-01-01');

      const result = DateTimeUtilities.addDays(date, numberOfDays);

      expect(result).toEqual(expectedDate);
    });
  });

  describe('addMonths', () => {
    it('should add 2 months to the date', () => {
      const date = new Date('2024-10-11T10:00:40.835629');
      const result = DateTimeUtilities.addMonths(date, 2);

      expect(moment(result).format('YYYY-MM-DD')).toBe('2024-12-11');
    });

    it('should subtract 1 month from the date', () => {
      const date = new Date('2024-10-11T10:00:40.835629');
      const result = DateTimeUtilities.addMonths(date, -1);

      expect(moment(result).format('YYYY-MM-DD')).toBe('2024-09-11');
    });

    it('should handle leap years correctly', () => {
      const date = new Date('2024-02-29T10:00:40.835629');

      const result = DateTimeUtilities.addMonths(date, 12);

      expect(moment(result).format('YYYY-MM-DD')).toBe('2025-02-28');
    });
  });

  describe('getDateWithTimezone', () => {
    it('should return the date with timezone offset', () => {
      const date = new Date('2024-10-11T10:00:40.835629');
      const formattedDate = moment(date).format('YYYY-MM-DDTHH:mm:ss');
      const timezoneOffset = DateTimeUtilities.getLocalTimezoneOffset();

      const result = DateTimeUtilities.getDateWithTimezone(date);

      expect(result).toBe(`${formattedDate}${timezoneOffset}`);
    });

    it('should handle different timezones correctly', () => {
      const date = new Date('2024-10-11T10:00:40.835629');
      const formattedDate = moment(date).format('YYYY-MM-DDTHH:mm:ss');
      const timezoneOffset = DateTimeUtilities.getLocalTimezoneOffset();

      const result = DateTimeUtilities.getDateWithTimezone(date);

      expect(result).toContain(formattedDate);
      expect(result).toContain(timezoneOffset);
    });

    it('should handle invalid date', () => {
      const invalidDate = new Date('invalid-date');
      const formattedDate = moment(invalidDate).format('YYYY-MM-DDTHH:mm:ss');
      const timezoneOffset = DateTimeUtilities.getLocalTimezoneOffset();

      const result = DateTimeUtilities.getDateWithTimezone(invalidDate);

      expect(result).toBe(`${formattedDate}${timezoneOffset}`);
    });
  });

  describe('getLocalTimezoneOffset', () => {
    it('should return the correct timezone offset for positive offset', () => {
      jest.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-120); // UTC+2

      const result = DateTimeUtilities.getLocalTimezoneOffset();

      expect(result).toBe('+02:00');
    });

    it('should return the correct timezone offset for negative offset', () => {
      jest.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(300); // UTC-5

      const result = DateTimeUtilities.getLocalTimezoneOffset();

      expect(result).toBe('-05:00');
    });

    it('should return the correct timezone offset for zero offset', () => {
      jest.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(0); // UTC

      const result = DateTimeUtilities.getLocalTimezoneOffset();

      expect(result).toBe('+00:00');
    });

    it('should handle single digit hours and minutes correctly', () => {
      jest.spyOn(Date.prototype, 'getTimezoneOffset').mockReturnValue(-90); // UTC+1:30

      const result = DateTimeUtilities.getLocalTimezoneOffset();

      expect(result).toBe('+01:30');
    });
  });

  describe('addYears', () => {
    it('should add 2 years to the date', () => {
      const date = new Date('2024-10-11T10:00:40.835629');
      const result = DateTimeUtilities.addYears(date, 2);

      expect(moment(result).format('YYYY-MM-DD')).toBe('2026-10-11');
    });

    it('should subtract 1 year from the date', () => {
      const date = new Date('2024-10-11T10:00:40.835629');
      const result = DateTimeUtilities.addYears(date, -1);

      expect(moment(result).format('YYYY-MM-DD')).toBe('2023-10-11');
    });

    it('should handle leap years correctly', () => {
      const date = new Date('2024-02-29T10:00:40.835629');
      const result = DateTimeUtilities.addYears(date, 1);

      expect(moment(result).format('YYYY-MM-DD')).toBe('2025-02-28');
    });
  });

  describe('convertDateRangeToUTC', () => {
    it('should convert date range to UTC', () => {
      const startDateString = '2024-10-11';
      const endDateString = '2024-10-12';

      const result = DateTimeUtilities.convertDateRangeToUTC(startDateString, endDateString);

      const startDate = new Date(startDateString);
      const endDate = new Date(endDateString);

      const expectedStartDate = new Date(
        startDate.getFullYear(),
        startDate.getMonth(),
        startDate.getDate(),
      ).toISOString();

      const expectedEndDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate() + 1).toISOString();

      expect(result.startDate).toBe(expectedStartDate);
      expect(result.endDate).toBe(expectedEndDate);
    });

    it('should return empty strings for invalid date range', () => {
      const startDateString = 'invalid-date';
      const endDateString = 'invalid-date';
      const result = DateTimeUtilities.convertDateRangeToUTC(startDateString, endDateString);

      expect(result.startDate).toBe(EMPTY);
      expect(result.endDate).toBe(EMPTY);
    });
  });

  describe('convertStartDateToUTC', () => {
    it('should convert start date to UTC', () => {
      const startDateString = '2024-10-11';
      const result = DateTimeUtilities.convertStartDateToUTC(startDateString);

      const startDate = new Date(startDateString);

      const expectedStartDate = new Date(
        startDate.getFullYear(),
        startDate.getMonth(),
        startDate.getDate(),
      ).toISOString();

      expect(result).toBe(expectedStartDate);
    });

    it('should return empty string for invalid start date', () => {
      const startDateString = 'invalid-date';
      const result = DateTimeUtilities.convertStartDateToUTC(startDateString);

      expect(result).toBe(EMPTY);
    });
  });

  describe('convertEndDateToUTC', () => {
    it('should convert end date to be next date in UTC', () => {
      const endDateString = '2024-10-12';
      const result = DateTimeUtilities.convertEndDateToUTC(endDateString);

      const endDate = new Date(endDateString);

      const expectedEndDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate() + 1).toISOString();

      expect(result).toBe(expectedEndDate);
    });

    it('should convert end date to be next date in UTC', () => {
      const endDateString = '2024-05-01';
      const result = DateTimeUtilities.convertEndDateToUTC(endDateString);

      const endDate = new Date(endDateString);
      const expectedEndDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate() + 1).toISOString();

      expect(result).toBe(expectedEndDate);
    });

    it('should return empty string for invalid end date', () => {
      const endDateString = 'invalid-date';
      const result = DateTimeUtilities.convertEndDateToUTC(endDateString);

      expect(result).toBe(EMPTY);
    });
  });

  describe('convertUTCToSpecificTimezone', () => {
    it('should convert UTC date string to specific positive timezone offset', () => {
      const utcDateString = '2024-10-11T10:00:00Z';
      const timezoneOffset = '+02:00';
      const result = DateTimeUtilities.convertUTCToSpecificTimezone(utcDateString, timezoneOffset);

      expect(result).toBe('2024-10-11T12:00:00');
    });

    it('should convert UTC date string to specific negative timezone offset', () => {
      const utcDateString = '2024-10-11T10:00:00Z';
      const timezoneOffset = '-05:00';
      const result = DateTimeUtilities.convertUTCToSpecificTimezone(utcDateString, timezoneOffset);

      expect(result).toBe('2024-10-11T05:00:00');
    });

    it('should handle UTC date string without Z', () => {
      const utcDateString = '2024-10-11T10:00:00';
      const timezoneOffset = '+03:00';
      const result = DateTimeUtilities.convertUTCToSpecificTimezone(utcDateString, timezoneOffset);

      expect(result).toBe('2024-10-11T13:00:00');
    });

    it('should handle timezoneOffset with UTC prefix', () => {
      const utcDateString = '2024-10-11T10:00:00Z';
      const timezoneOffset = 'UTC+01:00';
      const result = DateTimeUtilities.convertUTCToSpecificTimezone(utcDateString, timezoneOffset);

      expect(result).toBe('2024-10-11T11:00:00');
    });

    it('should return input if utcDateString is empty', () => {
      const utcDateString = '';
      const timezoneOffset = '+02:00';
      const result = DateTimeUtilities.convertUTCToSpecificTimezone(utcDateString, timezoneOffset);

      expect(result).toBe('');
    });

    it('should return input if timezoneOffset is empty', () => {
      const utcDateString = '2024-10-11T10:00:00Z';
      const timezoneOffset = '';
      const result = DateTimeUtilities.convertUTCToSpecificTimezone(utcDateString, timezoneOffset);

      expect(result).toBe(utcDateString);
    });

    it('should handle invalid utcDateString gracefully', () => {
      const utcDateString = 'invalid-date';
      const timezoneOffset = '+02:00';
      const result = DateTimeUtilities.convertUTCToSpecificTimezone(utcDateString, timezoneOffset);

      // moment returns 'Invalid date' string for invalid input
      expect(result).toBe('Invalid date');
    });
  });
});
