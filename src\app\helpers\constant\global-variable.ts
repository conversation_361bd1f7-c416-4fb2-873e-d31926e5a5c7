import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { CommonSystem } from 'src/app/interfaces/common-system';

import { ILanguage, en } from 'src/app/i18n/languages';
import { AccessibleSiteRes } from 'src/app/modules/settings/interfaces/site-setting';
import { EventParam } from 'src/app/models/event-items';
import { PageAcl } from 'src/app/interfaces/user';

export class GlobalVariables {
  static readonly eventSource: Subject<EventParam> = new Subject<EventParam>();
  static readonly events$: Observable<EventParam> = GlobalVariables.eventSource.asObservable();
  static site: AccessibleSiteRes[];
  static pages: PageAcl[];
  private static _siteByPageCode: AccessibleSiteRes[] = [];
  private static _languageFormat: ILanguage = en;

  static get siteByPageCode(): AccessibleSiteRes[] {
    return this._siteByPageCode;
  }

  static get languageFormat(): ILanguage {
    return this._languageFormat;
  }

  static fireEvent(param: EventParam) {
    this.eventSource.next(param);
  }

  static readonly commonSystems: BehaviorSubject<Record<string, CommonSystem>> = new BehaviorSubject<
    Record<string, CommonSystem>
  >({});
  static readonly commonSystems$: Observable<Record<string, CommonSystem>> =
    GlobalVariables.commonSystems.asObservable();

  static setCommonSystems(commonSystem: CommonSystem[]): void {
    this.commonSystems.next(this.transformCommonSystems(commonSystem));
  }

  static getCommonSystems(key: string): CommonSystem | undefined {
    let result: CommonSystem | undefined;

    this.commonSystems$
      .subscribe((systems) => {
        result = systems[key];
      })
      .unsubscribe();

    return result;
  }

  static setPages(pages: PageAcl[]): void {
    this.pages = pages;
  }

  static setSiteByPageCode(sites: AccessibleSiteRes[]): void {
    this._siteByPageCode = sites;
  }

  static setLanguageFormat(languageFormat: ILanguage): void {
    this._languageFormat = languageFormat;
  }

  private static transformCommonSystems(commonSystem: CommonSystem[]): Record<string, CommonSystem> {
    return commonSystem.reduce((systemConfig: Record<string, CommonSystem>, data) => {
      systemConfig[data.name] = data;

      return systemConfig;
    }, {});
  }
}
