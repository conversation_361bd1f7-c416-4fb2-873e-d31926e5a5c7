import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { Subject, of, throwError } from 'rxjs';

import { ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { AclModule } from 'src/app/directives/acl/acl.module';
import { CreateServiceTicketComponent } from './create-service-ticket.component';
import { CreateServiceTicketService } from '../../services/create-service-ticket.service';
import { DialogService } from 'src/app/services/dialog.service';
import { RuleConfigurationService } from '../../services/rule-configuration.service';
import { SnackBarService } from 'src/app/services/snackbar.service';
import { UserStoreService } from 'src/app/services/user-store.service';

class SnackBarServiceMock {
  displayMsg = jest.fn();
}

class CreateServiceTicketMock {
  getStandardRuleSettings = jest.fn();
}

interface DialogAction {
  label: string;
  onClick: () => void;
}

interface DialogData {
  actions: DialogAction[];
}

describe('CreateServiceTicketComponent', () => {
  let component: CreateServiceTicketComponent;

  let fixture: ComponentFixture<CreateServiceTicketComponent>;

  let createServiceTicketMock: jest.Mocked<CreateServiceTicketMock>;

  let snackBarServiceMock: jest.Mocked<SnackBarServiceMock>;

  const mockRuleConfigService = {
    updateStandardSettings: jest.fn().mockReturnValue(of({})),
  };

  let dialog: MatDialog;

  const mockUserStoreService = {};

  let dialogServiceOpenDialog$: Subject<MatDialogConfig>;

  const matDialogMock = {
    open: () => ({}),
    closeAll: jest.fn(),
  };

  const mockDialogService = {
    openDialog$: { next: jest.fn() },
    dialogRef: { afterClosed: jest.fn() },
  };

  beforeEach(async () => {
    const activatedRouteMock = {
      params: of({ id: '123' }),
      queryParams: of({ search: 'test' }),
      data: of({ pagecode: '123' }),
    };

    snackBarServiceMock = {
      displayMsg: jest.fn(),
    };

    createServiceTicketMock = {
      getStandardRuleSettings: jest.fn(),
    };

    dialogServiceOpenDialog$ = new Subject<MatDialogConfig>();

    await TestBed.configureTestingModule({
      providers: [
        {
          provide: RuleConfigurationService,
          useValue: mockRuleConfigService,
        },
        {
          provide: UserStoreService,
          useValue: mockUserStoreService,
        },
        {
          provide: ActivatedRoute,
          useValue: activatedRouteMock,
        },
        {
          provide: SnackBarService,
          useValue: snackBarServiceMock,
        },
        {
          provide: MatDialog,
          useValue: matDialogMock,
        },
        {
          provide: CreateServiceTicketService,
          useValue: createServiceTicketMock,
        },
        {
          provide: DialogService,
          useValue: mockDialogService,
        },
      ],
      imports: [NoopAnimationsModule, CreateServiceTicketComponent, HttpClientModule, CommonModule, AclModule],
    }).compileComponents();

    dialog = TestBed.inject(MatDialog);

    fixture = TestBed.createComponent(CreateServiceTicketComponent);

    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should close the dialog when "Cancel" is clicked', () => {
    mockDialogService.dialogRef = { afterClosed: jest.fn(() => of(undefined)) };

    jest.spyOn(mockDialogService.openDialog$, 'next');

    jest.spyOn(dialog, 'closeAll');

    component.openDialog('enable', true);

    const cancelButtonAction = mockDialogService.openDialog$.next.mock.calls[0][0].data.actions[0].onClick;

    cancelButtonAction();

    expect(dialog.closeAll).toHaveBeenCalled();
  });

  it('should call updateStandardSettings and close the dialog when clicking save button', () => {
    const settingKey = 'enable';

    const settingValue = true;

    mockDialogService.dialogRef = {
      afterClosed: jest.fn(() => of({})),
    };

    jest.spyOn(mockDialogService.openDialog$, 'next');

    jest.spyOn(dialog, 'closeAll');

    mockRuleConfigService.updateStandardSettings.mockReturnValue(of({}));

    component.openDialog(settingKey, settingValue);

    const saveButtonAction = mockDialogService.openDialog$.next.mock.calls[0][0].data.actions[1].onClick;

    saveButtonAction();

    expect(mockRuleConfigService.updateStandardSettings).toHaveBeenCalledWith('', settingKey, settingValue);
    expect(dialog.closeAll).toHaveBeenCalled();
  });

  it('should display error message if update unsuccessfully', () => {
    const settingKey = 'enable';

    const settingValue = true;

    const errorResponse = new Error('Test error');

    mockRuleConfigService.updateStandardSettings.mockReturnValue(throwError(() => errorResponse));

    jest.spyOn(snackBarServiceMock, 'displayMsg');
    jest.spyOn(component, 'revertBackupData');

    component.updateStandardSettings(settingKey, settingValue);

    expect(component.revertBackupData).toHaveBeenCalled();
  });

  it('should call closeAll on cancel action', () => {
    const closeAllSpy = jest.spyOn(dialog, 'closeAll');

    component.openDialog('enable', true);

    dialogServiceOpenDialog$.subscribe((dialogData: MatDialogConfig<DialogData>) => {
      const cancelAction = dialogData?.data?.actions!.find(
        (action: DialogAction) => action.label === $localize`:@@app-app_button-cancel:Cancel`,
      );

      if (cancelAction) {
        cancelAction.onClick();
      }

      expect(closeAllSpy).toHaveBeenCalled();
    });
  });

  it('should handle errors from updateStandardSettings', () => {
    const settingKey = 'enable';

    const settingValue = true;

    const errorResponse = new Error('Test error');

    jest.spyOn(mockRuleConfigService, 'updateStandardSettings').mockReturnValue(throwError(() => errorResponse));
    jest.spyOn(component, 'revertBackupData');

    component.openDialog(settingKey, settingValue);

    dialogServiceOpenDialog$.subscribe((dialogData) => {
      const saveAction = dialogData.data.actions.find(
        (action: DialogAction) => action.label === $localize`:@@app-app_button-save:Save`,
      );

      if (saveAction) {
        saveAction.onClick();
      }

      expect(component.revertBackupData).toHaveBeenCalled();
    });
  });

  it('should open dialog with the proper configuration', () => {
    const settingKey = 'enable';

    const settingValue = true;

    jest.spyOn(component, 'updateStandardSettings');

    jest.spyOn(component, 'backupData');

    component.openDialog(settingKey, settingValue);

    dialogServiceOpenDialog$.subscribe((dialogData) => {
      expect(dialogData.data.title).toBe($localize`:app-app_dialog-title:Save all data changes`);
      expect(dialogData.data.content).toBe(component.dialogContent);
      expect(dialogData.data.actions.length).toBe(2);

      const saveAction = dialogData.data.actions.find(
        (action: DialogAction) => action.label === $localize`:@@app-app_button-save:Save`,
      );

      expect(saveAction).toBeTruthy();
      expect(saveAction.color).toBe('primary');
      expect(saveAction.onClick).toBeDefined();

      const cancelAction = dialogData.data.actions.find(
        (action: DialogAction) => action.label === $localize`:@@app-app_button-cancel_changes:Cancel changes`,
      );

      expect(cancelAction).toBeTruthy();
      expect(cancelAction.color).toBe('ghost');
      expect(cancelAction.onClick).toBeDefined();
    });
  });

  it('should call updateStandardSettings ticket with correct parameters', () => {
    // 3rd party library class, must using mock object by casting type
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const event: MatSlideToggleChange = { checked: true } as MatSlideToggleChange;

    const openDialogSpy = jest.spyOn(component, 'openDialog').mockImplementation(() => {
      return;
    });

    component.onChangeEnable(event);

    expect(openDialogSpy).toHaveBeenCalledWith('enable', true);
  });

  it('should call updateStandardSettings enable with correct parameters', () => {
    // 3rd party library class, must using mock object by casting type
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const event: MatCheckboxChange = { checked: false } as MatCheckboxChange;

    const openDialogSpy = jest.spyOn(component, 'openDialog').mockImplementation(() => {
      return;
    });

    component.onChangeCreateServiceTicket(event);

    expect(openDialogSpy).toHaveBeenCalledWith('ticket', false);
  });

  it('should display success message and backup settings on successful update', () => {
    const settingKey = 'enable';

    const settingValue = true;

    mockRuleConfigService.updateStandardSettings.mockReturnValue(of(null));

    jest.spyOn(component, 'backupData');

    component.updateStandardSettings(settingKey, settingValue);

    expect(component.backupData).toHaveBeenCalled();
  });
});
