export const COMMON_SYSTEM_CONFIG_KEY = {
  MULTI_ANALYZER_MAX_SELECTED_ANALYZER_NUMBER: 'MULTI_ANALYZER_MAX_SELECTED_ANALYZER_NUMBER',
  XBARM_COMPARISON_MINUTE: 'XBARM_COMPARISON_MINUTE',
  SDI_CHART_STATUS_BOOLEAN: 'SDI_CHART_STATUS_BOOLEAN',
  BACKGROUND_COUNT_DEFAULT_DATE_RANGE_DAY: 'BACKGROUND_COUNT_DEFAULT_DATE_RANGE_DAY',
  BACKGROUND_COUNT_MAX_DATE_RANGE_MONTH: 'BACKGROUND_COUNT_MAX_DATE_RANGE_MONTH',
  CALIBRATION_MAX_DATE_RANGE_YEAR: 'CALIBRATION_MAX_DATE_RANGE_YEAR',
  CALIBRATION_DEFAULT_DATE_RANGE_MONTH: 'CALIBRATION_DEFAULT_DATE_RANGE_MONTH',
  BACKGROUND_COUNT_REPORT_IS_ENABLE: 'BACKGROUND_COUNT_REPORT_IS_ENABLE',
  REAGENT_CHANGE_HISTORY_REPORT_IS_ENABLE: 'REAGENT_CHANGE_HISTORY_REPORT_IS_ENABLE',
  REAGENT_COMPARISON_BETWEEN_LOTS_REPORT_IS_ENABLE: 'REAGENT_COMPARISON_BETWEEN_LOTS_REPORT_IS_ENABLE',
  UNCERTAINTY_OF_MEASUREMENT_REPORT_IS_ENABLE: 'UNCERTAINTY_OF_MEASUREMENT_REPORT_IS_ENABLE',
  PERIODICAL_REPORT_IS_ENABLE: 'PERIODICAL_REPORT_IS_ENABLE',
  MONTHLY_RILLIBAK_REPORT_IS_ENABLE: 'MONTHLY_RILLIBAK_REPORT_IS_ENABLE',
  HISTORICAL_REPORT_IS_ENABLE: 'HISTORICAL_REPORT_IS_ENABLE',
  TRACEABILITY_REPORT_IS_ENABLE: 'TRACEABILITY_REPORT_IS_ENABLE',
  LOT_TO_LOT_REPORT_IS_ENABLE: 'LOT_TO_LOT_REPORT_IS_ENABLE',
  PEER_GROUP_REPORT_IS_ENABLE: 'PEER_GROUP_REPORT_IS_ENABLE',
  PERIODICAL_IQC_REPORT_IS_ENABLE: 'PERIODICAL_IQC_REPORT_IS_ENABLE',
  CONTINUOUS_PERFORMANCE_VERIFICATION_REPORT_REPORT_IS_ENABLE:
    'CONTINUOUS_PERFORMANCE_VERIFICATION_REPORT_REPORT_IS_ENABLE',
  CALIBRATION_REPORT_IS_ENABLE: 'CALIBRATION_REPORT_IS_ENABLE',
  GROUP_CHART_REPORT_IS_ENABLE: 'GROUP_CHART_REPORT_IS_ENABLE',
  BACKGROUND_COUNT_REPORT_APPLIED_COUNTRY: 'BACKGROUND_COUNT_REPORT_APPLIED_COUNTRY',
  REAGENT_CHANGE_HISTORY_REPORT_APPLIED_COUNTRY: 'REAGENT_CHANGE_HISTORY_REPORT_APPLIED_COUNTRY',
  REAGENT_COMPARISON_BETWEEN_LOTS_REPORT_APPLIED_COUNTRY: 'REAGENT_COMPARISON_BETWEEN_LOTS_REPORT_APPLIED_COUNTRY',
  UNCERTAINTY_OF_MEASUREMENT_REPORT_APPLIED_COUNTRY: 'UNCERTAINTY_OF_MEASUREMENT_REPORT_APPLIED_COUNTRY',
  PERIODICAL_REPORT_APPLIED_COUNTRY: 'PERIODICAL_REPORT_APPLIED_COUNTRY',
  MONTHLY_RILLIBAK_REPORT_APPLIED_COUNTRY: 'MONTHLY_RILLIBAK_REPORT_APPLIED_COUNTRY',
  HISTORICAL_REPORT_APPLIED_COUNTRY: 'HISTORICAL_REPORT_APPLIED_COUNTRY',
  TRACEABILITY_COUNT_REPORT_APPLIED_COUNTRY: 'TRACEABILITY_COUNT_REPORT_APPLIED_COUNTRY',
  LOT_TO_LOT_REPORT_APPLIED_COUNTRY: 'LOT_TO_LOT_REPORT_APPLIED_COUNTRY',
  PEER_GROUP_REPORT_APPLIED_COUNTRY: 'PEER_GROUP_REPORT_APPLIED_COUNTRY',
  PERIODICAL_IQC_REPORT_APPLIED_COUNTRY: 'PERIODICAL_IQC_REPORT_APPLIED_COUNTRY',
  CONTINUOUS_PERFORMANCE_VERIFICATION_REPORT_REPORT_APPLIED_COUNTRY:
    'CONTINUOUS_PERFORMANCE_VERIFICATION_REPORT_REPORT_APPLIED_COUNTRY',
  CALIBRATION_REPORT_APPLIED_COUNTRY: 'CALIBRATION_REPORT_APPLIED_COUNTRY',
  GROUP_CHART_REPORT_APPLIED_COUNTRY: 'GROUP_CHART_REPORT_APPLIED_COUNTRY',
  BACKGROUND_COUNT_REPORT_APPLIED_LEVEL: 'BACKGROUND_COUNT_REPORT_APPLIED_LEVEL',
  REAGENT_CHANGE_HISTORY_REPORT_APPLIED_LEVEL: 'REAGENT_CHANGE_HISTORY_REPORT_APPLIED_LEVEL',
  REAGENT_COMPARISON_BETWEEN_LOTS_REPORT_APPLIED_LEVEL: 'REAGENT_COMPARISON_BETWEEN_LOTS_REPORT_APPLIED_LEVEL',
  UNCERTAINTY_OF_MEASUREMENT_REPORT_APPLIED_LEVEL: 'UNCERTAINTY_OF_MEASUREMENT_REPORT_APPLIED_LEVEL',
  PERIODICAL_REPORT_APPLIED_LEVEL: 'PERIODICAL_REPORT_APPLIED_LEVEL',
  MONTHLY_RILLIBAK_REPORT_APPLIED_LEVEL: 'MONTHLY_RILLIBAK_REPORT_APPLIED_LEVEL',
  HISTORICAL_REPORT_APPLIED_LEVEL: 'HISTORICAL_REPORT_APPLIED_LEVEL',
  TRACEABILITY_COUNT_REPORT_APPLIED_LEVEL: 'TRACEABILITY_COUNT_REPORT_APPLIED_LEVEL',
  LOT_TO_LOT_REPORT_APPLIED_LEVEL: 'LOT_TO_LOT_REPORT_APPLIED_LEVEL',
  PEER_GROUP_REPORT_APPLIED_LEVEL: 'PEER_GROUP_REPORT_APPLIED_LEVEL',
  PERIODICAL_IQC_REPORT_APPLIED_LEVEL: 'PERIODICAL_IQC_REPORT_APPLIED_LEVEL',
  CONTINUOUS_PERFORMANCE_VERIFICATION_REPORT_REPORT_APPLIED_LEVEL:
    'CONTINUOUS_PERFORMANCE_VERIFICATION_REPORT_REPORT_APPLIED_LEVEL',
  CALIBRATION_REPORT_APPLIED_LEVEL: 'CALIBRATION_REPORT_APPLIED_LEVEL',
  GROUP_CHART_REPORT_APPLIED_LEVEL: 'GROUP_CHART_REPORT_APPLIED_LEVEL',
  UOM_REPORT_DEFAULT_DATE_MONTH: 'UOM_REPORT_DEFAULT_DATE_MONTH',
  ASSAY_LIMIT_MAX_TIME_PERIOD_MONTH: 'ASSAY_LIMIT_MAX_TIME_PERIOD_MONTH',
  ERROR_JUDGEMENT_SETTING_MAX_TIME_PERIOD_MONTH: 'ERROR_JUDGEMENT_SETTING_MAX_TIME_PERIOD_MONTH',
  PARAM_SETTING_QC_DATA_MAX_TIME_PERIOD_MONTH: 'PARAM_SETTING_QC_DATA_MAX_TIME_PERIOD_MONTH',
  PARAM_SETTING_XBARM_MAX_TIME_PERIOD_MONTH: 'PARAM_SETTING_XBARM_MAX_TIME_PERIOD_MONTH',
  XBARM_CUSTOMER_MAX_DATE_RANGE_MONTH: 'XBARM_CUSTOMER_MAX_DATE_RANGE_MONTH',
  XBARM_CUSTOMER_DEFAULT_DATE_DAY: 'XBARM_CUSTOMER_DEFAULT_DATE_DAY',
  XBARM_LIMIT_MAX_TIME_PERIOD_MONTH: 'XBARM_LIMIT_MAX_TIME_PERIOD_MONTH',
  RULE_1_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_1_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_2_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_2_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_3_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_3_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_4_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_4_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_5_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_5_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_6_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_6_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_7_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_7_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_8_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_8_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_9_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_9_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_10_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_10_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_11_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_11_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_12_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_12_SETTING_MAX_TIME_PERIOD_MONTH',
  RULE_13_SETTING_MAX_TIME_PERIOD_MONTH: 'RULE_13_SETTING_MAX_TIME_PERIOD_MONTH',
};
