export interface ILanguage {
  DATE_MONTH_YEAR: string;
  DATE_MONTH_YEAR_REPORT: string;
  DATE_FULL_CHART: string;
  DATE_SHORT_CHART: string;
  DATE_MONTH: string;
  MONTH_YEAR: string;
  FORMAT_1: string;
  FORMAT_4: string;
  API_FORMAT: string;
}

export const en: ILanguage = {
  DATE_MONTH_YEAR: 'MM/dd/YYYY',
  DATE_MONTH: 'MM/dd',
  DATE_MONTH_YEAR_REPORT: 'MMDDYYYY',
  DATE_FULL_CHART: '{value:%m/%d/%Y}',
  DATE_SHORT_CHART: '{value:%m/%d}',
  MONTH_YEAR: 'MM/YYYY',
  FORMAT_1: 'HH:mm:ss',
  FORMAT_4: 'MM/dd/YYYY HH:mm:ss',
  API_FORMAT: 'YYYY-MM-ddT00:00:00',
};
