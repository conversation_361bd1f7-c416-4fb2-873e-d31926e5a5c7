@if (!isServiceL2) {
  @if (isDisplayingAlertBanner) {
    <mat-card appearance="outlined">
      <mat-card-content>
        <div class="flex flex-row gap-4 items-center">
          <mat-icon svgIcon="warning_black"></mat-icon>
          <small class="mat-mdc-tooltip-trigger banner-content flex-auto">
            <div class="flex gap-8">
              @for (alertAnalyzerStatus of mapAlertAnalyzerStatuses; track $index) {
                @if (alertAnalyzerStatus.numberOfAnalyzers) {
                  <div class="flex gap-1 items-center">
                    <span class="text-base">
                      <strong class="pr-1">{{ alertAnalyzerStatus.numberOfAnalyzers }}</strong>
                      <span i18n>analyzer status turn</span>
                      <strong>
                        {{ analyzerAlertStatusLabel(alertAnalyzerStatus.color) }}
                      </strong>
                    </span>
                    <div
                      class="w-3 h-3 rounded-full border border-none bg-[var(--{{ alertAnalyzerStatus.color }}-200)]"
                    ></div>
                  </div>
                }
              }
            </div>
          </small>
          <button mat-button (click)="openAnalyzerAlertDialog()">
            <span class="mdc-button__label text-[var(--blue-200)]" i18n>Details</span>
          </button>
        </div>
      </mat-card-content>
    </mat-card>

    <app-analyzer-alert-details
      [openDialog]="openDialog$ | async"
      [analyzerAlertStatus]="alertAnalyzerStatusContent"
    ></app-analyzer-alert-details>
  }
  <app-analyzer-troubleshooting (completeTroubleshooting)="openAnalyzerAlertDialog()"></app-analyzer-troubleshooting>
}
