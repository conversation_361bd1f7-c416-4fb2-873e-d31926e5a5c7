import { CurrentMonthValidatorDirective } from './current-month-validator.directive';
import { FormControl } from '@angular/forms';
import moment from 'moment/moment';

describe('CurrentMonthValidatorDirective', () => {
  let directive: CurrentMonthValidatorDirective;

  beforeEach(() => {
    directive = new CurrentMonthValidatorDirective();
  });

  it('should return { required: true } if the control value is empty', () => {
    const control = new FormControl('');

    const result = directive.validate(control);

    expect(result).toEqual({ required: true });
  });

  it('should return null ' +
    'if the date range is within the current month and is a full month selection', () => {
    const start = moment().startOf('month').toISOString();

    const end = moment().endOf('month').toISOString();

    const control = new FormControl({
      start,
      end,
    });

    const result = directive.validate(control);

    expect(result).toBeNull();
  });

  it('should return { isdaterangeincurrentmonth: true } ' +
    'if the date range is within the current month but not a full month selection', () => {
    const start = moment().startOf('month').add(1, 'day').toISOString();

    const end = moment().endOf('month').subtract(1, 'day').toISOString();

    const control = new FormControl({
      start,
      end,
    });

    const result = directive.validate(control);

    expect(result).toEqual({ isDateRangeInCurrentMonth: true });
  });

  it('should return null if the date range is not within the current month', () => {
    const start = moment().subtract(1, 'month').startOf('month').toISOString();

    const end = moment().subtract(1, 'month').endOf('month').toISOString();

    const control = new FormControl({
      start,
      end,
    });

    const result = directive.validate(control);

    expect(result).toBeNull();
  });
});
