import { ColDef } from 'ag-grid-community';

import { BalanceTEAs, MultiplierAndClinicalTEAs, UpperAndLowerLimits } from '../interfaces/rule-param-setting';
import { Rule1ConstantSetting, Rule2To5ConstantSetting } from '../interfaces/constant-setting';
import {
  RuleComponentsConfig,
  RuleConfig,
  RuleDataMappingFunc,
  RuleEditableParameter,
  RuleParametersTable,
} from '../interfaces/rule';
import { CocParameter } from '../interfaces/coc-parameters';
import { SORT_DIRECTION_TEXT } from 'src/app/enums';

export const AGGRID_ROW_BUFFER_LIMIT = 1000;

export const CELL_STYLE_DEFAULT = {
  display: 'flex',
  'flex-direction': 'row',
  'justify-content': 'center',
  'align-items': 'center',
};

export const RULE1_COLUMN_DEFS_BASE: ColDef[] = [
  {
    headerName: $localize`:app-app_grid-header_label:#`,
    valueGetter: 'node.rowIndex + 1',
    maxWidth: 150,
    cellStyle: CELL_STYLE_DEFAULT,
  },
  {
    field: 'parameterDisplayName',
    headerName: $localize`:app-app_grid-header_label:Parameter Name`,
    cellClass: 'text-left',
    headerClass: 'text-left',
    maxWidth: 320,
    sort: SORT_DIRECTION_TEXT.ASC,
    cellStyle: CELL_STYLE_DEFAULT,
  },
  {
    field: 'parameterType',
    headerName: $localize`:app-app_grid-header_label:Parameter Type`,
    cellClass: 'text-left',
    headerClass: 'text-left',
    maxWidth: 320,
    cellStyle: CELL_STYLE_DEFAULT,
  },
];

export const RULE2TO5_COLUMN_DEFS_BASE: ColDef[] = [
  {
    headerName: $localize`:app-app_grid-header_label:#`,
    valueGetter: 'node.rowIndex + 1',
    cellStyle: CELL_STYLE_DEFAULT,
  },
  {
    field: 'parameterDisplayName',
    headerName: $localize`:app-app_grid-header_label:Parameter Name`,
    cellClass: 'text-left',
    headerClass: 'text-left',
    sort: SORT_DIRECTION_TEXT.ASC,
    cellStyle: CELL_STYLE_DEFAULT,
  },
  {
    field: 'parameterType',
    headerName: $localize`:app-app_grid-header_label:Parameter Type`,
    cellClass: 'text-left',
    headerClass: 'text-left',
    cellStyle: CELL_STYLE_DEFAULT,
  },
];

export const RULE10_COLUMN_DEFS_BASE: ColDef[] = [
  {
    field: 'cocParameterCode',
    headerName: $localize`:app-app_grid-header_label:COC Parameter`,
    cellStyle: CELL_STYLE_DEFAULT,
  },
];

export const RULE1TO5_EDITABLE_PARAMETER_CONSTRAINTS = {
  GREATER_THAN_OR_EQUAL_TO: 0.0001,
  LESS_THAN_OR_EQUAL_TO: 100,
  MAX_DECIMALS: 4,
};

export const RULE12TO13_COLUMN_DEFS_BASE: ColDef[] = [
  {
    field: 'parameterCode',
    headerName: $localize`:app-app_grid-header_label: Parameter Name`,
    cellClass: 'text-left',
    headerClass: 'text-left',
    cellStyle: CELL_STYLE_DEFAULT,
  },
];

export const RULE12TO13_COLUMN_NAME = {
  LOWER_LIMIT: 'lowerLimit',
  UPPER_LIMIT: 'upperLimit',
  BALANCE_TEA: 'balanceTEA',
  CLINICAL_TEA: 'clinicalTEA',
  MULTIPLIER: 'multiplier',
};

export const RULES = {
  rule1: 'rule1',
  rule2: 'rule2',
  rule3: 'rule3',
  rule4: 'rule4',
  rule5: 'rule5',
  rule6: 'rule6',
  rule7: 'rule7',
  rule8: 'rule8',
  rule9: 'rule9',
  rule10: 'rule10',
  rule11: 'rule11',
  rule12: 'rule12',
  rule13: 'rule13',
};

export type SETTING_KEY = 'enable' | 'ticket';

export const RULE_COMPONENTS = {
  peerGroupSize: 'peerGroupSize',
  constantSettings: 'constantSettings',
  multiplierTea: 'multiplierTeaSettings',
  balanceTea: 'balanceTeaSettings',
  upperLowerLimits: 'upperLowerLimitSettings',
  numberSettings: 'numberSettings',
  cocParameter: 'cocParameter',
};

export const NUMBER_SETTINGS_ID = {
  recentRuns: 'recent_runs',
  troubleshootingEvents: 'troubleshooting_events',
  consecutiveControlRunSets: 'consecutive_control_run_sets',
};

export const RECENT_RUNS_RULES = ['rule4', 'rule5', 'rule9', 'rule13'];

export enum TABLE_ID {
  MULTIPLIER_CLINICAL_TEAS = 'multiplierAndClinicalTEAs',
  BALANCE_TEAS = 'balanceTEAs',
  UPPER_LOWER_LIMIT = 'upperAndLowerLimits',
  CONSTANT_SETTINGS = 'constantSettings',
  COC_PARAMETER = 'cocParameter'
}

const settings = [
  {
    id: 'recent_runs',
    label: $localize`:app-app_label:Number of Recent Runs`,
    type: 'number',
  },
  {
    id: 'consecutive_troubleshooting_events',
    label: $localize`:app-app_label:Number of Consecutive Troubleshooting Events`,
    type: 'number',
  },
  {
    id: 'troubleshooting_events',
    label: $localize`:app-app_label:Number of Troubleshooting Events`,
    type: 'number',
  },
  {
    id: 'consecutive_control_run_sets',
    label: $localize`:app-app_label:Number of Consecutive Control Runs`,
    type: 'number',
  },
  {
    id: 'consecutive_days',
    label: $localize`:app-app_label:Number of Consecutive Days`,
    type: 'number',
  },
  {
    id: 'difference_allowable_percentage',
    label: $localize`:app-app_label:Difference Allowable (%)`,
    type: 'percent',
  },
];

const rules = [
  {
    id: 'recent_runs',
    ruleCode: 'rule4',
    order: 1,
  },
  {
    id: 'recent_runs',
    ruleCode: 'rule5',
    order: 1,
  },
  {
    id: 'consecutive_troubleshooting_events',
    ruleCode: 'rule6',
    order: 1,
  },
  {
    id: 'troubleshooting_events',
    ruleCode: 'rule7',
    order: 1,
  },
  {
    id: 'consecutive_control_run_sets',
    ruleCode: 'rule7',
    order: 2,
  },
  {
    id: 'troubleshooting_events',
    ruleCode: 'rule8',
    order: 1,
  },
  {
    id: 'consecutive_days',
    ruleCode: 'rule8',
    order: 2,
  },
  {
    id: 'recent_runs',
    ruleCode: 'rule9',
    order: 1,
  },
  {
    id: 'difference_allowable_percentage',
    ruleCode: 'rule9',
    order: 2,
  },
  {
    id: 'recent_runs',
    ruleCode: 'rule13',
    order: 1,
  },
];

export const NUMBER_SETTINGS = settings.flatMap((setting) =>
  rules
    .filter((rule) => rule.id === setting.id)
    .map((rule) => ({
      ...setting,
      ...rule,
    })),
);

export const NUMBER_SETTING_CONSTRAINTS = {
  NUMBER_MIN: 1,
  NUMBER_MAX: 100,
  PERCENT_MIN: 0.0001,
  PERCENT_MAX: 1000,
  MAX_DECIMALS: 4,
};

export const TEXT_POSITION = {
  LEFT: 'text-left',
};

const RULE1_DATA_MAPPING_FUNC = (row: Rule1ConstantSetting) => ({
  parameterId: row.parameterId,
  ebqcl: Number(row.ebqcl),
  teaMultiplier: Number(row.teaMultiplier),
});

const RULE2TO5_EDITABLE_PARAMETER_CONSTRAINTS = {
  allowDecimals: true,
  min: 0.0001,
  max: 100,
  minIncluded: true,
  maxIncluded: true,
  maxDecimals: 4,
};

const RULE10_EDITABLE_PARAMETER_CONSTRAINTS = {
  allowDecimals: true,
  min: 0.0001,
  max: 10000,
  minIncluded: true,
  maxIncluded: true,
  maxDecimals: 4,
};

const RULE2TO5_DATA_MAPPING_FUNC = (row: Rule2To5ConstantSetting) => ({
  parameterId: row.parameterId,
  value: Number(row.value),
});

export const RULE10_DATA_MAPPING_FUNC = (row: CocParameter) => ({
  cocParameterCode: row.cocParameterCode,
  lowerLimit: row.lowerLimit,
  upperLimit: row.upperLimit,
});

const RULE12TO13_DATA_MAPPING_MULTIPLIER_TEA_FUNC = (row: MultiplierAndClinicalTEAs) => ({
  parameterCode: row.parameterCode,
  multiplier: row.multiplier,
  clinicalTEA: row.clinicalTEA,
});

const RULE12TO13_DATA_MAPPING_UPPER_LOWER_LIMIT_FUNC = (row: UpperAndLowerLimits) => ({
  parameterCode: row.parameterCode,
  lowerLimit: row.lowerLimit,
  upperLimit: row.upperLimit,
});

const RULE12TO13_DATA_MAPPING_BALANCE_TEA_FUNC = (row: BalanceTEAs) => ({
  parameterCode: row.parameterCode,
  balanceTEA: row.balanceTEA,
});

const RULE12TO13_EDITABLE_PARAMETER_CONSTRAINTS = {
  allowDecimals: true,
  min: 0.0001,
  max: 100,
  minIncluded: true,
  maxIncluded: true,
  maxDecimals: 4,
};

const RULE12TO13_EDITABLE_UPPER_LOWER_CONSTRAINTS = {
  allowDecimals: true,
  min: -1000,
  max: 1000,
  minIncluded: true,
  maxIncluded: true,
  maxDecimals: 4,
  allowSign: true,
};

function createRuleComponentsConfig(
  hasPeerGroupSize: boolean,
  hasCreateTicket: boolean,
  hasNumberSettings: boolean,
  hasConstantSettings: boolean,
  hasMultiplierTea: boolean,
  hasBalanceTea: boolean,
  hasUpperLowerLimit: boolean,
  hasCocParameters = false,
): RuleComponentsConfig {
  return {
    hasPeerGroupSize: hasPeerGroupSize,
    hasCreateTicket: hasCreateTicket,
    hasNumberSettings: hasNumberSettings,
    hasConstantSettings: hasConstantSettings,
    hasMultiplierTea: hasMultiplierTea,
    hasBalanceTea: hasBalanceTea,
    hasUpperLowerLimit: hasUpperLowerLimit,
    hasCocParameters: hasCocParameters,
  };
}

function createRuleConstantSettingsConfig(
  editableColumns: RuleEditableParameter[],
  dataMappingFunc: RuleDataMappingFunc,
): RuleParametersTable {
  return {
    editableColumns: editableColumns,
    dataMappingFunc: dataMappingFunc,
  };
}

const extractNumberFromRuleCode = (ruleCode: string): string => {
  const match = ruleCode.match(/\d+/);

  return match ? match[0] : '';
};

const cocParameterConfig = () => {
  return createRuleConstantSettingsConfig(
    [
      {
        id: 'lowerLimit',
        label: $localize`:app-app_grid-header_label:Lower Limit`,
        constraints: RULE10_EDITABLE_PARAMETER_CONSTRAINTS,
      },
      {
        id: 'upperLimit',
        label: $localize`:app-app_grid-header_label:Upper Limit`,
        constraints: RULE10_EDITABLE_PARAMETER_CONSTRAINTS,
      },
    ],
    RULE10_DATA_MAPPING_FUNC,
  );
};

const multiplierTeaConfig = (ruleCode: string) => {
  const ruleNumber = extractNumberFromRuleCode(ruleCode);

  return createRuleConstantSettingsConfig(
    [
      {
        id: 'clinicalTEA',
        label: $localize`:app-app_grid-header_label:Clinical TEA` + ' (%)',
        constraints: RULE12TO13_EDITABLE_PARAMETER_CONSTRAINTS,
      },
      {
        id: 'multiplier',
        label: $localize`:app-app_grid-header_label:Rule ${ruleNumber} Multiplier`,
        constraints: RULE12TO13_EDITABLE_PARAMETER_CONSTRAINTS,
      },
    ],
    RULE12TO13_DATA_MAPPING_MULTIPLIER_TEA_FUNC,
  );
};

const balanceTeaConfig = (ruleCode: string) => {
  const ruleNumber = extractNumberFromRuleCode(ruleCode);

  return createRuleConstantSettingsConfig(
    [
      {
        id: 'balanceTEA',
        label: $localize`:app-app_grid-header_label:Rule ${ruleNumber} Balance TEA (%)`,
        constraints: RULE12TO13_EDITABLE_PARAMETER_CONSTRAINTS,
      },
    ],
    RULE12TO13_DATA_MAPPING_BALANCE_TEA_FUNC,
  );
};

const upperLowerLimitConfig = (ruleCode: string) => {
  const ruleNumber = extractNumberFromRuleCode(ruleCode);

  return createRuleConstantSettingsConfig(
    [
      {
        id: 'lowerLimit',
        label: $localize`:app-app_grid-header_label:Rule ${ruleNumber} Lower Limit (%)`,
        constraints: RULE12TO13_EDITABLE_UPPER_LOWER_CONSTRAINTS,
      },
      {
        id: 'upperLimit',
        label: $localize`:app-app_grid-header_label:Rule ${ruleNumber} Upper Limit (%)`,
        constraints: RULE12TO13_EDITABLE_UPPER_LOWER_CONSTRAINTS,
      },
    ],
    RULE12TO13_DATA_MAPPING_UPPER_LOWER_LIMIT_FUNC,
  );
};

export const RULE_CONFIGS: RuleConfig[] = [
  {
    ruleCode: 'rule1',
    componentsConfig: createRuleComponentsConfig(true, true, false, true, false, false, false),
    constantSettingsTable: createRuleConstantSettingsConfig(
      [
        {
          id: 'ebqcl',
          label: $localize`:app-app_grid-header_label:Evidence based QC Limit (%)`,
          constraints: {
            allowDecimals: true,
            min: 0.0001,
            max: 1000,
            minIncluded: true,
            maxIncluded: true,
            maxDecimals: 4,
          },
        },
        {
          id: 'teaMultiplier',
          label: $localize`:app-app_grid-header_label:TEA Multiplier`,
          constraints: {
            allowDecimals: true,
            min: 0.0001,
            max: 100,
            minIncluded: true,
            maxIncluded: true,
            maxDecimals: 4,
          },
        },
      ],
      RULE1_DATA_MAPPING_FUNC,
    ),
  },
  {
    ruleCode: 'rule2',
    componentsConfig: createRuleComponentsConfig(false, true, false, true, false, false, false),
    constantSettingsTable: createRuleConstantSettingsConfig(
      [
        {
          id: 'value',
          label: $localize`:app-app_grid-header_label:Rule 2 SDI Limit`,
          constraints: RULE2TO5_EDITABLE_PARAMETER_CONSTRAINTS,
        },
      ],
      RULE2TO5_DATA_MAPPING_FUNC,
    ),
  },
  {
    ruleCode: 'rule3',
    componentsConfig: createRuleComponentsConfig(false, true, false, true, false, false, false),
    constantSettingsTable: createRuleConstantSettingsConfig(
      [
        {
          id: 'value',
          label: $localize`:app-app_grid-header_label:Rule 3 CV Multiplier`,
          constraints: RULE2TO5_EDITABLE_PARAMETER_CONSTRAINTS,
        },
      ],
      RULE2TO5_DATA_MAPPING_FUNC,
    ),
  },
  {
    ruleCode: 'rule4',
    componentsConfig: createRuleComponentsConfig(false, true, true, true, false, false, false),
    constantSettingsTable: createRuleConstantSettingsConfig(
      [
        {
          id: 'value',
          label: $localize`:app-app_grid-header_label:Rule 4 SDI Limit`,
          constraints: RULE2TO5_EDITABLE_PARAMETER_CONSTRAINTS,
        },
      ],
      RULE2TO5_DATA_MAPPING_FUNC,
    ),
  },
  {
    ruleCode: 'rule5',
    componentsConfig: createRuleComponentsConfig(false, true, true, true, false, false, false),
    constantSettingsTable: createRuleConstantSettingsConfig(
      [
        {
          id: 'value',
          label: $localize`:app-app_grid-header_label:Rule 5 CV Multiplier`,
          constraints: RULE2TO5_EDITABLE_PARAMETER_CONSTRAINTS,
        },
      ],
      RULE2TO5_DATA_MAPPING_FUNC,
    ),
  },
  {
    ruleCode: 'rule6',
    componentsConfig: createRuleComponentsConfig(false, true, true, false, false, false, false),
  },
  {
    ruleCode: 'rule7',
    componentsConfig: createRuleComponentsConfig(false, true, true, false, false, false, false),
  },
  {
    ruleCode: 'rule8',
    componentsConfig: createRuleComponentsConfig(false, true, true, false, false, false, false),
  },
  {
    ruleCode: 'rule9',
    componentsConfig: createRuleComponentsConfig(false, true, true, false, false, false, false),
  },
  {
    ruleCode: 'rule10',
    componentsConfig: createRuleComponentsConfig(false, true, false, false, false, false, false, true),
    cocParameterTable: cocParameterConfig(),
  },
  {
    ruleCode: 'rule12',
    componentsConfig: createRuleComponentsConfig(false, true, false, false, true, true, true),
    multiplierTeaTable: multiplierTeaConfig('rule12'),
    balanceTeaTable: balanceTeaConfig('rule12'),
    upperLowerLimitTable: upperLowerLimitConfig('rule12'),
  },
  {
    ruleCode: 'rule13',
    componentsConfig: createRuleComponentsConfig(false, true, true, false, true, true, true),
    multiplierTeaTable: multiplierTeaConfig('rule13'),
    balanceTeaTable: balanceTeaConfig('rule13'),
    upperLowerLimitTable: upperLowerLimitConfig('rule13'),
  },
];

export const MAX_DECIMAL_PLACES = 4;
