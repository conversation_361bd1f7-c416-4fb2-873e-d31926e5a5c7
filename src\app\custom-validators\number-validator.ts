import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';
import { Directive, Input } from '@angular/core';

import { Utils } from 'src/app/helpers/UtilFunctions';

@Directive({
  selector: '[appNumberValidator]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: NumberValidatorDirective,
      multi: true,
    },
  ],
  standalone: true,
})
export class NumberValidatorDirective implements Validator {
  @Input() maxDecimals?: number;

  @Input() minValue?: number;

  @Input() maxValue?: number;

  @Input() required?: boolean = true;

  validate(control: AbstractControl): ValidationErrors | null {
    if (!this.required && !control.value) {
      return null;
    }

    if (this.required && Utils.isNullishNumber(control.value)) {
      return { required: true };
    }

    const value = Number(control.value);

    if (this.maxValue !== undefined && value > this.maxValue) {
      return { maxError: true };
    }

    if (this.minValue !== undefined && value < this.minValue) {
      return { minError: true };
    }

    if (Utils.isExceedDecimals(value, this.maxDecimals)) {
      return { exceedDecimalsError: true };
    }

    return null;
  }
}
