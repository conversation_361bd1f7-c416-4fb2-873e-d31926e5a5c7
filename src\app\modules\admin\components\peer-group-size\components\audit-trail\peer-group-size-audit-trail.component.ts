import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AgGridModule } from 'ag-grid-angular';
import { CommonModule } from '@angular/common';
import { DesignSystemModule } from '@sysmex/design-system';

import { AuditTrailLogType } from 'src/app/enums/audit-trail';
import { DialogService } from 'src/app/services/dialog.service';
import { PaginatorComponent } from 'src/app/modules/common/paginator/components/paginator.component';
import { PeerGroupSizeAuditTrailTableRow } from 'src/app/modules/admin/interfaces/rule-configuration-audit-trail';
import { RuleConfigurationAuditTrailDialogBaseComponent } 
  from '../../../rule-configuration-audit-trail-dialog-base/rule-configuration-audit-trail-dialog-base.component';
import { RuleConfigurationService } from 'src/app/modules/admin/services/rule-configuration.service';

@Component({
  selector: 'app-peer-group-size-audit-trail',
  templateUrl:
    '../../../rule-configuration-audit-trail-dialog-base/rule-configuration-audit-trail-dialog-base.component.html',
  standalone: true,
  imports: [AgGridModule, CommonModule, DesignSystemModule, PaginatorComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeerGroupSizeAuditTrailComponent
  extends RuleConfigurationAuditTrailDialogBaseComponent<PeerGroupSizeAuditTrailTableRow> {
  constructor(
    override readonly dialogService: DialogService,
    override readonly ruleConfigurationService: RuleConfigurationService,
  ) {
    super(dialogService, ruleConfigurationService);

    this.dialogTitle = $localize`:app-app_dialog-title:Peer Group Size Log`;

    this.columnDefs = [
      ...this.sharedColumnDefs,
      {
        field: 'size',
        headerName: $localize`:app-app_grid-header_label:Number of Analyzers`,
        cellClass: 'text-right',
        minWidth: 150,
      },
    ];

    this.auditTrailLogType = AuditTrailLogType.PeerGroupSize;

    this.tableMapperFn = this.ruleConfigurationService.mapPeerGroupSizeAuditTrailData;
  }

  override componentOnInit(): void {
    return;
  }
}
