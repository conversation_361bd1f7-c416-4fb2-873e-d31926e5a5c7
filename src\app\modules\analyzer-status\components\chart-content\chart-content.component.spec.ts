import { ActivatedRoute, RouterModule } from '@angular/router';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatTab, MatTabChangeEvent } from '@angular/material/tabs';
import { of, throwError } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SimpleChanges } from '@angular/core';

import {
  AdditionalDataPoint,
  ChartPnt,
  ChartSeries,
  DataSeries,
  LevelSeries,
  LotSeries,
  MultiComparisonDataResponse,
  QcResult,
  SDIComparisonDataPoint,
  SDIComparisonDataPoints,
  SDIMultipleComparisonDataResponse,
  SDISingleComparisonDataResponse,
  SDISingleComparisonStatistic,
} from '../../interfaces/qc-data';
import {
  AnalyzerEventInfo,
  CompareLotModel,
  ComparingChartFilterParameter,
  DsLotSelectOption,
  ParameterDateRangeInfo,
  QcComparisonData,
  ReagentInfo,
} from '../../interfaces/analyzer';
import { ChannelWarning, ChannelWarningRequest, ParameterCriteriaDateRange } from '../../interfaces/channel';
import { ChartCriteriaEvent, EventAdvancedFilterChange } from 'src/app/models/event-params';
import { MOCK_ANALYZER_EVENT_INFO, MOCK_CHANGES, MOCK_FILTER_PARAMS } from '../../mocks/analyzer';
import { MOCK_REAGENT, MOCK_SDI_SINGLE_ANALYZERS_RESULT } from '../../mocks/qc-data';
import { CHART_MODE } from '../../constants';

import { GetQcDataRequestParams, QcDataService } from '../../services/qc-data.service';
import { AclModule } from 'src/app/directives/acl/acl.module';
import { AnalyzerService } from '../../services/analyzer.service';
import { ChannelService } from '../../services/channel.service';
import { ChartContentComponent } from './chart-content.component';
import { ChartExportComponent } from '../chart-export/chart-export.component';
import { ChartExportCsvService } from '../../services/chart-export-csv.service';
import { ChartExportPdfService } from '../../services/chart-export-pdf.service';
import { ChartStateService } from '../../services/chart-state.service';
import { CustomerLimit } from 'src/app/modules/settings/interfaces/customer-limits';
import { DatePipe } from '@angular/common';
import { EMPTY } from 'src/app/constant';
import { EventParam } from 'src/app/models/event-items';
import { HttpClient } from '@angular/common/http';
import { LimitSetting } from '../../interfaces/limit-setting';
import { LoadingService } from 'src/app/services/loading.service';
import { MOCK_CHANNELS } from '../../mocks/channels';
import { POINT_MARKER } from 'src/app/enums';
import { ParameterSelectedAnalyzers } from '../../interfaces/qc-comparison-data';
import { PdfFontService } from 'src/app/services/pdf-font.service';
import { SysmexService } from 'src/app/services/sysmex.service';
import { UserService } from 'src/app/services/user.service';
import { UserStoreService } from 'src/app/services/user-store.service';
import { Utils } from 'src/app/helpers/UtilFunctions';
import { VIEW_MODE } from '../../interfaces/enums';

describe('ChartContentComponent', () => {
  let component: ChartContentComponent;
  let fixture: ComponentFixture<ChartContentComponent>;

  const mockChannelService = {
    getChannels: jest.fn(),
    getChannelsWarning: jest.fn(),
    getChannelsWarningMultipleAnalyzers: jest.fn(),
    getPreviousLotChannelsWarning: jest.fn(),
    getComparisonFirstBarMChannelsWarning: jest.fn(),
    getComparisonSecondBarMChannelsWarning: jest.fn(),
    getChannelStatusSdiMultipleAnalyzers: jest.fn(),
    getSDISingleChannelsStatus: jest.fn(),
  };

  const mockQcDataService = {
    getQcData: jest.fn(),
    getDataTableExportedFile: jest.fn(),
    getQcStatistics: jest.fn(),
    setFilterDataTableParameters: jest.fn(),
    getChart: jest.fn(),
    getChannelsWarning: jest.fn(),
    getPreviousLotComparisonData: jest.fn(),
    getToXBarMComparisonQcData: jest.fn(),
    getToXBarMData: jest.fn(),
    getZScoreData: jest.fn(),
    getSDIMultipleComparisonData: jest.fn(),
    getMultiComparisonData: jest.fn(),
    getQCManageStatus: jest.fn(),
    getFilterDataTableParameters: jest.fn(),
  };

  const mockAnalyzerService = {
    getReagentEvents: jest.fn(),
    getAnalyzerEvents: jest.fn(),
    getAnalyzerServiceHistory: jest.fn(),
    getLastQcRunSet: jest.fn(),
  };

  const mockAnalyzerDetailExportPdfService = {
    exportPdf: jest.fn(),
  };

  const mockChartExport = {
    exportPdf: jest.fn(),
  };

  const mockChartExportCsvService = {
    exportTableAsCsv: jest.fn(),
    exportTableViewFile: jest.fn(),
  };

  const userServiceMock = {};

  const tabChangeEvent: MatTabChangeEvent = {
    index: 1,
    // 3rd party library, so we need to casting type
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    tab: null as unknown as MatTab,
  };

  const mockActivatedRoute: Partial<ActivatedRoute> = {
    data: of({}),
  };

  const mockUserStoreService = {
    user: {
      language: { languageCode: 'en' },
      access: [
        {
          sites: {
            siteCode: 'IL0624',
            siteName: 'Site Name',
            address1: 'Address 1',
            city: 'City',
            state: 'State',
            postalCode: 'Postal Code',
          },
        },
      ],
    },
    hasSitePackageLevel3: jest.fn().mockReturnValue(true),
  };

  const mockFormatCollection = {
    DATE_MONTH_YEAR: 'MM/dd/YYYY',
    DATE_MONTH_YEAR_REPORT: 'MMDDYYYY',
    FORMAT_1: 'HH:mm:ss',
    FORMAT_4: 'MM/dd/YYYY HH:mm:ss',
    API_FORMAT: 'YYYY-MM-dd',
  };

  const mockSysmex = {
    formatDate: jest.fn().mockImplementation((format, date) => {
      const datePipe = new DatePipe('en-US');

      return datePipe.transform(date, format);
    }),
    DATES: mockFormatCollection,
    formatDateWithTimeZone: jest
      .fn()
      .mockImplementation((datetime, timezone) => datetime?.replace('Z', EMPTY).concat(timezone.replace('UTC', EMPTY))),
  };

  const mockChartStateService = {
    resetHorizontalScale: jest.fn(),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        {
          provide: ChannelService,
          useValue: mockChannelService,
        },
        {
          provide: ChartExportCsvService,
          useValue: mockChartExportCsvService,
        },
        {
          provide: QcDataService<AdditionalDataPoint | undefined>,
          useValue: mockQcDataService,
        },
        {
          provide: AnalyzerService,
          useValue: mockAnalyzerService,
        },
        {
          provide: ChartExportComponent,
          useValue: mockChartExport,
        },
        {
          provide: ChartExportPdfService,
          useValue: mockAnalyzerDetailExportPdfService,
        },
        {
          provide: UserService,
          useValue: userServiceMock,
        },
        {
          provide: UserStoreService,
          useValue: mockUserStoreService,
        },
        {
          provide: SysmexService,
          useValue: mockSysmex,
        },
        {
          provide: ChartStateService,
          useValue: mockChartStateService,
        },
      ],
      imports: [NoopAnimationsModule, HttpClientTestingModule, ChartContentComponent, RouterModule, AclModule],
    })
      .overrideComponent(ChartContentComponent, {
        set: {
          providers: [
            {
              provide: ChartExportPdfService,
              useValue: mockAnalyzerDetailExportPdfService,
            },
            {
              provide: ActivatedRoute,
              useValue: mockActivatedRoute,
            },
          ],
        },
      })
      .compileComponents();

    fixture = TestBed.createComponent(ChartContentComponent);

    component = fixture.componentInstance;

    component.analyzerParameter = {
      modelCode: 'model1',
      serialNumber: '123456',
      site: {
        siteName: 'SYSMEX AMERICA CTR FOR LEARN (other)',
        address1: '1011 Woodlands Parkway',
        city: 'Vernon Hills',
        state: 'IL',
        postalCode: '60061',
        siteCode: '123',
      },
    };
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load channels on init and select the first one', () => {
    mockChannelService.getChannels.mockReturnValue(of(MOCK_CHANNELS));
    mockAnalyzerService.getLastQcRunSet.mockReturnValue(of([]));

    component.ngOnChanges(MOCK_CHANGES);

    component.analyzerParameter = {
      modelCode: 'model1',
      serialNumber: '123456',
      site: {
        siteName: 'SYSMEX AMERICA CTR FOR LEARN (other)',
        address1: '1011 Woodlands Parkway',
        city: 'Vernon Hills',
        state: 'IL',
        postalCode: '60061',
        siteCode: '123',
      },
    };

    component.chartFilterParameter = {
      selectedFullLotNumbers: ['lot1'],
      selectedDateRange: {
        start: 'start1',
        end: 'end1',
      },
      currentLevelLotNumber: '3312',
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'XN CHECK',
      selectedMaterialControlLotIds: ['controlLot1'],
    };

    fixture.detectChanges();

    expect(component.channels).toBeDefined();
    expect(component.activeChannel).toBeDefined();
    expect(mockChannelService.getChannels).toHaveBeenCalled();
  });

  it('should update active channel and fetch chart data on tab change', () => {
    component.channels = MOCK_CHANNELS;
    component.activeChannel = MOCK_CHANNELS[0].channelName;

    mockQcDataService.getQcData.mockReturnValue(of([]));
    mockAnalyzerService.getReagentEvents.mockReturnValue(of([]));
    mockAnalyzerService.getAnalyzerEvents.mockReturnValue(of([]));
    mockAnalyzerService.getAnalyzerServiceHistory.mockReturnValue(of([]));

    component.analyzerParameter = {
      modelCode: 'model1',
      serialNumber: '123456',
      site: {
        siteName: 'SYSMEX AMERICA CTR FOR LEARN (other)',
        address1: '1011 Woodlands Parkway',
        city: 'Vernon Hills',
        state: 'IL',
        postalCode: '60061',
        siteCode: '123',
      },
    };

    component.chartFilterParameter = {
      selectedFullLotNumbers: ['lot1'],
      selectedMaterialControlLotIds: ['controlLot1'],
      selectedDateRange: {
        start: 'start1',
        end: 'end1',
      },
      currentLevelLotNumber: '3312',
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'XN CHECK',
    };

    fixture.detectChanges();

    component.onChannelChanged(tabChangeEvent);

    expect(component.activeChannel).toEqual(MOCK_CHANNELS[1].channelName);
    expect(mockQcDataService.getQcData).toHaveBeenCalled();
    expect(mockAnalyzerService.getAnalyzerEvents).toHaveBeenCalled();
    expect(mockAnalyzerService.getAnalyzerServiceHistory).toHaveBeenCalled();
    expect(mockChartStateService.resetHorizontalScale).toHaveBeenCalled();
  });

  it('should handle error in getAnalyzerEvents and execute catchError block', () => {
    const mockParams: GetQcDataRequestParams = {
      modelCode: '',
      serialNumber: '',
    };
    const mockActiveChannelId = 1;

    jest.spyOn(mockQcDataService, 'getQcData').mockReturnValue(of([]));
    jest.spyOn(component, 'mapQCResults').mockReturnValue(of([]));
    jest
      .spyOn(component, 'getAnalyzerEvents')
      .mockReturnValue(throwError(() => new Error('Analyzer events fetch failed')));

    component.getQcData(mockParams, mockActiveChannelId);

    expect(component.getAnalyzerEvents).toHaveBeenCalled();
    expect(mockQcDataService.getQcData).toHaveBeenCalledWith(mockParams);

    setTimeout(() => {
      expect(component.analyzerEventInfo).toEqual({
        reagentEvents: [],
        calibrationEvents: [],
        serviceHistories: [],
      });

      expect(component.reagentInfo).toEqual([]);
    }, 0);
  });

  it('should update isShowServiceEvents value after call onToggleChartServiceEventsDisplay ', () => {
    component.isShowServiceEvents = true;

    component.onToggleChartServiceEventsDisplay(false);

    expect(component.isShowServiceEvents).toBe(false);
  });

  it('should update isShowCalibrationEvents value after call onToggleChartCalibrationEventsDisplay ', () => {
    component.isShowCalibrationEvents = true;

    component.onToggleChartCalibrationEventsDisplay(false);

    expect(component.isShowCalibrationEvents).toBe(false);
  });

  it('should update isShowChartComment value after call onToggleChartCommentDisplay ', () => {
    component.isShowComments = true;

    component.onToggleChartCommentsDisplay(false);

    expect(component.isShowComments).toBe(false);
  });

  it('should update isShowStatisticsTable value after call onToggleStatisticsTableDisplay ', () => {
    component.chartFunctions.hasStatisticTables = true;

    component.onToggleStatisticsTableDisplay(false);

    expect(component.chartFunctions.hasStatisticTables).toBe(false);
  });

  it('should initialize channels', () => {
    const channels = [{ channelName: 'channel1' }, { channelName: 'channel2' }];

    mockChannelService.getChannels.mockReturnValue(of(channels));

    component.analyzerParameter = {
      modelCode: 'model1',
      serialNumber: '123456',
      site: {
        siteName: 'SYSMEX AMERICA CTR FOR LEARN (other)',
        address1: '1011 Woodlands Parkway',
        city: 'Vernon Hills',
        state: 'IL',
        postalCode: '60061',
        siteCode: '123,',
      },
    };

    component.initializeChannels();

    expect(mockChannelService.getChannels).toHaveBeenCalled();
    expect(component.channels).toEqual([]);
    expect(component.activeChannel).toEqual('channel1');
  });

  it('should handle channel change', () => {
    const channels = [
      {
        channelName: 'channel1',
        channelId: 1,
        parameters: [],
      },
      {
        channelName: 'channel2',
        channelId: 2,
        parameters: [],
      },
    ];
    const qcResults: QcResult<AdditionalDataPoint>[] = [];

    mockQcDataService.getQcData.mockReturnValue(of(qcResults));
    mockAnalyzerService.getAnalyzerEvents.mockReturnValue(of(MOCK_ANALYZER_EVENT_INFO));

    component.channels = channels;
    component.chartFilterParameter = MOCK_FILTER_PARAMS;

    component.onChannelChanged(tabChangeEvent);

    expect(component.activeChannel).toEqual('channel2');

    expect(component.qcResults).toEqual(qcResults);

    expect(mockQcDataService.getQcData).toHaveBeenCalledWith({
      modelCode: 'model1',
      serialNumber: '123456',
      materialControlLotIds: MOCK_FILTER_PARAMS.selectedMaterialControlLotIds,
      channelId: 2,
      startDate: MOCK_FILTER_PARAMS.selectedDateRange.start,
      endDate: MOCK_FILTER_PARAMS.selectedDateRange.end,
    });

    expect(mockAnalyzerService.getAnalyzerEvents).toHaveBeenCalledWith({
      siteCode: '123',
      modelCode: 'model1',
      serialNumber: '123456',
      startDate: '2024-05-15',
      endDate: '2024-05-20',
    });
  });

  it('should not call getChart function when activeChannel is not changed', () => {
    jest.spyOn(component, 'getChart');

    component.channels = [
      {
        channelName: 'channel1',
        channelId: 1,
        parameters: [],
      },
      {
        channelName: 'channel2',
        channelId: 2,
        parameters: [],
      },
    ];

    component.activeChannel = 'channel1';

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const event = {
      index: 0,
    } as unknown as MatTabChangeEvent;

    component.onChannelChanged(event);

    expect(component.getChart).toHaveBeenCalledTimes(0);
  });

  it('should clear chart', () => {
    component.qcResults = [];

    component.clearChart();

    expect(component.qcResults).toEqual([]);
  });

  it('should get channels warning', () => {
    const channelsWarning = [{ isWarning: true }, { isWarning: false }];

    mockChannelService.getChannelsWarning.mockReturnValue(of(channelsWarning));

    component.chartMode = CHART_MODE.ADVANCED_LJ_CHART;

    component.chartFilterParameter = MOCK_FILTER_PARAMS;

    component.fetchWarningsStatus();

    const result = {
      analyzerSerial: '123456',
      endDate: '2024-05-20',
      materialControlLotIds: ['3312', '3314'],
      modelCode: 'model1',
      startDate: '2024-05-15',
    };

    expect(mockChannelService.getChannelsWarning).toHaveBeenCalledWith(result);
    expect(component.channelsWarningTrue).toEqual([{ isWarning: true }, { isWarning: false }]);
  });

  it('should call setFilterDataTableParameters if viewMode is TABLE', () => {
    component.viewMode = VIEW_MODE.TABLE;

    component.chartFilterParameter = MOCK_FILTER_PARAMS;

    component.isServiceLevel3 = true;

    jest.spyOn(component, 'setFilterDataTableParameters');

    component.updateChartFromCriteria();

    expect(component.setFilterDataTableParameters).toHaveBeenCalled();
  });

  it('should call getChart if viewMode is not TABLE and filterParameter changes', () => {
    component.viewMode = VIEW_MODE.CHART; // Assume CHART is not TABLE

    component.chartFilterParameter = MOCK_FILTER_PARAMS;

    component.isServiceLevel3 = true;

    jest.spyOn(component, 'getChart');

    component.updateChartFromCriteria();

    expect(component.getChart).toHaveBeenCalled();
  });

  it('should get chart', () => {
    const qcResults: QcResult<AdditionalDataPoint>[] = [];

    mockQcDataService.getQcData.mockReturnValue(of(qcResults));
    mockAnalyzerService.getReagentEvents.mockReturnValue(of(MOCK_REAGENT));
    mockAnalyzerService.getAnalyzerEvents.mockReturnValue(of(MOCK_ANALYZER_EVENT_INFO));

    component.channels = [
      {
        channelName: 'channel1',
        channelId: 1,
        parameters: [],
      },
    ];
    component.activeChannel = 'channel1';
    component.chartFilterParameter = MOCK_FILTER_PARAMS;
    component.isServiceLevel3 = true;

    component.getChart();

    setTimeout(() => {
      expect(mockQcDataService.getQcData).toHaveBeenCalledWith({
        modelCode: 'model1',
        serialNumber: '123456',
        materialControlLotIds: MOCK_FILTER_PARAMS.selectedMaterialControlLotIds,
        channelId: 1,
        startDate: MOCK_FILTER_PARAMS.selectedDateRange.start,
        endDate: MOCK_FILTER_PARAMS.selectedDateRange.end,
      });
      expect(component.qcResults).toEqual(qcResults);

      expect(mockAnalyzerService.getAnalyzerEvents).toHaveBeenCalledWith({
        modelCode: 'model1',
        serialNumber: '123456',
        startDate: '2024-05-15',
        endDate: '2024-05-20',
      });
      expect(component.analyzerEventInfo).toEqual(MOCK_ANALYZER_EVENT_INFO);
    }, 0);
  });

  it('should get chart without service events on level 2', () => {
    const qcResults: QcResult<AdditionalDataPoint>[] = [];

    mockQcDataService.getQcData.mockReturnValue(of(qcResults));
    mockAnalyzerService.getReagentEvents.mockReturnValue(of(MOCK_REAGENT));
    mockAnalyzerService.getAnalyzerEvents.mockReturnValue(of(MOCK_ANALYZER_EVENT_INFO));

    component.channels = [
      {
        channelName: 'channel1',
        channelId: 1,
        parameters: [],
      },
    ];
    component.activeChannel = 'channel1';
    component.chartFilterParameter = MOCK_FILTER_PARAMS;
    component.isServiceLevel3 = false;

    component.getChart();

    setTimeout(() => {
      expect(component.analyzerEventInfo.serviceHistories).toEqual(MOCK_ANALYZER_EVENT_INFO.serviceHistories);
    }, 0);
  });

  it('should display warning on channel', () => {
    component.channels = [
      {
        channelId: 1,
        isWarning: true,
        isError: false,
        channelName: EMPTY,
      },
      {
        channelId: 2,
        isWarning: false,
        isError: true,
        channelName: EMPTY,
      },
    ];

    component.isServiceLevel3 = false;

    expect(component.getChannelIcon(1)).toEqual('warning');
    expect(component.getChannelIcon(2)).toEqual('warning_yellow');
  });

  it('should toggle chart comments display', () => {
    component.onToggleChartCommentsDisplay(true);

    expect(component.isShowComments).toBe(true);

    component.onToggleChartCommentsDisplay(false);

    expect(component.isShowComments).toBe(false);
  });

  it('should toggle statistics table display', () => {
    component.onToggleStatisticsTableDisplay(true);

    expect(component.chartFunctions.hasStatisticTables).toBe(true);

    component.onToggleStatisticsTableDisplay(false);

    expect(component.chartFunctions.hasStatisticTables).toBe(false);
  });

  it('should call getDataTableExportedFile and create a file', () => {
    component.chartFilterParameter = MOCK_FILTER_PARAMS;
    jest.spyOn(mockChartExportCsvService, 'exportTableAsCsv').mockImplementation(() => of('response from service'));

    component.exportTableViewFile();

    expect(mockChartExportCsvService.exportTableAsCsv).toHaveBeenCalled();
  });

  it('should call exportTableViewFile when trigger export method in table view mode', () => {
    component.chartFilterParameter = MOCK_FILTER_PARAMS;
    component.viewMode = VIEW_MODE.TABLE;

    jest.spyOn(component, 'exportTableViewFile');

    jest.spyOn(mockChartExportCsvService, 'exportTableAsCsv').mockImplementation(() => of('response from service'));

    component.export();

    expect(component.exportTableViewFile).toHaveBeenCalled();

    expect(mockChartExportCsvService.exportTableAsCsv).toHaveBeenCalled();
  });

  it('should call onUpdateQCManageStatus when change manage status', () => {
    jest.spyOn(component, 'onUpdateQCManageStatus');

    component.onUpdateQCManageStatus();

    expect(component.onUpdateQCManageStatus).toHaveBeenCalled();
  });

  it('should filter reagents based on active channel ID', () => {
    const reagentInfos: ReagentInfo[] = MOCK_REAGENT;

    const activeChanelId = 1;

    const result = component.filterReagentInfo(reagentInfos, activeChanelId);

    expect(result.length).toBe(1);
    expect(result).toEqual(MOCK_REAGENT);
  });

  it('should return an empty array if no reagents match the active channel ID', () => {
    const reagentInfos: ReagentInfo[] = MOCK_REAGENT;

    const activeChanelId = 2;

    const result = component.filterReagentInfo(reagentInfos, activeChanelId);

    expect(result.length).toBe(0);
    expect(result).toEqual([]);
  });

  it('should filter reagents based on active channel ID', () => {
    const activeChannelId = 1;

    jest.spyOn(component, 'filterReagentInfo');

    jest.spyOn(mockAnalyzerService, 'getReagentEvents').mockReturnValue(of(MOCK_REAGENT));

    mockAnalyzerService
      .getReagentEvents({
        modelCode: component.analyzerParameter?.modelCode,
        serialNumber: component.analyzerParameter?.serialNumber,
        startDate: component.chartFilterParameter?.selectedDateRange.start,
        endDate: component.chartFilterParameter?.selectedDateRange.end,
      })
      .subscribe((reagentInfo: ReagentInfo[]) => {
        component.reagentInfo = component.filterReagentInfo(reagentInfo, activeChannelId);

        expect(component.filterReagentInfo).toHaveBeenCalledWith(reagentInfo, activeChannelId);
        expect(component.reagentInfo).toEqual(MOCK_REAGENT);
      });
  });

  it('should call initializeChannels on analyzerParameter change', () => {
    jest.spyOn(component, 'initializeChannels');

    const changes: SimpleChanges = {
      analyzerParameter: {
        currentValue: {
          modelCode: 'model1',
          serialNumber: '123456',
        },
        previousValue: null,
        firstChange: true,
        isFirstChange: () => true,
      },
    };

    component.ngOnChanges(changes);

    expect(component.initializeChannels).toHaveBeenCalled();
  });

  it('should call getChannelsWarning when setDataParameters is called', () => {
    jest.spyOn(component, 'fetchWarningsStatus');

    component.isServiceLevel3 = true;

    component.chartFilterParameter = {
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedFullLotNumbers: ['lot1'],
      selectedMaterialType: 'someMaterialType',
      selectedMaterialControlLotIds: ['controlLot1'],
      currentLevelLotNumber: 'currentLevelLot1',
    };

    component.updateChartFromCriteria();

    expect(component.fetchWarningsStatus).toHaveBeenCalled();
  });

  it('should call getChart when setDataParameters is called and viewMode is CHART', () => {
    jest.spyOn(component, 'getChart');

    component.viewMode = VIEW_MODE.CHART;
    component.isServiceLevel3 = true;
    component.chartFilterParameter = {
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedFullLotNumbers: ['lot1'],
      selectedMaterialType: 'someMaterialType',
      selectedMaterialControlLotIds: ['controlLot1'],
      currentLevelLotNumber: 'currentLevelLot1',
    };

    component.updateChartFromCriteria();

    expect(component.getChart).toHaveBeenCalled();
  });

  it('should call getChannelsWarningMultipleAnalyzers when chartMode is COMPARE_MULTIPLE_ANALYZERS', () => {
    jest.spyOn(mockChannelService, 'getChannelsWarningMultipleAnalyzers').mockReturnValue(of([]));

    component.chartMode = component.chartModeFilter.COMPARE_MULTIPLE_ANALYZERS;
    component.chartFilterParameter = {
      analyzers: [
        {
          modelCode: 'model1',
          serialNumber: '123456',
          siteCode: EMPTY,
          isPrimary: false,
        },
        {
          modelCode: 'model2',
          serialNumber: '654321',
          siteCode: EMPTY,
          isPrimary: false,
        },
      ],
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialControlLotIds: ['lot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedMaterialType: 'someMaterialType',
      selectedFullLotNumbers: ['fullLot1'],
      currentLevelLotNumber: 'currentLevelLot1',
    };

    component.fetchWarningsStatus();

    expect(mockChannelService.getChannelsWarningMultipleAnalyzers).toHaveBeenCalledWith(
      ['model1^123456', 'model2^654321'],
      'lot1',
      '2024-05-15',
      '2024-05-20',
    );
  });

  it('should call getChannelsWarning when chartMode is not COMPARE_MULTIPLE_ANALYZERS', () => {
    jest.spyOn(mockChannelService, 'getChannelsWarning').mockReturnValue(of([]));

    component.chartMode = component.chartModeFilter.ADVANCED_LJ_CHART;
    component.analyzerParameter = {
      modelCode: 'model1',
      serialNumber: '123456',
    };
    component.chartFilterParameter = {
      analyzers: [],
      selectedFullLotNumbers: ['lot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'someMaterialType',
      currentLevelLotNumber: 'currentLevelLot1',
      selectedMaterialControlLotIds: ['controlLot1'],
    };

    const result = {
      analyzerSerial: '123456',
      endDate: '2024-05-20',
      materialControlLotIds: ['controlLot1'],
      modelCode: 'model1',
      startDate: '2024-05-15',
    };

    component.fetchWarningsStatus();

    expect(mockChannelService.getChannelsWarning).toHaveBeenCalledWith(result);
  });

  it('should set filterParams using limitChartService when setFilterDataTableParameters is called', () => {
    const mockFilterParams = {
      param1: 'value1',
      param2: 'value2',
    };

    jest.spyOn(mockQcDataService, 'getFilterDataTableParameters').mockReturnValue(mockFilterParams);

    component.analyzerParameter = {
      modelCode: 'model1',
      serialNumber: '123456',
      site: {
        siteName: 'SYSMEX AMERICA CTR FOR LEARN (other)',
        address1: '1011 Woodlands Parkway',
        city: 'Vernon Hills',
        state: 'IL',
        postalCode: '60061',
        siteCode: '123',
      },
    };

    component.chartFilterParameter = {
      selectedFullLotNumbers: ['lot1'],
      selectedDateRange: {
        start: 'start1',
        end: 'end1',
      },
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'XN CHECK',
      selectedMaterialControlLotIds: ['controlLot1'],
      currentLevelLotNumber: 'currentLevelLot1',
    };

    component.setFilterDataTableParameters();

    expect(mockQcDataService.getFilterDataTableParameters).toHaveBeenCalledWith(
      component.analyzerParameter,
      component.chartFilterParameter,
      component.chartMode,
    );
    expect(component.filterParams).toEqual(mockFilterParams);
  });

  it('should call getMultipleAnalyzerComparisonData when chartMode is COMPARE_MULTIPLE_ANALYZERS', () => {
    jest.spyOn(component, 'getChartData');
    component.channels = [
      {
        channelId: 1,
        channelName: 'channel1',
        isWarning: false,
        isError: false,
        parameters: [
          {
            parameterId: 1,
            parameterCode: 'parameter1',
            parameterDisplayName: 'Parameter 1',
            unitDisplay: 'unit1',
            decimalPoint: 2,
          },
        ],
      },
    ];
    component.activeChannel = 'channel1';
    component.chartMode = component.chartModeFilter.COMPARE_MULTIPLE_ANALYZERS;
    component.chartFilterParameter = {
      selectedFullLotNumbers: ['lot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'someMaterialType',
      currentLevelLotNumber: 'currentLevelLot1',
      selectedMaterialControlLotIds: ['controlLot1'],
    };

    component.getChart();

    component.getChartData(1);

    expect(component.qcResults).toEqual([]);
  });

  it('should call initializeComparisonData and fetchAndProcessComparisonData when parameterList is not empty', () => {
    component.chartFilterParameter = {
      selectedFullLotNumbers: ['Lot1', 'Lot2'],
      selectedMaterialControlLotIds: [],
      currentLevelLotNumber: EMPTY,
      selectedMaterialType: EMPTY,
      selectedDateRange: {
        start: EMPTY,
        end: EMPTY,
      },
      selectedLevel1LotNumber: EMPTY,
      selectedLevel2LotNumber: EMPTY,
      selectedLevel3LotNumber: EMPTY,
    };

    component.limitData = [
      {
        parameterId: 1,
        parameterCode: 'parameter1',
        assayLimit: {
          target: 100,
          upperLimit: 110,
          lowerLimit: 90,
        },
        customerLimit: {
          target: 100,
          upperLimit: 110,
          lowerLimit: 90,
          sd: 10,
          cv: 10,
        },
        countryLimit: null,
        limitType: 'assay_limit',
      },
    ];

    component.limitDataPrevious = [
      {
        parameterId: 1,
        xbarmLimit: {
          target: 10,
          upperLimit: 15,
          lowerLimit: 5,
        },
        xbarmCustomerLimit: {
          target: 10,
          upperLimit: 15,
          lowerLimit: 5,
        },
        assayLimit: null,
        customerLimit: null,
        countryLimit: null,
        parameterCode: EMPTY,
      },
      {
        parameterId: 2,
        xbarmLimit: null,
        xbarmCustomerLimit: null,
        assayLimit: {
          target: 20,
          upperLimit: 25,
          lowerLimit: 15,
        },
        customerLimit: {
          target: 22,
          upperLimit: 24,
          lowerLimit: 18,
        },
        countryLimit: {
          target: 21,
          upperLimit: 23,
          lowerLimit: 19,
        },
        parameterCode: EMPTY,
      },
    ];

    const activeChannelId = 1;
    const parameterList: ParameterDateRangeInfo[] = [
      {
        conversionRate: 1,
        allSeriesInfo: [],
        analyzerSerial: '123456',
        channelId: 1,
        criteriaDateRange: {
          end: '2024-05-20',
          start: '2024-05-15',
        },
        decimalPoint: 2,
        limitsData: {
          assay: {
            lower: 90,
            target: 100,
            upper: 110,
          },
          country: undefined,
          customer: {
            lower: 90,
            target: 100,
            upper: 110,
          },
        },
        limitsDataPrevious: {
          assay: {
            lower: 90,
            target: 100,
            upper: 110,
          },
          country: undefined,
          customer: {
            lower: 90,
            target: 100,
            upper: 110,
          },
        },
        modelGroup: EMPTY,
        parameterCode: 'parameter1',
        parameterDisplayName: 'Parameter 1',
        parameterId: 1,
        parameterUnitId: 1,
        siteCode: '123',
        unitDisplay: 'unit1',
        isCountryLimitChart: false,
      },
    ];

    jest.spyOn(component, 'getParameterList').mockReturnValue(parameterList);
    jest.spyOn(component, 'initializeComparisonData');
    jest.spyOn(component, 'fetchAndProcessComparisonData');

    component.getChartData(activeChannelId);

    expect(component.getParameterList).toHaveBeenCalledWith(activeChannelId);
    expect(component.initializeComparisonData).toHaveBeenCalledWith(parameterList, activeChannelId);
    expect(component.fetchAndProcessComparisonData).toHaveBeenCalled();
  });

  it('should not call initializeComparisonData and fetchAndProcessComparisonData when parameterList is empty', () => {
    const activeChannelId = 1;

    jest.spyOn(component, 'getParameterList').mockReturnValue([]);
    jest.spyOn(component, 'initializeComparisonData');
    jest.spyOn(component, 'fetchAndProcessComparisonData');

    component.getChartData(activeChannelId);

    expect(component.getParameterList).toHaveBeenCalledWith(activeChannelId);
    expect(component.initializeComparisonData).not.toHaveBeenCalled();
    expect(component.fetchAndProcessComparisonData).not.toHaveBeenCalled();
  });

  it('should populate parameterMap and update qcMultipleComparisonData based on response', () => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const response = {
      '1': {
        previousLot: {
          dataPoints: [
            ['123', '123'],
            ['123', '123'],
          ],
        },
        currentLot: {
          dataPoints: [
            ['123', '123'],
            ['123', '123'],
          ],
        },
      },
      '2': {
        previousLot: {
          dataPoints: [
            ['123', '123'],
            ['123', '123'],
          ],
        },
        currentLot: {
          dataPoints: [
            ['123', '123'],
            ['123', '123'],
          ],
        },
      },
    } as unknown as Record<string, LotSeries>;

    const parameterList: ParameterDateRangeInfo[] = [];

    const analyzerEvents: AnalyzerEventInfo = {
      reagentEvents: [],
      calibrationEvents: [],
      serviceHistories: [],
    };

    component.getParameterMapPreviousLot(analyzerEvents, parameterList, [], 2, response, []);

    const expectedSeriesData = {
      previousLot: {
        pnts: [],
      },
      currentLot: {
        pnts: [],
      },
    };

    expect(component.qcMultipleComparisonData.some((data) => data.seriesData[0] === expectedSeriesData)).toBe(false);
  });

  it('should return an empty array when data is null or undefined', () => {
    const result = component.mapDataPointsToChartPnt([]);

    expect(result).toEqual([]);
  });

  it('should correctly map data points to chart points', () => {
    const data = [
      {
        '0': '1',
        '1': '101',
        '2': '5.5',
        '3': '100',
        '4': '110',
        '5': '90',
        '6': '100',
        '7': '2023-04-01',
        '8': 'True',
        '9': 'False',
        '10': 'True',
        '11': 'True',
        '12': 'False',
        '13': 'False',
        '14': 'True',
        '15': '0.1',
        '16': '0.5',
        '17': '1.0',
        '18': '0.0',
        '19': '0.0',
        '20': '0.0',
        '21': '0.0',
        '22': '0.0',
        '23': '0.0',
        '24': 'UTC+07:00',
        '25': '1.5',
        '26': '2.5',
        '27': '3.5',
      },
      {
        '0': '2',
        '1': '102',
        '2': '4.5',
        '3': '200',
        '4': '210',
        '5': '190',
        '6': '200',
        '7': '2023-04-02',
        '8': 'False',
        '9': 'True',
        '10': 'False',
        '11': 'True',
        '12': 'False',
        '13': 'False',
        '14': 'True',
        '15': '0.1',
        '16': '0.5',
        '17': '1.0',
        '18': '0.0',
        '19': '0.0',
        '20': '0.0',
        '21': '0.0',
        '22': '0.0',
        '23': '0.0',
        '24': 'UTC+07:00',
        '25': '1.5',
        '26': '2.5',
        '27': '3.5',
      },
    ];

    const expected = [
      {
        runDetailId: '1',
        runId: '101',
        lotNumber: '1',
        varPct: 5.5,
        origVal: 100,
        origUpp: 110,
        origLow: 90,
        origTgt: 100,
        qcDt: '2023-04-01+07:00',
        isCommented: true,
        isMngd: false,
        isPsd: true,
        isPsdAsL: true,
        isPsdCoL: false,
        isPsdCuL: false,
        isPsdMulrL: true,
        hasRuleValidated: data[0][12] != EMPTY || data[0][13] != EMPTY || data[0][14] != EMPTY,
        groupIndex: -1,
        assayLow: 0.1,
        assayTgt: 0.5,
        assayUpp: 1,
        customerLowerLimit: 1.5,
        customerTarget: 2.5,
        customerUpperLimit: 3.5,
        multiRuleLow2Sd: 0,
        multiRuleLow3Sd: 0,
        multiRuleTgt2Sd: 0,
        multiRuleTgt3Sd: 0,
        multiRuleUpp2Sd: 0,
        multiRuleUpp3Sd: 0,
      },
      {
        runDetailId: '2',
        runId: '102',
        lotNumber: '1',
        varPct: 4.5,
        origVal: 200,
        origUpp: 210,
        origLow: 190,
        origTgt: 200,
        qcDt: '2023-04-02+07:00',
        isCommented: false,
        isMngd: true,
        isPsd: false,
        isPsdAsL: true,
        isPsdCoL: false,
        isPsdCuL: false,
        isPsdMulrL: true,
        hasRuleValidated: data[1][12] != EMPTY || data[1][13] != EMPTY || data[1][14] != EMPTY,
        groupIndex: -1,
        assayLow: 0.1,
        assayTgt: 0.5,
        assayUpp: 1,
        customerLowerLimit: 1.5,
        customerTarget: 2.5,
        customerUpperLimit: 3.5,
        multiRuleLow2Sd: 0,
        multiRuleLow3Sd: 0,
        multiRuleTgt2Sd: 0,
        multiRuleTgt3Sd: 0,
        multiRuleUpp2Sd: 0,
        multiRuleUpp3Sd: 0,
      },
    ];

    const result = component.mapDataPointsToChartPnt(data, 1);

    expect(result).toEqual(expected);
  });

  it('should return series info for all selected lots', () => {
    // Mock the chartFilterParameter property on the component
    component.chartFilterParameter = {
      selectedFullLotNumbers: ['Lot1', 'Lot2'],
      selectedMaterialControlLotIds: [],
      currentLevelLotNumber: EMPTY,
      selectedMaterialType: EMPTY,
      selectedDateRange: {
        start: EMPTY,
        end: EMPTY,
      },
      selectedLevel1LotNumber: EMPTY,
      selectedLevel2LotNumber: EMPTY,
      selectedLevel3LotNumber: EMPTY,
    };

    const expected = [
      {
        label: 'Level 1',
        name: 'previousLot',
        isBold: false,
        isDashedStyle: true,
        color: 'var(--red-100)',
        pointIcon: 'circle',
        level: 1,
      },
      {
        label: 'Level 2',
        name: 'currentLot',
        isBold: false,
        isDashedStyle: false,
        color: 'var(--green-100)',
        pointIcon: 'square',
        level: 2,
      },
    ];

    const result = component.getAllSeriesPreviousLotInfo();

    expect(result).toEqual(expected);
  });

  it('should call getPreviousLotComparisonData with correct parameters', () => {
    component.analyzerParameter = {
      modelCode: 'Model1',
      serialNumber: 'SN123',
    };
    component.chartFilterParameter = {
      selectedMaterialControlLotIds: ['Lot1', 'Lot2'],
      selectedDateRange: {
        start: '2021-01-01',
        end: '2021-01-31',
      },
      currentLevelLotNumber: 'Lot2',
      previousLevelLotNumber: 'Lot1',
      selectedMaterialType: 'XN CHECK',
      selectedFullLotNumbers: [],
      selectedLevel1LotNumber: EMPTY,
      selectedLevel2LotNumber: EMPTY,
      selectedLevel3LotNumber: EMPTY,
      analyzers: [],
    };

    const parameterCriteriaMock: ParameterCriteriaDateRange[] = [
      {
        conversionRate: 1,
        criteriaDateRange: {
          start: '2021-01-01',
          end: '2021-01-31',
        },

        channelId: 1,
        parameterUnitId: 1,
        parameterId: 0,
        parameterCode: EMPTY,
        parameterDisplayName: EMPTY,
        unitDisplay: EMPTY,
        decimalPoint: 0,
        allSeriesInfo: [],
        siteCode: EMPTY,
        modelGroup: EMPTY,
        analyzerSerial: EMPTY,
        isCountryLimitChart: false,
      },
    ];

    component.apiZScoreAndPreviousCall(2, parameterCriteriaMock);

    expect(mockQcDataService.getPreviousLotComparisonData).toHaveBeenCalledWith({
      modelCode: 'Model1',
      serialNumber: 'SN123',
      currentLotId: 'Lot2',
      previousLotId: 'Lot1',
      parameterUnitIds: '1',
      startDate: '2021-01-01',
      endDate: '2021-01-31',
    });
  });

  it('should call updateStyleWhileExporting when event is "exporting"', () => {
    component.mapChartContainerStyle('exporting');

    expect(component.chartFunctions.hasLegend).toBe(true);
  });

  it('should call removeStyleAfterExport when event is not "exporting"', () => {
    component.mapChartContainerStyle('notexporting');

    expect(component.chartFunctions.hasLegend).toBe(true);
  });

  it('should call onExportMutlipleAnalyzer if chartMode is COMPARE_MULTIPLE_ANALYZERS', () => {
    component.chartMode = CHART_MODE.COMPARE_MULTIPLE_ANALYZERS;

    jest.spyOn(component, 'openDialogExport');

    component.openDialogExport();

    expect(component.openDialogExport).toHaveBeenCalled();
  });

  it('should call exportTableViewFile if viewMode is TABLE', () => {
    component.chartMode = CHART_MODE.SIMPLE_CHART;

    component.viewMode = VIEW_MODE.TABLE;

    jest.spyOn(component, 'exportTableViewFile');

    component.export();
    expect(component.exportTableViewFile).toHaveBeenCalled();
  });

  it('should call exportAdvanceChartPdf if viewMode is CHART', () => {
    const loadingService: LoadingService = new LoadingService();
    const sysmexService: SysmexService = new SysmexService('en-US');
    const httpClientMock = TestBed.inject(HttpClient);
    const analyzerDetailExportPdfService: ChartExportPdfService = new ChartExportPdfService(
      sysmexService,
      new PdfFontService(httpClientMock),
    );

    component.chartExport = new ChartExportComponent(loadingService, analyzerDetailExportPdfService);

    component.chartMode = CHART_MODE.SIMPLE_CHART;

    component.viewMode = VIEW_MODE.CHART;

    jest.spyOn(component, 'exportAdvanceChartPdf');

    component.export();

    expect(component.exportPdfParams).toBeDefined();
  });

  it('should return true if multiComparisonData is not empty', () => {
    const multiComparisonData = { key: 'value' };

    expect(component.isMultiComparisonDataNotEmpty(multiComparisonData)).toBe(true);
  });

  it('should return false if multiComparisonData is empty', () => {
    const multiComparisonData = {};

    expect(component.isMultiComparisonDataNotEmpty(multiComparisonData)).toBe(false);
  });

  it('should return true if channel is warning', () => {
    component.channels = [
      {
        channelId: 1,
        isWarning: true,
        isError: false,
        channelName: EMPTY,
      },
    ];

    const result = component.isChannelNotValidate(1);

    expect(result).toBe(true);
  });

  it('should return false if channel is not warning and isServiceLevel3 is true', () => {
    component.channelsWarningTrue = [
      {
        channelId: 1,
        isWarning: false,
        isPassedCustomerLimit: true,
        channelName: EMPTY,
      },
    ];
    component.isServiceLevel3 = true;

    const result = component.isChannelNotValidate(1);

    expect(result).toBe(false);
  });

  it('should return true if channel is not warning and isServiceLevel3 and isPassedCustomerLimit is false', () => {
    component.channelsWarningTrue = [
      {
        channelId: 1,
        isWarning: false,
        isPassedCustomerLimit: false,
        channelName: EMPTY,
      },
    ];
    component.isServiceLevel3 = false;

    const result = component.isChannelNotValidate(1);

    expect(result).toBe(false);
  });

  it('should return false if channel is not found', () => {
    component.channelsWarningTrue = [];
    component.isServiceLevel3 = true;

    const result = component.isChannelNotValidate(1);

    expect(result).toBe(false);
  });

  it('should call getQCManageStatus when onUpdateQCManageStatus is called', () => {
    component.activeChannel = '1';
    component.channels = [
      {
        channelId: 1,
        channelName: '1',
        isWarning: false,
        isError: false,
        parameters: [
          {
            parameterId: 1,
            parameterCode: 'parameter1',
            parameterDisplayName: 'Parameter 1',
            unitDisplay: 'unit1',
            decimalPoint: 2,
          },
        ],
      },
    ];

    component.analyzerParameter = {
      modelCode: 'Model1',
      serialNumber: 'SN123',
    };

    component.chartFilterParameter = {
      selectedMaterialControlLotIds: ['Lot1', 'Lot2'],
      selectedDateRange: {
        start: '2021-01-01',
        end: '2021-01-31',
      },
      currentLevelLotNumber: 'Lot2',
      previousLevelLotNumber: 'Lot1',
      selectedMaterialType: 'XN CHECK',
      selectedFullLotNumbers: [],
      selectedLevel1LotNumber: EMPTY,
      selectedLevel2LotNumber: EMPTY,
      selectedLevel3LotNumber: EMPTY,
      analyzers: [],
    };

    jest.spyOn(mockQcDataService, 'getQCManageStatus').mockReturnValue(of([]));

    component.onUpdateQCManageStatus();

    expect(mockQcDataService.getQCManageStatus).toHaveBeenCalled();

    expect(component.statisticalData).toEqual([]);
  });

  it('should update chartFilterParameter and call updateChartFromCriteria for EventAdvancedFilterChange', async () => {
    const mockFilterParameter = {
      selectedMaterialType: 'someMaterialType',
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedMaterialControlLotIds: ['controlLot1'],
      selectedFullLotNumbers: ['fullLot1'],
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      someFilter: 'value',
    };
    const event: EventParam = new EventParam(new EventAdvancedFilterChange(mockFilterParameter));

    jest.spyOn(component, 'updateChartFromCriteria');

    component.onEvent(event);

    expect(component.chartFilterParameter).toEqual(mockFilterParameter);
    expect(component.updateChartFromCriteria).toHaveBeenCalled();
  });

  it('should update for ChartCriteriaChange event', async () => {
    const mockFilterParameter: ComparingChartFilterParameter = {
      selectedMaterialType: 'someMaterialType',
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedMaterialControlLotIds: ['controlLot1'],
      selectedFullLotNumbers: ['fullLot1'],
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      currentLevelLotNumber: EMPTY,
      analyzers: [],
      limitData: [],
    };
    const event: EventParam = new EventParam(new ChartCriteriaEvent(mockFilterParameter));

    jest.spyOn(component, 'updateChartFromCriteria');

    component.onEvent(event);

    expect(component.chartFilterParameter).toEqual(mockFilterParameter);
    expect(component.analyzerList).toEqual([]);
    expect(component.limitData).toEqual([]);
    expect(component.updateChartFromCriteria).toHaveBeenCalled();
  });

  it('should not call onEvent when event is null', async () => {
    const event: EventParam = new EventParam();

    component.onEvent(event);

    expect(component.chartFilterParameter).toBeUndefined();
  });

  it('should call getPreviousLotChannelsWarning when chartMode is COMPARE_TO_PREVIOUS_LOT', () => {
    const params: ChannelWarningRequest = {
      modelCode: 'model1',
      analyzerSerial: '123456',
      materialControlLotIds: ['controlLot1'],
      startDate: '2024-05-15',
      endDate: '2024-05-20',
    };

    component.chartMode = CHART_MODE.COMPARE_TO_PREVIOUS_LOT;
    component.analyzerParameter = {
      modelCode: 'model1',
      serialNumber: '123456',
    };
    component.chartFilterParameter = {
      analyzers: [],
      selectedFullLotNumbers: ['lot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'someMaterialType',
      currentLevelLotNumber: 'currentLevelLot1',
      selectedMaterialControlLotIds: ['controlLot1'],
    };

    component.isServiceLevel3 = false;

    component.limitData = [
      {
        parameterId: 1,
        xbarmLimit: {
          target: 10,
          upperLimit: 15,
          lowerLimit: 5,
        },
        xbarmCustomerLimit: {
          target: 10,
          upperLimit: 15,
          lowerLimit: 5,
        },
        assayLimit: null,
        customerLimit: null,
        countryLimit: null,
        parameterCode: EMPTY,
        unitDisplay: '123',
      },
      {
        parameterId: 2,
        xbarmLimit: null,
        xbarmCustomerLimit: null,
        assayLimit: {
          target: 20,
          upperLimit: 25,
          lowerLimit: 15,
        },
        customerLimit: {
          target: 22,
          upperLimit: 24,
          lowerLimit: 18,
        },
        countryLimit: {
          target: 21,
          upperLimit: 23,
          lowerLimit: 19,
        },
        parameterCode: EMPTY,
        unitDisplay: '123',
      },
    ];

    jest.spyOn(mockChannelService, 'getPreviousLotChannelsWarning').mockReturnValue(of([]));

    component.fetchWarningsStatus();

    expect(mockChannelService.getPreviousLotChannelsWarning).toHaveBeenCalledWith(params);
  });

  it('should get all series info', () => {
    component.analyzerList = [
      {
        modelCode: 'Model1',
        serialNumber: '123456',
        siteCode: EMPTY,
        isPrimary: false,
      },
      {
        modelCode: 'Model2',
        serialNumber: '654321',
        siteCode: EMPTY,
        isPrimary: false,
      },
    ];

    component.chartFilterParameter = {
      selectedMaterialControlLotIds: ['controlLot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      currentLevelLotNumber: '3312',
      previousLevelLotNumber: '3314',
      selectedMaterialType: 'someMaterialType',
      selectedFullLotNumbers: ['fullLot1'],
      selectedLevel1LotNumber: EMPTY,
      selectedLevel2LotNumber: EMPTY,
      selectedLevel3LotNumber: EMPTY,
    };

    const result = component.getAllSeriesInfo();

    expect(result).toEqual([
      {
        label: 'Model1/ #123456',
        name: 'Model1123456',
        isBold: false,
        isDashedStyle: false,
        color: '#864B99',
        pointIcon: 'circle',
        level: 1,
      },
      {
        label: 'Model2/ #654321',
        name: 'Model2654321',
        isBold: false,
        isDashedStyle: false,
        color: '#DBB62E',
        pointIcon: 'circle',
        level: 1,
      },
    ]);
  });

  it('shouuld not call initializeChannels when analyzerParameter is null', () => {
    component.analyzerParameter.modelCode = EMPTY;
    jest.spyOn(component, 'initializeChannels');
    expect(component.initializeChannels).not.toHaveBeenCalled();
  });

  it('should add exporting class to chartContainer when event is exporting', () => {
    component.chartMode = CHART_MODE.COMPARE_TO_XBARM;
    component.chartContainer = {
      nativeElement: {
        classList: {
          add: jest.fn(),
          remove: jest.fn(),
        },
      },
    };

    component.mapChartContainerStyle('exporting');

    expect(component.chartContainer.nativeElement.classList.add).toHaveBeenCalledWith('exporting');
    expect(component.chartContainer.nativeElement.classList.add).toHaveBeenCalledWith('exporting-xbarm-chart');
    expect(component.chartContainer.nativeElement.classList.remove).not.toHaveBeenCalled();
  });

  it('should remove exporting class from chartContainer when event is not exporting', () => {
    component.chartMode = CHART_MODE.COMPARE_TO_XBARM;
    component.chartContainer = {
      nativeElement: {
        classList: {
          add: jest.fn(),
          remove: jest.fn(),
        },
      },
    };

    component.mapChartContainerStyle('not-exporting');

    expect(component.chartContainer.nativeElement.classList.remove).toHaveBeenCalledWith('exporting');
    expect(component.chartContainer.nativeElement.classList.remove).toHaveBeenCalledWith('exporting-xbarm-chart');
    expect(component.chartContainer.nativeElement.classList.add).not.toHaveBeenCalled();
  });

  it('should not throw error if chartContainer is undefined', () => {
    component.chartContainer = undefined;

    expect(() => component.mapChartContainerStyle('exporting')).not.toThrow();
  });

  it('should set viewMode to CHART, set activeChannel, and call getChart', () => {
    component.channels = [
      {
        channelId: 1,
        channelName: 'Channel1',
        isWarning: false,
        isError: false,
        parameters: [],
      },
      {
        channelId: 2,
        channelName: 'Channel2',
        isWarning: false,
        isError: false,
        parameters: [],
      },
    ];

    jest.spyOn(component, 'getChart');

    component.chartFilterParameter = MOCK_FILTER_PARAMS;
    component.viewChart();

    expect(component.viewMode).toBe(VIEW_MODE.CHART);
    expect(component.activeChannel).toBe('Channel1');
    expect(component.getChart).toHaveBeenCalled();
  });

  describe('onToggleChartReagentDisplay', () => {
    it('should update isShowReagent and chartFunctions.hasReagentChanges based on the checked parameter', () => {
      const initialChartFunctions = component.chartFunctions;

      // Test with checked = true
      component.onToggleChartReagentDisplay(true);
      expect(component.isShowReagent).toBe(true);
      expect(component.chartFunctions.hasReagentChanges).toBe(true);
      expect(component.chartFunctions).toEqual({
        ...initialChartFunctions,
        hasReagentChanges: true,
      });

      // Resetting chartFunctions to initial state for the next test
      component.chartFunctions = initialChartFunctions;

      // Test with checked = false
      component.onToggleChartReagentDisplay(false);
      expect(component.isShowReagent).toBe(false);
      expect(component.chartFunctions.hasReagentChanges).toBe(false);
      expect(component.chartFunctions).toEqual({
        ...initialChartFunctions,
        hasReagentChanges: false,
      });
    });
  });

  describe('getParameterCodeByUnitId', () => {
    it('should return the parameter code for the given unit ID', () => {
      const parameterList: ParameterDateRangeInfo[] = [
        {
          conversionRate: 1,
          parameterUnitId: 1,
          parameterCode: 'Code1',
          channelId: 0,
          parameterId: 0,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
        {
          conversionRate: 1,
          parameterUnitId: 2,
          parameterCode: 'Code2',
          channelId: 0,
          parameterId: 0,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
        {
          conversionRate: 1,
          parameterUnitId: 3,
          parameterCode: 'Code3',
          channelId: 0,
          parameterId: 0,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
      ];
      const result = component.getParameterCodeByUnitId(parameterList, 2);

      expect(result).toBe('Code2');
    });

    it('should return EMPTY when the unit ID is not found', () => {
      const parameterList: ParameterDateRangeInfo[] = [
        {
          conversionRate: 1,
          parameterUnitId: 1,
          parameterCode: 'Code1',
          channelId: 0,
          parameterId: 0,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
        {
          conversionRate: 1,
          parameterUnitId: 2,
          parameterCode: 'Code2',
          channelId: 0,
          parameterId: 0,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
        {
          conversionRate: 1,
          parameterUnitId: 3,
          parameterCode: 'Code3',
          channelId: 0,
          parameterId: 0,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
      ];
      const result = component.getParameterCodeByUnitId(parameterList, 4);

      expect(result).toBe(EMPTY); // Assuming EMPTY is a predefined constant for an empty string or similar
    });
  });

  describe('getReagentEventsForParameter', () => {
    it('should return reagent events for the specified parameter code and active channel ID', () => {
      const analyzerEvent: AnalyzerEventInfo = {
        reagentEvents: [
          {
            channelId: 1,
            parameters: [
              {
                parameterCode: 'Code1',
                reagentEvents: [],
              },
              {
                parameterCode: 'Code2',
                reagentEvents: [],
              },
            ],
          },
          {
            channelId: 2,
            parameters: [
              {
                parameterCode: 'Code1',
                reagentEvents: [],
              },
              {
                parameterCode: 'Code2',
                reagentEvents: [],
              },
            ],
          },
        ],
        calibrationEvents: [],
        serviceHistories: [],
      };
      const parameterCode = 'Code1';
      const activeChannelId = 1;
      const result = component.getReagentEventsForParameter(analyzerEvent, parameterCode, activeChannelId);

      expect(result).toEqual([]);
    });

    it('should return an empty array if no reagent events', () => {
      const analyzerEvent: AnalyzerEventInfo = {
        reagentEvents: [
          {
            channelId: 1,
            parameters: [
              {
                parameterCode: 'Code1',
                reagentEvents: [],
              },
              {
                parameterCode: 'Code2',
                reagentEvents: [],
              },
            ],
          },
          {
            channelId: 2,
            parameters: [
              {
                parameterCode: 'Code1',
                reagentEvents: [],
              },
              {
                parameterCode: 'Code2',
                reagentEvents: [],
              },
            ],
          },
        ],
        calibrationEvents: [],
        serviceHistories: [],
      };
      const parameterCode = 'Code1';
      const activeChannelId = 1;
      const result = component.getReagentEventsForParameter(analyzerEvent, parameterCode, activeChannelId);

      expect(result).toEqual([]);
    });
  });

  describe('getParameterMapPreviousLot', () => {
    it('should correctly populate comparisonData with seriesData and hasDataPoint', () => {
      // Step 1: Mock inputs
      const analyzerEvent: AnalyzerEventInfo = {
        reagentEvents: [],
        calibrationEvents: [],
        serviceHistories: [],
      }; // Simplified for the example
      const parameterList: ParameterDateRangeInfo[] = [
        {
          conversionRate: 1,
          parameterUnitId: 1,
          parameterCode: 'Code1',
          channelId: 0,
          parameterId: 0,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
      ];
      const comparisonData: QcComparisonData[] = [
        {
          parameterUnitId: 1,
          seriesData: [],
          hasDataPoint: false,
          paramDateRange: {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 0,
            parameterId: 0,
            parameterCode: EMPTY,
            parameterDisplayName: EMPTY,
            unitDisplay: EMPTY,
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-05-15',
              end: '2024-05-20',
            },
            allSeriesInfo: [],
            siteCode: EMPTY,
            modelGroup: EMPTY,
            analyzerSerial: EMPTY,
            isCountryLimitChart: false,
          },
          channelId: 0,
        },
      ];
      const activeChannelId = 1;
      const response: Record<string, LotSeries> = {
        '1': {
          previousLot: {
            dataPoints: [
              /* Some data points */
            ],
            level: 0,
            lotNumber: 0,
            statisticValue: {},
          },
          currentLot: {
            dataPoints: [
              /* Some data points */
            ],
            level: 0,
            lotNumber: 0,
            statisticValue: {},
          },
        },
      };

      // Step 2: Mock dependent methods
      jest.spyOn(component, 'getParameterCodeByUnitId').mockReturnValue('Code1');
      jest.spyOn(component, 'getReagentEventsForParameter').mockReturnValue([]);
      jest.spyOn(component, 'mapDataPointsToChartPnt').mockReturnValue([]);

      // Step 3: Call the method
      const updatedComparisonData = component.getParameterMapPreviousLot(
        analyzerEvent,
        parameterList,
        comparisonData,
        activeChannelId,
        response,
        [],
      );

      // Step 4: Verify the results
      expect(updatedComparisonData[0].seriesData).toBeDefined();
      expect(updatedComparisonData[0].hasDataPoint).toBe(false);
    });
  });

  describe('getParameterMapMulti', () => {
    it('should correctly populate comparisonData with seriesData and hasDataPoint based on response', () => {
      component.chartFilterParameter = MOCK_FILTER_PARAMS;

      // Mock inputs
      const comparisonData: QcComparisonData[] = [
        {
          parameterUnitId: 1,
          seriesData: [],
          hasDataPoint: false,
          paramDateRange: {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 1,
            parameterId: 0,
            parameterCode: EMPTY,
            parameterDisplayName: EMPTY,
            unitDisplay: EMPTY,
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-05-15',
              end: '2024-05-20',
            },
            allSeriesInfo: [],
            siteCode: EMPTY,
            modelGroup: EMPTY,
            analyzerSerial: EMPTY,
            isCountryLimitChart: false,
          },
          channelId: 0,
        },
        {
          parameterUnitId: 2,
          seriesData: [],
          hasDataPoint: false,
          paramDateRange: {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 1,
            parameterId: 0,
            parameterCode: EMPTY,
            parameterDisplayName: EMPTY,
            unitDisplay: EMPTY,
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-05-15',
              end: '2024-05-20',
            },
            allSeriesInfo: [],
            siteCode: EMPTY,
            modelGroup: EMPTY,
            analyzerSerial: EMPTY,
            isCountryLimitChart: false,
          },
          channelId: 0,
        },
      ];
      const chartPoint: ChartPnt = {
        varPct: 0,
        origVal: 2,
        origUpp: 4,
        origLow: 1,
        origTgt: 3,
        qcDt: '2020-12-01T00:00:00',
        isMngd: false,
        isPsd: false,
        isPsdAsL: false,
        isPsdCuL: true,
        isPsdCoL: true,
        isPsdMulrL: true,
        tZ: 'UTC+00:00',
        groupIndex: 0,
      };
      const response: MultiComparisonDataResponse<ChartPnt> = {
        parameterUnits: [
          {
            parameterUnitId: 1,
            analyzers: {
              analyzer1: {
                pnts: [chartPoint],
              },
              analyzer2: {
                pnts: [
                  {
                    ...chartPoint,
                    isPsdCuL: true,
                  },
                  {
                    ...chartPoint,
                    isPsdCuL: false,
                    isPsdMulrL: true,
                  },
                  {
                    ...chartPoint,
                    isPsdCuL: false,
                    isPsdCoL: false,
                  },
                ],
              },
            },
          },
          {
            parameterUnitId: 2,
            analyzers: {
              analyzer3: {
                pnts: [],
              },
            },
          },
        ],
        channelId: 0,
      };

      const parameterInLimit: ParameterCriteriaDateRange[] = [
        {
          conversionRate: 1,
          parameterUnitId: 1,
          criteriaDateRange: {
            start: '2021-01-01',
            end: '2021-12-31',
          },
          channelId: 0,
          parameterId: 0,
          parameterCode: EMPTY,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
      ];

      // Call the method
      const updatedComparisonData = component.getParameterMapMulti(comparisonData, response, parameterInLimit);

      // Verify the results
      // For parameterUnitId 1, seriesData should be populated and hasDataPoint should be true
      expect(updatedComparisonData[0].seriesData).toEqual([
        {
          analyzer1: {
            pnts: [
              {
                ...chartPoint,
                hasRuleValidated: true,
                qcDt: '2020-12-01T00:00:00+00:00',
                lotNumber: '3312',
              },
            ],
          },
          analyzer2: {
            pnts: [
              {
                ...chartPoint,
                isPsdCuL: true,
                isPsdMulrL: true,
                hasRuleValidated: true,
                qcDt: '2020-12-01T00:00:00+00:00',
                lotNumber: '3312',
              },
              {
                ...chartPoint,
                isPsdCuL: false,
                isPsdMulrL: true,
                hasRuleValidated: true,
                qcDt: '2020-12-01T00:00:00+00:00',
                lotNumber: '3312',
              },
              {
                ...chartPoint,
                isPsdCuL: false,
                isPsdCoL: false,
                hasRuleValidated: true,
                qcDt: '2020-12-01T00:00:00+00:00',
                lotNumber: '3312',
              },
            ],
          },
        },
      ]);

      expect(updatedComparisonData[0].hasDataPoint).toBe(false);

      // For parameterUnitId 2, seriesData should remain empty and hasDataPoint should be false
      // because parameterUnitId 2 is not present in response.parameterUnits
      expect(updatedComparisonData[1].seriesData).toEqual([]);
      expect(updatedComparisonData[1].hasDataPoint).toBe(false);
    });
  });

  it('apiToXBarMFirstChartCall should call qcDataService with correct parameters', () => {
    component.analyzerParameter = {
      modelCode: 'model123',
      serialNumber: 'serial123',
    };

    component.chartFilterParameter = {
      selectedFullLotNumbers: ['lot1'],
      selectedDateRange: {
        start: 'start1',
        end: 'end1',
      },
      currentLevelLotNumber: '3312',
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'XN CHECK',
      selectedMaterialControlLotIds: ['controlLot1'],
    };

    const parameterCriteria: ParameterCriteriaDateRange[] = [
      {
        conversionRate: 1,
        parameterUnitId: 1,
        criteriaDateRange: {
          start: EMPTY,
          end: EMPTY,
        },
        channelId: 0,
        parameterId: 0,
        parameterCode: EMPTY,
        parameterDisplayName: EMPTY,
        unitDisplay: EMPTY,
        decimalPoint: 0,
        allSeriesInfo: [],
        siteCode: EMPTY,
        modelGroup: EMPTY,
        analyzerSerial: EMPTY,
        isCountryLimitChart: false,
      },
      {
        conversionRate: 1,
        parameterUnitId: 2,
        criteriaDateRange: {
          start: EMPTY,
          end: EMPTY,
        },
        channelId: 0,
        parameterId: 0,
        parameterCode: EMPTY,
        parameterDisplayName: EMPTY,
        unitDisplay: EMPTY,
        decimalPoint: 0,
        allSeriesInfo: [],
        siteCode: EMPTY,
        modelGroup: EMPTY,
        analyzerSerial: EMPTY,
        isCountryLimitChart: false,
      },
    ];
    const expectedParams = {
      modelCode: 'model123',
      serialNumber: 'serial123',
      materialControlLotIds: 'controlLot1',
      parameterUnitIds: '1,2',
      startDate: 'start1',
      endDate: 'end1',
    };

    component.apiToXBarMFirstChartCall(parameterCriteria);

    expect(mockQcDataService.getToXBarMComparisonQcData).toHaveBeenCalledWith(expectedParams);
  });

  it('apiToXBarMSecondChartCall should call qcDataService with correct parameters', () => {
    component.analyzerParameter = {
      modelCode: 'model123',
      serialNumber: 'serial123',
    };

    component.chartFilterParameter = {
      selectedFullLotNumbers: ['lot1'],
      selectedDateRange: {
        start: 'start1',
        end: 'end1',
      },
      currentLevelLotNumber: '3312',
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'XN CHECK',
      selectedMaterialControlLotIds: ['controlLot1'],
    };

    const parameterCriteria: ParameterCriteriaDateRange[] = [
      {
        conversionRate: 1,
        parameterUnitId: 1,
        criteriaDateRange: {
          start: EMPTY,
          end: EMPTY,
        },
        channelId: 0,
        parameterId: 0,
        parameterCode: EMPTY,
        parameterDisplayName: EMPTY,
        unitDisplay: EMPTY,
        decimalPoint: 0,
        allSeriesInfo: [],
        siteCode: EMPTY,
        modelGroup: EMPTY,
        analyzerSerial: EMPTY,
        isCountryLimitChart: false,
      },
      {
        conversionRate: 1,
        parameterUnitId: 2,
        criteriaDateRange: {
          start: EMPTY,
          end: EMPTY,
        },
        channelId: 0,
        parameterId: 0,
        parameterCode: EMPTY,
        parameterDisplayName: EMPTY,
        unitDisplay: EMPTY,
        decimalPoint: 0,
        allSeriesInfo: [],
        siteCode: EMPTY,
        modelGroup: EMPTY,
        analyzerSerial: EMPTY,
        isCountryLimitChart: false,
      },
    ];
    const expectedParams = {
      siteCode: undefined,
      modelCode: 'model123',
      serialNumber: 'serial123',
      parameterUnitIds: '1,2',
      startDate: 'start1',
      endDate: 'end1',
    };

    component.apiToXBarMSecondChartCall(parameterCriteria);

    expect(mockQcDataService.getToXBarMData).toHaveBeenCalledWith(expectedParams);
  });

  it('should map comparison data correctly with both responses', () => {
    const comparisonData: QcComparisonData[] = [
      {
        parameterUnitId: 1,
        seriesData: [{}, {}],
        hasDataPoint: false,
        chartIndex: 0,
        paramDateRange: {
          conversionRate: 1,
          channelId: 0,
          parameterUnitId: 1,
          parameterId: 0,
          parameterCode: EMPTY,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: EMPTY,
            end: EMPTY,
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
        channelId: 0,
      },
      {
        parameterUnitId: 2,
        seriesData: [{}, {}],
        hasDataPoint: false,
        chartIndex: 1,
        paramDateRange: {
          conversionRate: 1,
          channelId: 0,
          parameterUnitId: 1,
          parameterId: 0,
          parameterCode: EMPTY,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: EMPTY,
            end: EMPTY,
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
        channelId: 0,
      },
    ];
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const firstResponse = {
      '1': {
        levels: [
          {
            level: 1,
            dataPoints: [
              [
                '1',
                '2',
                '50',
                '100',
                '120',
                '80',
                '100',
                '2020-01-01',
                'True',
                'False',
                '2020-01-01',
                'True',
                'UTC+07:00',
              ],
            ],
            statistics: 100,
          },
        ],
      },

      '2': {
        levels: [
          {
            level: 2,
            dataPoints: [
              [
                '1',
                '2',
                '50',
                '100',
                '120',
                '80',
                '100',
                '2020-01-01',
                'True',
                'False',
                '2020-01-01',
                'True',
                'UTC+07:00',
              ],
            ],
            statistics: 200,
          },
        ],
      },
    } as unknown as Record<string, LevelSeries>;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const secondResponse = {
      1: JSON.stringify({
        dataPoints: [
          ['1', '100', '2024-12-01T15:50:00', 'True', 'False', 'True', '1', '1', '1', '1', '1', '1', 'UTC+00:00'],
        ],
        statistics: 300,
      }),

      2: JSON.stringify({
        dataPoints: [
          ['1', '100', '2024-12-01T15:50:00', 'True', 'False', 'True', '1', '1', '1', '1', '1', '1', 'UTC+00:00'],
        ],
        statistics: 400,
      }),
    } as unknown as Record<string, DataSeries>;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const parameterInLimit = [
      {
        parameterUnitId: 1,
        startDate: '2020-01-01',
        endDate: '2020-01-31',
      },
      {
        parameterUnitId: 2,
        startDate: '2020-02-01',
        endDate: '2020-02-29',
      },
    ] as unknown as ParameterCriteriaDateRange[];

    const result = component.getParameterMapToXBarM(comparisonData, firstResponse, secondResponse, parameterInLimit);

    expect(result).toBeDefined();
  });

  it('should handle empty secondResponse correctly', () => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const comparisonData = [
      {
        parameterUnitId: 1,
        seriesData: [{}, {}],
        hasDataPoint: false,
        chartIndex: 0,
      },
    ] as QcComparisonData[];
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const firstResponse = {
      '1': {
        levels: [
          {
            level: 1,
            dataPoints: [],
            statistics: 100,
          },
        ],
      },
    } as unknown as Record<string, LevelSeries>;
    const secondResponse = {};
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const parameterInLimit = [
      {
        parameterUnitId: 1,
        startDate: '2020-01-01',
        endDate: '2020-01-31',
      },
    ] as unknown as ParameterCriteriaDateRange[];

    const result = component.getParameterMapToXBarM(comparisonData, firstResponse, secondResponse, parameterInLimit);

    expect(result).toBeDefined();
  });

  it('should correctly map data points to ChartPnt format', () => {
    const data = [
      {
        '0': '1',
        '1': '2',
        '2': '50',
        '3': '100',
        '4': '120',
        '5': '80',
        '6': '100',
        '7': '2020-01-01T12:00:00',
        '8': 'True',
        '9': 'False',
        '10': '2020-01-01T12:00:00',
        '11': 'True',
        '12': 'UTC+07:00',
      },
    ];

    const expected = [
      {
        runDetailId: '1',
        runId: '2',
        lotNumber: '1',
        varPct: 50,
        origVal: 100,
        origUpp: 120,
        origLow: 80,
        origTgt: 100,
        qcDt: '2020-01-01T12:00:00+07:00',
        isCommented: true,
        isMngd: false,
        qcSet: '2020-01-01T12:00:00+07:00',
        isPsd: true,
        isPsdAsL: true,
        isPsdCuL: true,
        isPsdCoL: true,
        groupIndex: -1,
      },
    ];

    const result = component.mapDataPointsToFirstChartPnt(data, 1);

    expect(result).toEqual(expected);
  });

  it('should correctly map data points to CustomPntData format', () => {
    const data: Record<string, string>[] = [
      {
        0: '1',
        1: '100',
        2: '2024-12-01T15:50:00',
        3: 'True',
        4: 'False',
        5: 'True',
        6: '1',
        7: '1',
        8: '1',
        9: '1',
        10: '1',
        11: '1',
        12: 'UTC+00:00',
      },
    ];

    const expected = [
      {
        runDetailId: '1',
        origVal: 100,
        qcDt: '2024-12-01T15:50:00+00:00',
        lotNumber: '1',
        isCommented: true,
        isPsdXbL: false,
        isPsdCuL: true,
        xbarmLimitParameterLowerLimit: 1,
        xbarmLimitParameterMean: 1,
        xbarmLimitParameterUpperLimit: 1,
        xbarmCustomerLimitParameterLowerLimit: 1,
        xbarmCustomerLimitParameterMean: 1,
        xbarmCustomerLimitParameterUpperLimit: 1,
        varPct: 0,
        origUpp: 0,
        origLow: 0,
        origTgt: 0,
        isMngd: true,
        isPsd: true,
        isPsdAsL: true,
        isPsdCoL: true,
        groupIndex: -1,
      },
    ];

    const result = component.mapDataPointsToSecondChartPnt(data, 1);

    expect(result).toEqual(expected);
  });

  it('should return an empty array if no channel matches the activeChannelId', () => {
    const result = component.getParameterList(3); // Assuming no channel with channelId 3 exists

    expect(result).toEqual([]);
  });

  it('should return parameter list with limits data for the active channel', () => {
    component.channelsWarningTrue = [
      {
        channelId: 1,
        parameters: [
          {
            conversionRate: 1,
            parameterId: 1,
            channelId: 0,
            parameterUnitId: 0,
            parameterCode: EMPTY,
            parameterDisplayName: EMPTY,
            unitDisplay: '123',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: EMPTY,
            modelGroup: EMPTY,
            analyzerSerial: EMPTY,
            isCountryLimitChart: false,
          },
          {
            conversionRate: 1,
            parameterId: 2,
            channelId: 0,
            parameterUnitId: 0,
            parameterCode: EMPTY,
            parameterDisplayName: EMPTY,
            unitDisplay: '123',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: EMPTY,
            modelGroup: EMPTY,
            analyzerSerial: EMPTY,
            isCountryLimitChart: false,
          },
        ],
        channelName: EMPTY,
      },
      {
        channelId: 2,
        parameters: [
          {
            conversionRate: 1,
            parameterId: 3,
            channelId: 0,
            parameterUnitId: 0,
            parameterCode: EMPTY,
            parameterDisplayName: EMPTY,
            unitDisplay: EMPTY,
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: EMPTY,
            modelGroup: EMPTY,
            analyzerSerial: EMPTY,
            isCountryLimitChart: false,
          },
        ],
        channelName: EMPTY,
      },
    ];
    component.limitData = [
      {
        parameterId: 1,
        xbarmLimit: {
          target: 10,
          upperLimit: 15,
          lowerLimit: 5,
        },
        xbarmCustomerLimit: {
          target: 10,
          upperLimit: 15,
          lowerLimit: 5,
        },
        assayLimit: null,
        customerLimit: null,
        countryLimit: null,
        parameterCode: EMPTY,
        unitDisplay: '123',
      },
      {
        parameterId: 2,
        xbarmLimit: null,
        xbarmCustomerLimit: null,
        assayLimit: {
          target: 20,
          upperLimit: 25,
          lowerLimit: 15,
        },
        customerLimit: {
          target: 22,
          upperLimit: 24,
          lowerLimit: 18,
        },
        countryLimit: {
          target: 21,
          upperLimit: 23,
          lowerLimit: 19,
        },
        parameterCode: EMPTY,
        unitDisplay: '123',
      },
    ];
    component.chartMode = CHART_MODE.COMPARE_TO_XBARM;
    component.chartFilterParameter = {
      selectedDateRange: {
        start: '2020-01-01',
        end: '2020-01-31',
      },
      selectedMaterialControlLotIds: [],
      currentLevelLotNumber: EMPTY,
      selectedMaterialType: EMPTY,
      selectedFullLotNumbers: [],
      selectedLevel1LotNumber: EMPTY,
      selectedLevel2LotNumber: EMPTY,
      selectedLevel3LotNumber: EMPTY,
    };

    const result = component.getParameterList(1);

    expect(result).toBeDefined();
  });

  it('should return series info including dynamically generated and static entries', () => {
    component.chartFilterParameter = {
      selectedFullLotNumbers: ['L12345', 'L67890'],
      selectedMaterialControlLotIds: [],
      currentLevelLotNumber: EMPTY,
      selectedMaterialType: EMPTY,
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedLevel1LotNumber: EMPTY,
      selectedLevel2LotNumber: EMPTY,
      selectedLevel3LotNumber: EMPTY,
    };

    Utils.getChartProperties = jest.fn(() => ({
      color: 'red',
      icon: 'circle',
    }));

    const result = component.getAllSeriesToXBarMInfo();
    const expected = [
      {
        label: 'Level 5',
        name: 'Level5',
        isBold: false,
        isDashedStyle: false,
        color: 'red',
        pointIcon: 'circle',
        level: 5,
      },
      {
        label: 'Level 0',
        name: 'Level0',
        isBold: false,
        isDashedStyle: false,
        color: 'red',
        pointIcon: 'circle',
        level: 0,
      },
      {
        label: 'Level 1',
        name: 'SecondChartData',
        isBold: false,
        isDashedStyle: false,
        color: 'var(--purple-100)',
        pointIcon: POINT_MARKER.CIRCLE,
        level: 1,
      },
    ];

    expect(result).toEqual(expected);
    expect(Utils.getChartProperties).toHaveBeenCalledTimes(2);
  });

  it('should update parameterSelectedAnalyzers correctly', () => {
    // Mock data
    const mockParameterSelectedAnalyzers: ParameterSelectedAnalyzers[] = [
      {
        parameterUnitId: 1,
        analyzers: [
          {
            modelCode: 'A1',
            serialNumber: '123',
            isEnabled: false,
            siteCode: '',
            isPrimary: false,
          },
          {
            modelCode: 'A2',
            serialNumber: '456',
            isEnabled: true,
            siteCode: '',
            isPrimary: false,
          },
        ],
      },
    ];

    const parameterSelectedAnalyzer: ParameterSelectedAnalyzers = {
      parameterUnitId: 1,
      analyzers: [
        {
          modelCode: 'A1',
          serialNumber: '123',
          isEnabled: true,
          siteCode: '',
          isPrimary: false,
        },
        {
          modelCode: 'A2',
          serialNumber: '456',
          isEnabled: false,
          siteCode: '',
          isPrimary: false,
        },
      ],
    };

    // Mock Utils.deepClone
    jest.spyOn(Utils, 'deepClone').mockReturnValue(mockParameterSelectedAnalyzers);

    // Set initial state
    component.parameterSelectedAnalyzers = mockParameterSelectedAnalyzers;

    // Call the method
    component.onParameterSelectedAnalyzer(parameterSelectedAnalyzer);

    // Assertions
    expect(component.parameterSelectedAnalyzers[0].analyzers[0].isEnabled).toBe(true);
    expect(component.parameterSelectedAnalyzers[0].analyzers[1].isEnabled).toBe(false);
  });

  it('should group parameter analyzers correctly', () => {
    // Set initial state
    component.parameterSelectedAnalyzers = [
      {
        parameterUnitId: 1,
        analyzers: [
          {
            modelCode: 'A1',
            serialNumber: '123',
            isEnabled: true,
            siteCode: '',
            isPrimary: false,
          },
          {
            modelCode: 'A2',
            serialNumber: '456',
            isEnabled: false,
            siteCode: '',
            isPrimary: false,
          },
        ],
      },
      {
        parameterUnitId: 2,
        analyzers: [
          {
            modelCode: 'A3',
            serialNumber: '789',
            isEnabled: true,
            siteCode: '',
            isPrimary: false,
          },
          {
            modelCode: 'A4',
            serialNumber: '012',
            isEnabled: true,
            siteCode: '',
            isPrimary: false,
          },
        ],
      },
      {
        parameterUnitId: 3,
        analyzers: [
          {
            modelCode: 'A5',
            serialNumber: '345',
            isEnabled: true,
            siteCode: '',
            isPrimary: false,
          },
        ],
      },
    ];

    // Call the method
    const result = component.groupParameterAnalyzers();

    // Expected result
    const expected: ParameterSelectedAnalyzers[] = [
      {
        parameterUnitId: 1,
        analyzers: [
          {
            modelCode: 'A1',
            serialNumber: '123',
            isEnabled: true,
            siteCode: '',
            isPrimary: false,
          },
          {
            modelCode: 'A3',
            serialNumber: '789',
            isEnabled: true,
            siteCode: '',
            isPrimary: false,
          },
          {
            modelCode: 'A4',
            serialNumber: '012',
            isEnabled: true,
            siteCode: '',
            isPrimary: false,
          },
        ],
      },
      {
        parameterUnitId: 3,
        analyzers: [
          {
            modelCode: 'A5',
            serialNumber: '345',
            isEnabled: true,
            siteCode: '',
            isPrimary: false,
          },
        ],
      },
    ];

    // Assertions
    expect(result).toEqual(expected);
  });

  it('should return an empty array if data is null or undefined', () => {
    const result = component.mapDataPointsZScoreChart([], 1);

    expect(result).toEqual([]);
  });

  it('should correctly map data points to ChartPnt format', () => {
    const data = [
      {
        '0': '1',
        '1': '2',
        '2': '100',
        '3': '2020-01-01T12:00:00',
        '4': '2020-01-01T12:00:00',
        '5': 'True',
        '6': 'False',
        '7': 'True',
        '8': 'False',
        '9': '1.5',
        '10': 'ErrorType1',
        '11': 'False',
        '12': '2024-09-30T12:00:00.0000000Z',
        '13': '0.1',
        '14': '0.5',
        '15': '1.0',
        '16': '0.0',
        '17': '0.0',
        '18': '0.0',
        '19': '0.0',
        '20': '0.0',
        '21': '0.0',
        '22': 'UTC+07:00',
      },
      {
        '0': '3',
        '1': '4',
        '2': '200',
        '3': '2020-01-01T12:00:00',
        '4': '2020-01-01T12:00:00',
        '5': 'False',
        '6': 'True',
        '7': 'False',
        '8': 'True',
        '9': '2.5',
        '10': 'ErrorType2',
        '11': 'False',
        '12': '2024-09-30T12:00:00.0000000Z',
        '13': '0.1',
        '14': '0.5',
        '15': '1.0',
        '16': '0.0',
        '17': '0.0',
        '18': '0.0',
        '19': '0.0',
        '20': '0.0',
        '21': '0.0',
        '22': 'UTC+07:00',
      },
    ];

    const expected = [
      {
        runDetailId: '1',
        runId: '2',
        origVal: 100,
        qcDt: '2020-01-01T12:00:00+07:00',
        qcRunUpdatedDate: '2024-09-30T12:00:00.0000000Z',
        qcSet: '2020-01-01T12:00:00+07:00',
        isMngd: true,
        isCommented: false,
        isPsdAsL: true,
        isPsdMulrL: false,
        yValue: 1.5,
        errorType: 'ErrorType1',
        varPct: 0,
        origUpp: 0,
        origLow: 0,
        origTgt: 0,
        isPsd: true,
        isPsdCuL: true,
        isPsdCoL: true,
        lotNumber: '1',
        isAutoUnmanaged: false,
        zscoreAssayLowerLimit: 0.1,
        zscoreAssayMean: 0.5,
        zscoreAssayUpperLimit: 1,
        groupIndex: -1,
      },
      {
        runDetailId: '3',
        runId: '4',
        origVal: 200,
        qcDt: '2020-01-01T12:00:00+07:00',
        qcRunUpdatedDate: '2024-09-30T12:00:00.0000000Z',
        qcSet: '2020-01-01T12:00:00+07:00',
        isMngd: false,
        isCommented: true,
        isPsdAsL: false,
        isPsdMulrL: true,
        yValue: 2.5,
        errorType: 'ErrorType2',
        varPct: 0,
        origUpp: 0,
        origLow: 0,
        origTgt: 0,
        isPsd: true,
        isPsdCuL: true,
        isPsdCoL: true,
        lotNumber: '1',
        isAutoUnmanaged: false,
        zscoreAssayLowerLimit: 0.1,
        zscoreAssayMean: 0.5,
        zscoreAssayUpperLimit: 1,
        groupIndex: -1,
      },
    ];

    const result = component.mapDataPointsZScoreChart(data, 1);

    expect(result).toEqual(expected);
  });

  it('should return series info for all selected lots in ZScore chart', () => {
    // Mock the chartFilterParameter property on the component
    component.chartFilterParameter = {
      selectedFullLotNumbers: ['Lot1', 'Lot2'],
      selectedMaterialControlLotIds: [],
      selectedLevelLotNumbers: ['1', '2'],
      currentLevelLotNumber: EMPTY,
      selectedMaterialType: EMPTY,
      selectedDateRange: {
        start: EMPTY,
        end: EMPTY,
      },
      selectedLevel1LotNumber: EMPTY,
      selectedLevel2LotNumber: EMPTY,
      selectedLevel3LotNumber: EMPTY,
    };

    // Mock the Utils.getLevelBaseOnFullLotNumber and Utils.getChartProperties methods
    jest.spyOn(Utils, 'getLevelBaseOnFullLotNumber').mockImplementation((lot) => {
      if (lot === '1') {
        return 1;
      }

      if (lot === '2') {
        return 2;
      }

      return 0;
    });

    jest.spyOn(Utils, 'getChartProperties').mockImplementation((type, level) => {
      if (level === 1) {
        return {
          color: 'red',
          icon: 'circle',
        };
      }

      if (level === 2) {
        return {
          color: 'blue',
          icon: 'square',
        };
      }

      return {
        color: 'black',
        icon: 'triangle',
      };
    });

    const expected = [
      {
        label: 'Level 1',
        name: '1',
        isBold: false,
        isDashedStyle: false,
        color: 'red',
        pointIcon: 'circle',
        level: 1,
      },
      {
        label: 'Level 2',
        name: '2',
        isBold: false,
        isDashedStyle: false,
        color: 'blue',
        pointIcon: 'square',
        level: 2,
      },
    ];

    const result = component.getAllSeriesZScoreInfo();

    expect(result).toEqual(expected);
    expect(Utils.getLevelBaseOnFullLotNumber).toHaveBeenCalledTimes(2);
    expect(Utils.getChartProperties).toHaveBeenCalledTimes(2);
  });

  describe('getParameterMapZScore', () => {
    it('should correctly populate comparisonData with seriesData and hasDataPoint', () => {
      // Mock inputs
      const analyzerEvent: AnalyzerEventInfo = {
        reagentEvents: [
          {
            channelId: 1,
            parameters: [
              {
                parameterCode: 'Code1',
                reagentEvents: [],
              },
              {
                parameterCode: 'Code2',
                reagentEvents: [],
              },
            ],
          },
          {
            channelId: 2,
            parameters: [
              {
                parameterCode: 'Code1',
                reagentEvents: [],
              },
              {
                parameterCode: 'Code2',
                reagentEvents: [],
              },
            ],
          },
        ],
        calibrationEvents: [],
        serviceHistories: [],
      };
      const parameterList: ParameterDateRangeInfo[] = [
        {
          conversionRate: 1,
          parameterUnitId: 1,
          parameterCode: 'Code1',
          channelId: 0,
          parameterId: 0,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
      ];
      const comparisonData: QcComparisonData[] = [
        {
          parameterUnitId: 1,
          seriesData: [],
          hasDataPoint: false,
          paramDateRange: {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 1,
            parameterId: 0,
            parameterCode: EMPTY,
            parameterDisplayName: EMPTY,
            unitDisplay: EMPTY,
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-05-15',
              end: '2024-05-20',
            },
            allSeriesInfo: [],
            siteCode: EMPTY,
            modelGroup: EMPTY,
            analyzerSerial: EMPTY,
            isCountryLimitChart: false,
          },
          channelId: 0,
        },
      ];
      const activeChannelId = 1;
      const response: Record<string, LotSeries> = {
        '1': {
          1: {
            dataPoints: [
              /* Some data points */
            ],
            statisticValue: {},
          },
          2: {
            dataPoints: [
              /* Some data points */
            ],
            statisticValue: {},
          },
          3: {
            dataPoints: [
              /* Some data points */
            ],
            statisticValue: {},
          },
        },
      };

      // Mock dependent methods
      jest.spyOn(component, 'getParameterCodeByUnitId').mockReturnValue('Code1');
      jest.spyOn(component, 'getReagentEventsForParameter').mockReturnValue([]);
      jest.spyOn(component, 'mapDataPointsZScoreChart').mockReturnValue([]);

      // Call the method
      const updatedComparisonData = component.getParameterMapZScore(
        analyzerEvent,
        parameterList,
        comparisonData,
        activeChannelId,
        response,
      );

      // Verify the results
      expect(updatedComparisonData[0].seriesData).toBeDefined();
      expect(updatedComparisonData[0].hasDataPoint).toBe(false);
    });

    it('should handle empty response correctly', () => {
      // Mock inputs
      const analyzerEvent: AnalyzerEventInfo = {
        reagentEvents: [],
        calibrationEvents: [],
        serviceHistories: [],
      };
      const parameterList: ParameterDateRangeInfo[] = [
        {
          conversionRate: 1,
          parameterUnitId: 1,
          parameterCode: 'Code1',
          channelId: 0,
          parameterId: 0,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
      ];
      const comparisonData: QcComparisonData[] = [
        {
          parameterUnitId: 1,
          seriesData: [],
          hasDataPoint: false,
          paramDateRange: {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 1,
            parameterId: 0,
            parameterCode: EMPTY,
            parameterDisplayName: EMPTY,
            unitDisplay: EMPTY,
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-05-15',
              end: '2024-05-20',
            },
            allSeriesInfo: [],
            siteCode: EMPTY,
            modelGroup: EMPTY,
            analyzerSerial: EMPTY,
            isCountryLimitChart: false,
          },
          channelId: 0,
        },
      ];
      const activeChannelId = 1;
      const response: Record<string, LotSeries> = {};

      // Mock dependent methods
      jest.spyOn(component, 'getParameterCodeByUnitId').mockReturnValue('Code1');
      jest.spyOn(component, 'getReagentEventsForParameter').mockReturnValue([]);
      jest.spyOn(component, 'mapDataPointsZScoreChart').mockReturnValue([]);

      // Call the method
      const updatedComparisonData = component.getParameterMapZScore(
        analyzerEvent,
        parameterList,
        comparisonData,
        activeChannelId,
        response,
      );

      // Verify the results
      expect(updatedComparisonData[0].seriesData).toEqual([]);
      expect(updatedComparisonData[0].hasDataPoint).toBe(false);
    });

    it('should correctly map data points to ChartPnt format', () => {
      const data = [
        {
          '0': '1',
          '1': '2',
          '2': '100',
          '3': '2020-01-01T12:00:00',
          '4': '2020-01-01T12:00:00',
          '5': 'True',
          '6': 'False',
          '7': 'True',
          '8': 'False',
          '9': '1.5',
          '10': 'ErrorType1',
          '11': 'False',
          '12': '2024-09-30T12:00:00.0000000Z',
          '13': '0.1',
          '14': '0.5',
          '15': '1.0',
          '16': '0.0',
          '17': '0.0',
          '18': '0.0',
          '19': '0.0',
          '20': '0.0',
          '21': '0.0',
          '22': 'UTC+07:00',
        },
        {
          '0': '3',
          '1': '4',
          '2': '200',
          '3': '2020-01-01T12:00:00',
          '4': '2020-01-01T12:00:00',
          '5': 'False',
          '6': 'True',
          '7': 'False',
          '8': 'True',
          '9': '2.5',
          '10': 'ErrorType2',
          '11': 'True',
          '12': '2024-09-30T12:00:00.0000000Z',
          '13': '0.1',
          '14': '0.5',
          '15': '1.0',
          '16': '0.0',
          '17': '0.0',
          '18': '0.0',
          '19': '0.0',
          '20': '0.0',
          '21': '0.0',
          '22': 'UTC+07:00',
        },
      ];

      const expected = [
        {
          runDetailId: '1',
          runId: '2',
          origVal: 100,
          qcDt: '2020-01-01T12:00:00+07:00',
          qcSet: '2020-01-01T12:00:00+07:00',
          isMngd: true,
          isCommented: false,
          isPsdAsL: true,
          isPsdMulrL: false,
          yValue: 1.5,
          errorType: 'ErrorType1',
          varPct: 0,
          origUpp: 0,
          origLow: 0,
          origTgt: 0,
          isPsd: true,
          isPsdCuL: true,
          isPsdCoL: true,
          lotNumber: '1',
          isAutoUnmanaged: false,
          qcRunUpdatedDate: '2024-09-30T12:00:00.0000000Z',
          zscoreAssayLowerLimit: 0.1,
          zscoreAssayMean: 0.5,
          zscoreAssayUpperLimit: 1,
          groupIndex: -1,
        },
        {
          runDetailId: '3',
          runId: '4',
          origVal: 200,
          qcDt: '2020-01-01T12:00:00+07:00',
          qcSet: '2020-01-01T12:00:00+07:00',
          isMngd: false,
          isCommented: true,
          isPsdAsL: false,
          isPsdMulrL: true,
          yValue: 2.5,
          errorType: 'ErrorType2',
          varPct: 0,
          origUpp: 0,
          origLow: 0,
          origTgt: 0,
          isPsd: true,
          isPsdCuL: true,
          isPsdCoL: true,
          lotNumber: '1',
          isAutoUnmanaged: true,
          qcRunUpdatedDate: '2024-09-30T12:00:00.0000000Z',
          zscoreAssayLowerLimit: 0.1,
          zscoreAssayMean: 0.5,
          zscoreAssayUpperLimit: 1,
          groupIndex: -1,
        },
      ];

      const result = component.mapDataPointsZScoreChart(data, 1);

      expect(result).toEqual(expected);
    });
  });

  it('should return ZSCORE_CHART limits when chartMode is ZSCORE_CHART', () => {
    component.chartMode = component.chartModeFilter.ZSCORE_CHART;

    const limitData: LimitSetting[] = [];
    const parameter: ParameterDateRangeInfo = {
      conversionRate: 1,
      parameterId: 1,
      parameterCode: 'param1',
      unitDisplay: 'unit1',
      channelId: 0,
      parameterUnitId: 0,
      parameterDisplayName: '',
      decimalPoint: 0,
      criteriaDateRange: {
        start: '2020-01-01',
        end: '2020-01-31',
      },
      allSeriesInfo: [],
      siteCode: '',
      modelGroup: '',
      analyzerSerial: '',
      isCountryLimitChart: false,
    };

    const result = component.getLimitValues(limitData, parameter);

    expect(result).toEqual({
      assayLimit: {
        target: 0,
        upperLimit: 3,
        lowerLimit: -3,
      },
      customerLimit: null,
      countryLimit: null,
      xbarmLimit: null,
      multiRulesLimit: null,
      xbarmCustomerLimit: null,
      parameterCode: '',
      parameterId: 0,
      parameterUnitId: 0,
    });
  });

  it('should return combined parameters for matching channels', () => {
    const firstData: ChannelWarning[] = [
      {
        channelId: 1,
        isWarning: true,
        parameters: [
          {
            conversionRate: 1,
            parameterId: 1,
            parameterCode: 'A',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
          {
            conversionRate: 1,
            parameterId: 2,
            parameterCode: 'B',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        channelName: '',
      },
    ];

    const secondData: ChannelWarning[] = [
      {
        channelId: 1,
        isWarning: false,
        isPassedXbarmCustomerLimit: true,
        parameters: [
          {
            conversionRate: 1,
            parameterId: 2,
            parameterCode: 'B',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
          {
            conversionRate: 1,
            parameterId: 3,
            parameterCode: 'C',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        channelName: '',
      },
    ];

    const result = component.getCommonParameters(firstData, secondData);

    expect(result).toBeDefined();
  });

  it('should return original parameters for channels with no match in secondData', () => {
    const firstData: ChannelWarning[] = [
      {
        channelId: 1,
        isWarning: true,
        parameters: [
          {
            conversionRate: 1,
            parameterId: 1,
            parameterCode: 'A',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        channelName: '',
      },
    ];

    const secondData: ChannelWarning[] = [];

    const result = component.getCommonParameters(firstData, secondData);

    expect(result).toBeDefined();
  });

  it('should filter out duplicate parameters', () => {
    const firstData: ChannelWarning[] = [
      {
        channelId: 1,
        isWarning: true,
        parameters: [
          {
            conversionRate: 1,
            parameterId: 1,
            parameterCode: 'A',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
          {
            conversionRate: 1,
            parameterId: 2,
            parameterCode: 'B',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        channelName: '',
      },
    ];

    const secondData: ChannelWarning[] = [
      {
        channelId: 1,
        isWarning: false,
        isPassedXbarmCustomerLimit: true,
        parameters: [
          {
            conversionRate: 1,
            parameterId: 2,
            parameterCode: 'B',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
          {
            parameterId: 1,
            conversionRate: 1,
            parameterCode: 'A',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2020-01-01',
              end: '2020-01-31',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        channelName: '',
      },
    ];

    const result = component.getCommonParameters(firstData, secondData);

    expect(result).toBeDefined();
  });

  describe('combineWarning', () => {
    it('should return true when channel.isWarning is true', () => {
      const channel: ChannelWarning = {
        isWarning: true,
        isPassedXbarmLimit: true,
        channelId: 0,
        channelName: '',
      };
      const correspondingChannel: ChannelWarning = {
        isWarning: false,
        isPassedXbarmLimit: true,
        channelId: 0,
        channelName: '',
      };

      expect(component.combineWarning(channel, correspondingChannel)).toBe(true);
    });

    it('should return true when correspondingChannel.isPassedXbarmLimit is false', () => {
      const channel: ChannelWarning = {
        isWarning: false,
        isPassedXbarmLimit: true,
        channelId: 0,
        channelName: '',
      };
      const correspondingChannel: ChannelWarning = {
        isWarning: false,
        isPassedXbarmLimit: false,
        channelId: 0,
        channelName: '',
      };

      expect(component.combineWarning(channel, correspondingChannel)).toBe(true);
    });

    it('should return false when neither condition is met', () => {
      const channel: ChannelWarning = {
        isWarning: false,
        isPassedXbarmLimit: true,
        channelId: 0,
        channelName: '',
      };
      const correspondingChannel: ChannelWarning = {
        isWarning: false,
        isPassedXbarmLimit: true,
        channelId: 0,
        channelName: '',
      };

      expect(component.combineWarning(channel, correspondingChannel)).toBe(false);
    });
  });

  it('should call compare to XbarM when chartMode is COMPARE_TO_PREVIOUS_LOT', () => {
    component.chartMode = CHART_MODE.COMPARE_TO_XBARM;
    component.analyzerParameter = {
      modelCode: 'model1',
      serialNumber: '123456',
    };
    component.chartFilterParameter = {
      analyzers: [],
      selectedFullLotNumbers: ['lot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'someMaterialType',
      currentLevelLotNumber: 'currentLevelLot1',
      selectedMaterialControlLotIds: ['controlLot1'],
    };

    jest.spyOn(mockChannelService, 'getComparisonFirstBarMChannelsWarning').mockReturnValue(of([]));
    jest.spyOn(mockChannelService, 'getComparisonSecondBarMChannelsWarning').mockReturnValue(of([]));

    component.fetchWarningsStatus();

    expect(mockChannelService.getComparisonFirstBarMChannelsWarning).toHaveBeenCalled();
    expect(mockChannelService.getComparisonSecondBarMChannelsWarning).toHaveBeenCalled();
  });

  it('should call getChannelStatusSdiMultipleAnalyzers when chartMode is SDI_MULTIPLE_ANALYZERS', () => {
    component.chartMode = component.chartModeFilter.SDI_MULTIPLE_ANALYZERS;
    component.chartFilterParameter = {
      analyzers: [
        {
          modelCode: 'model1',
          serialNumber: '123456',
          siteCode: EMPTY,
          isPrimary: false,
        },
        {
          modelCode: 'model2',
          serialNumber: '654321',
          siteCode: EMPTY,
          isPrimary: false,
        },
      ],
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialControlLotIds: ['lot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedMaterialType: 'someMaterialType',
      selectedFullLotNumbers: ['fullLot1'],
      currentLevelLotNumber: 'currentLevelLot1',
      selectedPeerGroupId: 'peerGroup1',
    };
    mockChannelService.getChannelStatusSdiMultipleAnalyzers.mockReturnValue(of([]));
    component.fetchWarningsStatus();

    const expectedSdiParams = {
      analyzers: ['model1^123456', 'model2^654321'],
      materialControlLotId: 'lot1',
      peerGroupId: 'peerGroup1',
      startDate: '2024-05-15',
      endDate: '2024-05-20',
    };

    expect(mockChannelService.getChannelStatusSdiMultipleAnalyzers).toHaveBeenCalledWith(expectedSdiParams);
  });
  it('should return undefined if chartMode is not SDI_SINGLE_ANALYZER', () => {
    component.chartMode = CHART_MODE.COMPARE_TO_XBARM;
    component.chartFilterParameter = {
      selectedMaterialControlLots: undefined,
      selectedMaterialControlLotIds: [],
      currentLevelLotNumber: '',
      selectedMaterialType: '',
      selectedDateRange: {
        start: '',
        end: '',
      },
      selectedFullLotNumbers: [],
      selectedLevel1LotNumber: '',
      selectedLevel2LotNumber: '',
      selectedLevel3LotNumber: '',
      analyzers: [],
      limitData: [],
    };

    const result = component.getSDIMaterialControlLotIds();

    expect(result).toBeUndefined();
  });
  it('should return undefined if selectedMaterialControlLots is undefined', () => {
    component.chartMode = component.chartModeFilter.SDI_SINGLE_ANALYZER;
    component.chartFilterParameter = {
      selectedMaterialControlLots: undefined,
      selectedMaterialControlLotIds: [],
      currentLevelLotNumber: '',
      selectedMaterialType: '',
      selectedDateRange: {
        start: '',
        end: '',
      },
      selectedFullLotNumbers: [],
      selectedLevel1LotNumber: '',
      selectedLevel2LotNumber: '',
      selectedLevel3LotNumber: '',
      analyzers: [],
      limitData: [],
    };

    const result = component.getSDIMaterialControlLotIds();

    expect(result).toBeUndefined();
  });
  it('should return correct CompareLotModel when selectedMaterialControlLots has single lot', () => {
    component.chartMode = component.chartModeFilter.SDI_SINGLE_ANALYZER;
    component.chartFilterParameter = {
      selectedMaterialControlLots: new Map([
        [
          1,
          [
            {
              id: 'id1',
              materialControlId: 'id1',
              value: 'value1',
              lotNumber: 'lot1',
              isLatest: true,
              isDisabled: false,
              startDate: '2024-01-01',
              expiredDate: '2024-12-31',
              level: '1',
            },
          ],
        ],
      ]),
      selectedMaterialControlLotIds: [],
      currentLevelLotNumber: '',
      selectedMaterialType: '',
      selectedDateRange: {
        start: '',
        end: '',
      },
      selectedFullLotNumbers: [],
      selectedLevel1LotNumber: '',
      selectedLevel2LotNumber: '',
      selectedLevel3LotNumber: '',
      analyzers: [],
      limitData: [],
    };

    const result = component.getSDIMaterialControlLotIds();

    expect(result).toEqual({
      previousLots: [],
      currentLots: ['id1'],
    });
  });
  it('should return correct CompareLotModel when selectedMaterialControlLots has multiple lots', () => {
    component.chartMode = component.chartModeFilter.SDI_SINGLE_ANALYZER;
    component.chartFilterParameter = {
      selectedMaterialControlLots: new Map<number, DsLotSelectOption[]>([
        [
          1,
          [
            {
              id: 'id1',
              materialControlId: 'id1',
              value: 'value1',
              lotNumber: 'lot1',
              isLatest: true,
              startDate: '2024-01-01',
              expiredDate: '2024-12-31',
              level: '1',
            },
            {
              id: 'id2',
              materialControlId: 'id2',
              value: 'value2',
              lotNumber: 'lot2',
              isLatest: true,
              startDate: '2024-01-01',
              expiredDate: '2024-12-31',
              level: '1',
            },
          ],
        ],
        [
          2,
          [
            {
              id: 'id3',
              materialControlId: 'id3',
              value: 'value3',
              lotNumber: 'lot3',
              isLatest: true,
              startDate: '2024-01-01',
              expiredDate: '2024-12-31',
              level: '1',
            },
            {
              id: 'id4',
              materialControlId: 'id4',
              value: 'value4',
              lotNumber: 'lot4',
              isLatest: true,
              startDate: '2024-01-01',
              expiredDate: '2024-12-31',
              level: '1',
            },
          ],
        ],
      ]),
      selectedMaterialControlLotIds: [],
      currentLevelLotNumber: '',
      selectedMaterialType: '',
      selectedDateRange: {
        start: '',
        end: '',
      },
      selectedFullLotNumbers: [],
      selectedLevel1LotNumber: '',
      selectedLevel2LotNumber: '',
      selectedLevel3LotNumber: '',
      analyzers: [],
      limitData: [],
      isComparedToPreviousLot: true,
    };

    const result = component.getSDIMaterialControlLotIds();

    expect(result).toEqual({
      previousLots: ['id2', 'id4'],
      currentLots: ['id1', 'id3'],
    });
  });

  it('should fetch warnings for single analyzer in SDI_SINGLE_ANALYZER mode', () => {
    const mockWarnings = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        isOutlier: true,
      },
    ];

    mockChannelService.getSDISingleChannelsStatus.mockReturnValue(of(mockWarnings));
    component.chartMode = CHART_MODE.SDI_SINGLE_ANALYZER;
    component.analyzerParameter = {
      modelCode: 'model1',
      serialNumber: '123456',
    };
    component.chartFilterParameter = {
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedMaterialControlLotIds: ['lot1'],
      selectedFullLotNumbers: ['lot1'],
      selectedPeerGroupId: 'peerGroup1',
      currentLevelLotNumber: '',
      selectedMaterialType: '',
      selectedLevel1LotNumber: '',
      selectedLevel2LotNumber: '',
      selectedLevel3LotNumber: '',
    };
    jest.spyOn(component, 'getSDIMaterialControlLotIds').mockReturnValue({
      previousLots: [],
      currentLots: ['lot1'],
    });
    // Act
    component.fetchWarningsStatus();
    expect(mockChannelService.getSDISingleChannelsStatus).toHaveBeenCalledWith({
      modelCode: 'model1',
      analyzerSerial: '123456',
      materialControlLotIds: ['lot1'],
      startDate: '2024-05-15',
      endDate: '2024-05-20',
      peerGroupId: 'peerGroup1',
      previousMaterialControlLotIds: [],
    });
    expect(component.channels).toEqual([]);
  });

  it('should return undefined when limitValues is undefined', () => {
    const result = component.mapLimitData(undefined, undefined, undefined);

    expect(result).toBeUndefined();
  });

  it('should map limit values correctly when limitValues is defined', () => {
    const limitValues: LimitSetting = {
      customerLimit: {
        target: 1,
        upperLimit: 2,
        lowerLimit: 0,
      },
      countryLimit: {
        target: 1.5,
        upperLimit: 2.5,
        lowerLimit: 0.5,
      },
      multiRulesLimit: {
        rule1: {
          target: 2,
          upperLimit: 3,
          lowerLimit: 1,
        },
      },
      parameterId: 0,
      parameterCode: '',
    };
    const assayLimit: CustomerLimit = {
      target: 1,
      upperLimit: 2,
      lowerLimit: 0,
    };
    const customerLimit: CustomerLimit = {
      target: 1.5,
      upperLimit: 2.5,
      lowerLimit: 0.5,
    };

    component.chartMode = CHART_MODE.ADVANCED_LJ_CHART;

    const result = component.mapLimitData(limitValues, assayLimit, customerLimit);

    expect(result).toEqual({
      assay: {
        target: 1,
        upper: 2,
        lower: 0,
      },
      customer: {
        target: 1.5,
        upper: 2.5,
        lower: 0.5,
      },
      country: undefined,
      multiRules: undefined,
    });
  });

  it('should handle multiRulesLimit correctly when chartMode is COMPARE_TO_XBARM', () => {
    const limitValues: LimitSetting = {
      customerLimit: {
        target: 1,
        upperLimit: 2,
        lowerLimit: 0,
      },
      countryLimit: {
        target: 1.5,
        upperLimit: 2.5,
        lowerLimit: 0.5,
      },
      multiRulesLimit: {
        rule1: {
          target: 2,
          upperLimit: 3,
          lowerLimit: 1,
        },
      },
      parameterId: 0,
      parameterCode: '',
    };
    const assayLimit: CustomerLimit = {
      target: 1,
      upperLimit: 2,
      lowerLimit: 0,
    };
    const customerLimit: CustomerLimit = {
      target: 1.5,
      upperLimit: 2.5,
      lowerLimit: 0.5,
    };

    const countryLimit: CustomerLimit = {
      target: 1.5,
      upperLimit: 2.5,
      lowerLimit: 0.5,
    };

    component.chartMode = CHART_MODE.COMPARE_TO_XBARM;

    const result = component.mapLimitData(limitValues, assayLimit, customerLimit, undefined, countryLimit);

    expect(result).toEqual({
      assay: {
        target: 1,
        upper: 2,
        lower: 0,
      },
      customer: {
        target: 1.5,
        upper: 2.5,
        lower: 0.5,
      },
      country: {
        target: 1.5,
        upper: 2.5,
        lower: 0.5,
      },
      multiRules: undefined,
    });
  });

  describe('getAllSeriesSDISingleInfo', () => {
    it('should return an empty array when selectedMaterialControlLots is undefined', () => {
      component.chartFilterParameter = {
        ...component.chartFilterParameter,
        selectedMaterialControlLots: undefined,
      };

      const result = component.getAllSeriesSDISingleInfo();

      expect(result).toEqual([]);
    });

    it('should return correct series information for single lots', () => {
      component.chartFilterParameter = {
        ...component.chartFilterParameter,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        selectedMaterialControlLots: new Map([[1, [{ materialControlId: 'lot1' }]]]) as unknown as Map<
          number,
          DsLotSelectOption[]
        >,
      };

      jest.spyOn(Utils, 'getChartProperties').mockReturnValue({
        color: 'red',
        icon: 'circle',
      });

      const result = component.getAllSeriesSDISingleInfo();

      expect(result).toEqual([
        {
          label: 'Level 1',
          name: 'Level1_currentLot',
          isBold: false,
          isDashedStyle: false,
          color: 'red',
          pointIcon: 'circle',
          level: 1,
          visible: true,
        },
      ]);
    });

    it('should return correct series information for double lots', () => {
      component.chartFilterParameter = {
        ...component.chartFilterParameter,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        selectedMaterialControlLots: new Map([
          [1, [{ materialControlId: 'lot1' }, { materialControlId: 'lot2' }]],
        ]) as unknown as Map<number, DsLotSelectOption[]>,
        isComparedToPreviousLot: true,
      };

      jest.spyOn(Utils, 'getChartProperties').mockReturnValue({
        color: 'blue',
        icon: 'square',
      });

      const result = component.getAllSeriesSDISingleInfo();

      expect(result).toEqual([
        {
          label: 'Level 1',
          name: 'Level1_currentLot',
          isBold: false,
          isDashedStyle: false,
          color: 'blue',
          pointIcon: 'square',
          level: 1,
          visible: true,
        },
        {
          label: 'Level 1',
          name: 'Level1_previousLot',
          isBold: false,
          isDashedStyle: true,
          color: 'blue',
          pointIcon: 'square',
          level: 1,
          visible: true,
        },
      ]);
    });
  });

  describe('getParameterMapSDISingleAnalyzer', () => {
    it('should correctly process the input data and return the expected output', () => {
      const comparisonData: QcComparisonData[] = [
        {
          paramDateRange: {
            conversionRate: 1,
            parameterId: 3,
            parameterUnitId: 11,
            parameterCode: 'HCT',
            parameterDisplayName: 'HCT',
            unitDisplay: '%',
            decimalPoint: 1,
            criteriaDateRange: {
              start: '2024-09-20',
              end: '2024-10-28',
            },
            limitsData: {
              assay: {
                target: 0,
                upper: 4,
                lower: -4,
              },
            },
            allSeriesInfo: [
              {
                label: 'Level 1',
                name: 'Level1_currentLot',
                isBold: false,
                isDashedStyle: false,
                color: 'var(--purple-100)',
                pointIcon: 'circle',
                level: 1,
                visible: true,
              },
            ],
            channelId: 1,
            isComparedToPreviousLot: false,
            siteCode: 'IL0624',
            modelGroup: 'XN-10/XN-20',
            analyzerSerial: '240899',
            isCountryLimitChart: false,
          },
          seriesData: [],
          hasDataPoint: false,
          channelId: 1,
          parameterUnitId: 11,
        },
      ];

      const response: SDISingleComparisonDataResponse = MOCK_SDI_SINGLE_ANALYZERS_RESULT;

      jest.spyOn(Utils, 'getLevelBaseOnFullLotNumber').mockReturnValue(1);

      component.chartFilterParameter = {
        selectedMaterialControlLotIds: ['1'],
        selectedFullLotNumbers: ['1'],
        currentLevelLotNumber: '1',
        selectedMaterialType: '1',
        selectedDateRange: {
          start: '2024-10-01',
          end: '2024-10-20',
        },
        selectedLevel1LotNumber: '1',
        selectedLevel2LotNumber: '2',
        selectedLevel3LotNumber: '3',
      };

      const result = component.getParameterMapSDISingleAnalyzer(comparisonData, response);

      expect(result).toEqual([
        {
          paramDateRange: {
            parameterId: 3,
            parameterUnitId: 11,
            parameterCode: 'HCT',
            parameterDisplayName: 'HCT',
            unitDisplay: '%',
            decimalPoint: 1,
            modelGroup: 'XN-10/XN-20',
            siteCode: 'IL0624',
            criteriaDateRange: {
              start: '2024-09-20',
              end: '2024-10-28',
            },
            limitsData: {
              assay: {
                target: 0,
                upper: 4,
                lower: -4,
              },
            },
            allSeriesInfo: [
              {
                label: 'Level 1',
                name: 'Level1_currentLot',
                isBold: false,
                isDashedStyle: false,
                color: 'var(--purple-100)',
                pointIcon: 'circle',
                level: 1,
                visible: true,
              },
            ],
            analyzerSerial: '240899',
            channelId: 1,
            conversionRate: 1,
            isComparedToPreviousLot: false,
            isCountryLimitChart: false,
          },
          seriesData: [
            {
              Level1_currentLot: {
                chartType: 12,
                level: 1,
                pnts: [
                  {
                    runDetailId: 'a8ec92cf-11dd-4377-bef4-5b71673ac2ac',
                    runId: '6bc56fee-f73a-4527-b01f-3946afb4581c',
                    varPct: 0,
                    sdi: 1,
                    bias: 36.957135,
                    origVal: 1,
                    origUpp: 0,
                    origLow: 0,
                    origTgt: 0,
                    qcDt: '2024-10-20T01:48:26+07:00',
                    qcSet: '2024-10-20T08:00:00+07:00',
                    isMngd: true,
                    isCommented: true,
                    isPsd: true,
                    isPsdAsL: true,
                    isPsdCuL: true,
                    isPsdCoL: true,
                    peerGroupStatisticsParameterId: '3',
                    isAutoUnmanaged: true,
                    groupIndex: 0,
                  },
                ],
                statPnts: [
                  {
                    mean: 1,
                    sd: 2,
                    cv: 3,
                    n: 4,
                    bias: 36.957135,
                  },
                ],
              },
            },
          ],
          hasDataPoint: true,
          channelId: 1,
          parameterUnitId: 11,
          chartIndex: 0,
        },
      ]);
    });
  });

  describe('mapSDIDataPointsToChartPnt', () => {
    it('should correctly process the input data and return the expected output', () => {
      const data: SDIComparisonDataPoint = {
        qcRunId: ['run1', 'run2'],
        qcRunDetailId: ['detail1', 'detail2'],
        sdi: [1.1, 2.2],
        bias: [0.1, 0.2],
        originalValue: [10, 20],
        qcDatetime: ['2023-01-01', '2023-01-02'],
        qcSet: ['2023-01-01', '2023-01-02'],
        isManaged: [true, false],
        isCommented: [false, true],
        isOutlier: [false, true],
        peerGroupStatisticsParameterId: ['param1', 'param2'],
        autoUnmanagedFlags: [true, true],
        timeZone: ['UTC-07:00', 'UTC-07:00'],
        groupIndex: [0],
      };

      const statisticData: SDISingleComparisonStatistic = {
        peerGroupStatisticsParameterId: ['param1', 'param2'],
        groupMean: [1.0, 2.0],
        groupSd: [0.1, 0.2],
        groupCv: [10, 20],
        groupN: [100, 200],
      };

      const expectedOutput: ChartSeries<ChartPnt> = {
        pnts: [
          {
            runDetailId: 'detail1',
            runId: 'run1',
            varPct: 0,
            sdi: 1.1,
            bias: 0.1,
            origVal: 10,
            origUpp: 0,
            origLow: 0,
            origTgt: 0,
            qcDt: '2023-01-01-07:00',
            qcSet: '2023-01-01-07:00',
            isMngd: true,
            isCommented: false,
            isPsd: true,
            isPsdAsL: true,
            isPsdCuL: true,
            isPsdCoL: true,
            peerGroupStatisticsParameterId: 'param1',
            isAutoUnmanaged: true,
            groupIndex: 0,
          },
          {
            runDetailId: 'detail2',
            runId: 'run2',
            varPct: 0,
            sdi: 2.2,
            bias: 0.2,
            origVal: 20,
            origUpp: 0,
            origLow: 0,
            origTgt: 0,
            qcDt: '2023-01-02-07:00',
            qcSet: '2023-01-02-07:00',
            isMngd: false,
            isCommented: true,
            isPsd: false,
            isPsdAsL: true,
            isPsdCuL: true,
            isPsdCoL: true,
            peerGroupStatisticsParameterId: 'param2',
            isAutoUnmanaged: true,
            groupIndex: -1,
          },
        ],
        statPnts: [
          {
            mean: 1.0,
            sd: 0.1,
            cv: 10,
            n: 100,
            bias: 0.1,
          },
          {
            mean: 2.0,
            sd: 0.2,
            cv: 20,
            n: 200,
            bias: 0.2,
          },
        ],
      };

      const result = component.mapSDIDataPointsToChartPnt(data, statisticData);

      expect(result).toEqual(expectedOutput);
    });
  });

  describe('getSDIMaterialControlLotIds', () => {
    it('should return undefined when chartMode is not SDI_SINGLE_ANALYZER', () => {
      component.chartFilterParameter = {
        ...component.chartFilterParameter,
        selectedMaterialControlLots: new Map(),
      };

      const result = component.getSDIMaterialControlLotIds();

      expect(result).toBeUndefined();
    });

    it('should return undefined when selectedMaterialControlLots is undefined', () => {
      component.chartMode = component.chartModeFilter.SDI_SINGLE_ANALYZER;
      component.chartFilterParameter = {
        ...component.chartFilterParameter,
        selectedMaterialControlLots: undefined,
      };

      const result = component.getSDIMaterialControlLotIds();

      expect(result).toBeUndefined();
    });

    it(`should return correct CompareLotModel when chartMode is SDI_SINGLE_ANALYZER,
        selectedMaterialControlLots is provided and isComparedToPreviousLot is true`, () => {
      component.chartMode = component.chartModeFilter.SDI_SINGLE_ANALYZER;
      component.chartFilterParameter = {
        ...component.chartFilterParameter,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        selectedMaterialControlLots: new Map([
          [1, [{ materialControlId: 'lot1' }, { materialControlId: 'lot2' }]],
          [2, [{ materialControlId: 'lot3' }]],
        ]) as unknown as Map<number, DsLotSelectOption[]>,
        isComparedToPreviousLot: true,
      };

      const expected: CompareLotModel = {
        previousLots: ['lot2'],
        currentLots: ['lot1', 'lot3'],
      };

      const result = component.getSDIMaterialControlLotIds();

      expect(result).toEqual(expected);
    });
  });

  it('should map SDI comparison data points to chart points correctly', () => {
    const data: SDIComparisonDataPoints = {
      qcRunId: ['run1', 'run2'],
      qcRunDetailId: ['detail1', 'detail2'],
      sdi: [1.5, -0.5],
      bias: [0.1, -0.1],
      originalValue: [100, 200],
      qcDatetime: ['2023-01-01', '2023-01-02'],
      qcSet: ['2023-01-01', '2023-01-02'],
      isManaged: [true, false],
      isCommented: [false, true],
      isOutlier: [false, true],
      peerGroupStatisticsParameterId: ['param1', 'param2'],
      autoUnmanagedFlags: [true, true],
      timeZone: ['UTC-07:00', 'UTC-07:00'],

      groupIndex: [0],
    };

    const expected: ChartSeries<ChartPnt> = {
      pnts: [
        {
          runDetailId: 'detail1',
          runId: 'run1',
          varPct: 0,
          sdi: 1.5,
          bias: 0.1,
          origVal: 100,
          origUpp: 0,
          origLow: 0,
          origTgt: 0,
          qcDt: '2023-01-01-07:00',
          qcSet: '2023-01-01-07:00',
          isMngd: true,
          isCommented: false,
          isPsd: true,
          isPsdAsL: true,
          isPsdCuL: true,
          isPsdCoL: true,
          peerGroupStatisticsParameterId: 'param1',
          isAutoUnmanaged: true,
          groupIndex: 0,
        },
        {
          runDetailId: 'detail2',
          runId: 'run2',
          varPct: 0,
          sdi: -0.5,
          bias: -0.1,
          origVal: 200,
          origUpp: 0,
          origLow: 0,
          origTgt: 0,
          qcDt: '2023-01-02-07:00',
          qcSet: '2023-01-02-07:00',
          isMngd: false,
          isCommented: true,
          isPsd: false,
          isPsdAsL: true,
          isPsdCuL: true,
          isPsdCoL: true,
          peerGroupStatisticsParameterId: 'param2',
          isAutoUnmanaged: true,
          groupIndex: -1,
        },
      ],
    };

    const result = component.mapSDIMultipleDataPointsToChartPnt(data);

    expect(result).toEqual(expected);
  });

  it('should return an empty array if data is empty', () => {
    const data: SDIComparisonDataPoints = {
      qcRunId: [],
      qcRunDetailId: [],
      sdi: [],
      bias: [],
      originalValue: [],
      qcDatetime: [],
      qcSet: [],
      isManaged: [],
      isCommented: [],
      isOutlier: [],
      peerGroupStatisticsParameterId: [],
      autoUnmanagedFlags: [false],
      timeZone: [],
      groupIndex: [0],
    };

    const expected: ChartSeries<ChartPnt> = {
      pnts: [],
    };

    const result = component.mapSDIMultipleDataPointsToChartPnt(data);

    expect(result).toEqual(expected);
  });

  it('should map SDI multiple analyzers data correctly', () => {
    const comparisonData: QcComparisonData[] = [
      {
        paramDateRange: {
          conversionRate: 1,
          parameterUnitId: 1,
          parameterId: 1,
          allSeriesInfo: [],
          channelId: 1,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          limitsData: undefined,
          limitsDataPrevious: undefined,
          modelGroup: '',
          parameterCode: '',
          parameterDisplayName: '',
          siteCode: '',
          unitDisplay: '',
          decimalPoint: 0,
          analyzerSerial: '',
          isCountryLimitChart: false,
        },
        seriesData: [],
        hasDataPoint: false,
        channelId: 1,
        parameterUnitId: 1,
        parameterId: 1,
      },
    ];

    const response: SDIMultipleComparisonDataResponse = {
      qcData: {
        parameters: [
          {
            parameterId: 1,
            analyzers: [
              {
                analyzerModel: 'model1',
                analyzerSerialNumber: '123456',
                dataPoints: {
                  qcRunId: ['run1'],
                  qcRunDetailId: ['detail1'],
                  sdi: [1],
                  bias: [1],
                  originalValue: [100],
                  qcDatetime: ['2024-05-15'],
                  qcSet: ['set1'],
                  isManaged: [true],
                  isCommented: [true],
                  isOutlier: [false],
                  peerGroupStatisticsParameterId: ['param1'],
                  autoUnmanagedFlags: [true],
                  timeZone: ['UTC-07:00'],
                  groupIndex: [0],
                },
                parameterUnitId: '',
              },
            ],
            decimalPoint: 2,
            unitDisplay: 'unit1',
            parameterUnitId: 0,
            parameterDisplayName: '',
          },
        ],
        channelId: 0,
      },
      peerGroupStats: {
        peerGroupStatisticsParameterId: ['param1'],
        groupMean: [1],
        groupSd: [1],
        groupCv: [1],
        groupN: [1],
      },
    };

    component.chartFilterParameter = {
      analyzers: [
        {
          modelCode: 'model1',
          serialNumber: '123456',
          siteCode: 'site1',
          isPrimary: true,
        },
        {
          modelCode: 'model2',
          serialNumber: '654321',
          siteCode: 'site2',
          isPrimary: false,
        },
      ],
      selectedMaterialControlLotIds: ['lot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedPeerGroupId: 'peerGroup1',
      selectedFullLotNumbers: [],
      selectedLevel1LotNumber: '',
      selectedLevel2LotNumber: '',
      selectedLevel3LotNumber: '',
      selectedMaterialType: '',
      currentLevelLotNumber: '',
    };

    const result = component.getParameterMapSDIMultipleAnalyzers(comparisonData, response);

    expect(result[0].paramDateRange.unitDisplay).toBe('unit1');
    expect(result[0].paramDateRange.decimalPoint).toBe(2);
    expect(result[0].hasDataPoint).toBe(true);
  });

  it('should call getSDIMultipleComparisonData with correct parameters', () => {
    const activeChannelId = 1;
    const parameterCriteria: ParameterCriteriaDateRange[] = [
      {
        conversionRate: 1,
        parameterId: 1,
        parameterUnitId: 1,
        parameterCode: 'param1',
        parameterDisplayName: 'Param 1',
        unitDisplay: 'U1',
        decimalPoint: 2,
        criteriaDateRange: {
          start: '',
          end: '',
        },
        channelId: 0,
        allSeriesInfo: [],
        siteCode: '',
        modelGroup: '',
        analyzerSerial: '',
        isCountryLimitChart: false,
      },
      {
        conversionRate: 1,
        parameterId: 2,
        parameterUnitId: 2,
        parameterCode: 'param2',
        parameterDisplayName: 'Param 2',
        unitDisplay: 'U2',
        decimalPoint: 2,
        criteriaDateRange: {
          start: '',
          end: '',
        },
        channelId: 0,
        allSeriesInfo: [],
        siteCode: '',
        modelGroup: '',
        analyzerSerial: '',
        isCountryLimitChart: false,
      },
    ];

    component.chartFilterParameter = {
      analyzers: [
        {
          modelCode: 'model1',
          serialNumber: '123456',
          siteCode: 'site1',
          isPrimary: true,
        },
        {
          modelCode: 'model2',
          serialNumber: '654321',
          siteCode: 'site2',
          isPrimary: false,
        },
      ],
      selectedMaterialControlLotIds: ['lot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedPeerGroupId: 'peerGroup1',
      selectedFullLotNumbers: [],
      selectedLevel1LotNumber: '',
      selectedLevel2LotNumber: '',
      selectedLevel3LotNumber: '',
      selectedMaterialType: '',
      currentLevelLotNumber: '',
    };

    jest
      .spyOn(mockQcDataService, 'getSDIMultipleComparisonData')
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      .mockReturnValue(of({} as SDIMultipleComparisonDataResponse));

    component.apiSDIMultipleComparisonCall(activeChannelId, parameterCriteria).subscribe();

    expect(mockQcDataService.getSDIMultipleComparisonData).toHaveBeenCalledWith({
      channelId: activeChannelId,
      analyzers: ['model1^123456', 'model2^654321'],
      parameterIds: [1, 2],
      materialControlLotId: 'lot1',
      startDate: '2024-05-15',
      endDate: '2024-05-20',
      peerGroupId: 'peerGroup1',
    });
  });

  it('should fetch and process comparison data chunks for SDI_MULTIPLE_ANALYZERS chart mode', () => {
    const activeChannelId = 1;
    const parameterList: ParameterDateRangeInfo[] = [
      {
        conversionRate: 1,
        parameterId: 1,
        parameterUnitId: 1,
        parameterCode: 'param1',
        parameterDisplayName: 'Parameter 1',
        unitDisplay: 'unit1',
        decimalPoint: 2,
        criteriaDateRange: {
          start: '2024-05-15',
          end: '2024-05-20',
        },
        limitsData: undefined,
        limitsDataPrevious: undefined,
        channelId: 0,
        allSeriesInfo: [],
        siteCode: '',
        modelGroup: '',
        analyzerSerial: '',
        isCountryLimitChart: false,
      },
    ];

    const analyzerEvent: AnalyzerEventInfo = {
      reagentEvents: [],
      calibrationEvents: [],
      serviceHistories: [],
    };

    const mockResponse: SDIMultipleComparisonDataResponse = {
      qcData: {
        parameters: [
          {
            parameterId: 1,
            analyzers: [
              {
                analyzerModel: 'model1',
                analyzerSerialNumber: '123456',
                dataPoints: {
                  qcRunId: ['run1'],
                  qcRunDetailId: ['detail1'],
                  sdi: [1.0],
                  bias: [0.1],
                  originalValue: [100],
                  qcDatetime: ['2024-05-15'],
                  qcSet: ['set1'],
                  isManaged: [true],
                  isCommented: [false],
                  isOutlier: [false],
                  peerGroupStatisticsParameterId: ['param1'],
                  autoUnmanagedFlags: [true],
                  timeZone: [],
                  groupIndex: [0],
                },
                parameterUnitId: '',
              },
            ],
            decimalPoint: 2,
            unitDisplay: 'unit1',
            parameterUnitId: 0,
            parameterDisplayName: '',
          },
        ],
        channelId: 0,
      },
      peerGroupStats: {
        peerGroupStatisticsParameterId: ['param1'],
        groupMean: [1.0],
        groupSd: [0.1],
        groupCv: [10],
        groupN: [5],
      },
    };

    jest.spyOn(component, 'getParameterList').mockReturnValue(parameterList);
    jest.spyOn(component, 'initializeComparisonData').mockReturnValue([]);
    jest.spyOn(component, 'fetchAndProcessComparisonData');
    jest.spyOn(component, 'apiSDIMultipleComparisonCall').mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'getAnalyzerEvents').mockReturnValue(of(analyzerEvent));

    component.chartMode = CHART_MODE.SDI_MULTIPLE_ANALYZERS;
    component.chartFilterParameter = {
      analyzers: [
        {
          modelCode: 'model1',
          serialNumber: '123456',
          siteCode: 'site1',
          isPrimary: false,
        },
      ],
      selectedFullLotNumbers: ['lot1'],
      selectedDateRange: {
        start: '2024-05-15',
        end: '2024-05-20',
      },
      selectedLevel1LotNumber: '3312',
      selectedLevel2LotNumber: '3314',
      selectedLevel3LotNumber: '3312',
      selectedMaterialType: 'someMaterialType',
      currentLevelLotNumber: 'currentLevelLot1',
      selectedMaterialControlLotIds: ['controlLot1'],
      selectedPeerGroupId: 'peerGroup1',
    };

    component.getChartData(activeChannelId);

    expect(component.getParameterList).toHaveBeenCalledWith(activeChannelId);
    expect(component.initializeComparisonData).toHaveBeenCalledWith(parameterList, activeChannelId);
    expect(component.apiSDIMultipleComparisonCall).toHaveBeenCalledWith(activeChannelId, parameterList);
  });

  describe('compareUnitCode', () => {
    it('should return true if chartMode is SDI_MULTIPLE_ANALYZERS', () => {
      component.chartMode = CHART_MODE.SDI_MULTIPLE_ANALYZERS;

      const result = component.compareUnitCode('value1', 'value2');

      expect(result).toBe(true);
    });

    it('should return true if chartMode is SDI_SINGLE_ANALYZER', () => {
      component.chartMode = CHART_MODE.SDI_SINGLE_ANALYZER;

      const result = component.compareUnitCode('value1', 'value2');

      expect(result).toBe(true);
    });

    it('should return true if values are equal ignoring case', () => {
      component.chartMode = CHART_MODE.ZSCORE_CHART;

      const result = component.compareUnitCode('value', 'VALUE');

      expect(result).toBe(true);
    });

    it('should return false if values are not equal ignoring case', () => {
      component.chartMode = CHART_MODE.ZSCORE_CHART;

      const result = component.compareUnitCode('value1', 'value2');

      expect(result).toBe(false);
    });
  });

  it('should get status disabled is true of export button when view mode is chart', () => {
    component.viewMode = VIEW_MODE.CHART;
    component.chartMode = CHART_MODE.ZSCORE_CHART;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    component.qcMultipleComparisonData = [] as QcComparisonData[];

    const result = component.isDataOrSeriesDataEmpty();

    expect(result).toBe(true);
  });

  it('should get status disabled is true of export button when view mode is chart', () => {
    component.viewMode = VIEW_MODE.CHART;
    component.chartMode = CHART_MODE.ADVANCED_LJ_CHART;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    component.qcResults = [];

    const result = component.isDataOrSeriesDataEmpty();

    expect(result).toBe(true);
  });

  it('should get status disabled is false of export button when view mode is chart', () => {
    component.viewMode = VIEW_MODE.CHART;
    component.chartMode = CHART_MODE.ZSCORE_CHART;
    component.qcMultipleComparisonData = [
      {
        paramDateRange: {
          conversionRate: 1,
          parameterUnitId: 8,
          parameterId: 2,
          parameterCode: 'HGB',
          parameterDisplayName: 'HGB',
          unitDisplay: 'g/dL',
          decimalPoint: 1,
          criteriaDateRange: {
            start: '2024-06-01',
            end: '2024-11-05',
          },
          limitsData: {
            assay: {
              target: 0,
              upper: 3,
              lower: -3,
            },
          },
          limitsDataPrevious: {
            assay: {
              target: 0,
              upper: 3,
              lower: -3,
            },
          },
          allSeriesInfo: [
            {
              label: 'Level 1',
              name: '1',
              isBold: false,
              isDashedStyle: false,
              color: 'var(--red-100)',
              pointIcon: 'circle',
              level: 1,
            },
            {
              label: 'Level 2',
              name: '2',
              isBold: false,
              isDashedStyle: false,
              color: 'var(--green-100)',
              pointIcon: 'square',
              level: 2,
            },
            {
              label: 'Level 3',
              name: '3',
              isBold: false,
              isDashedStyle: false,
              color: '#000000',
              pointIcon: 'triangle',
              level: 3,
            },
          ],
          channelId: 1,
          isComparedToPreviousLot: false,
          siteCode: '',
          modelGroup: '',
          analyzerSerial: '',
          isCountryLimitChart: false,
        },
        seriesData: [
          {
            '1': {
              pnts: [
                {
                  runDetailId: '77d4f138-7d66-4c71-ae65-bb51043e370e',
                  runId: '803995c7-2f5c-45b2-9d2f-1df22dbe12e6',
                  origVal: 5.8603,
                  qcDt: '2024-06-05T08:10:00.0000000',
                  qcSet: '2024-06-05T06:00:00.0000000',
                  isMngd: true,
                  isCommented: false,
                  isPsdAsL: true,
                  isPsdMulrL: true,
                  yValue: 0.010767857142857143,
                  varPct: 0,
                  origUpp: 0,
                  origLow: 0,
                  origTgt: 0,
                  isPsd: true,
                  isPsdCuL: true,
                  isPsdCoL: true,
                  groupIndex: 0,
                },
              ],
              statVal: {
                mean: 5.86835,
                bias: 0,
                sd: 0.034797449619189,
                cv: 0.5929682043366371,
                n: 10,
              },
            },
            reagentEvent: {
              pnts: [],
              evtPnts: {
                reagentEvents: [],
                calibrationEvents: [],
                serviceHistories: [],
              },
            },
          },
        ],
        hasDataPoint: true,
        channelId: 1,
        parameterUnitId: 8,
        parameterId: 2,
        chartIndex: 0,
      },
    ];

    const result = component.isDataOrSeriesDataEmpty();

    expect(result).toBe(true);
  });

  it('should get status disabled is false of export button when view mode is chart', () => {
    component.viewMode = VIEW_MODE.CHART;
    component.chartMode = CHART_MODE.ADVANCED_LJ_CHART;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    component.qcResults = [
      {
        parameterId: 3,
        parameterCode: 'HCT',
        parameterDisplayName: 'HCT',
        unitDisplay: '%',
        decimalPoint: 1,
        levels: [
          {
            level: 3,
            lotNumber: '2323',
            statisticValue: {
              mean: 0.3095,
              bias: -11.571428571428571,
              sd: 0.00101980390271856,
              cv: 0.32950045322085847,
              n: 3,
            },
            dataPoints: [
              {
                qcRunDetailId: '44f3d70b-70af-47a0-9508-429e9c15b950',
                qcRunId: '6eeaa2a5-25e3-4d2e-8941-7fda21cbb508',
                isManaged: true,
                isPassed: false,
                isPassedAssayLimit: true,
                isPassedCustomerLimit: true,
                isPassedCountryLimit: true,
                isCommented: false,
                originalValue: 0.3091,
                variancePercentageValue: -136.333333,
                originalUpperLimit: 0.38,
                originalLowerLimit: 0.32,
                originalTarget: 0.35,
                qcDateTime: '2024-11-05T14:27:00',
                qcSet: '2024-11-05T03:25:00Z',
                assayLimitParameter: undefined,
                customerLimitParameter: undefined,
                countryLimitParameter: undefined,
                advancedData: {
                  isManaged: true,
                },
                xValue: 1730777100000,
                isReagent: false,
              },
            ],
          },
          {
            level: 2,
            lotNumber: '2323',
            statisticValue: {
              mean: 0.32193333333333335,
              bias: -5.313725490196078,
              sd: 0.0205658022508781,
              cv: 6.388217721333016,
              n: 6,
            },
            dataPoints: [
              {
                qcRunDetailId: '0616dda3-8d61-4f94-880b-85c47ff1d5ae',
                qcRunId: '298beb7b-162a-4315-ad5e-54759320486f',
                isManaged: true,
                isPassed: false,
                isPassedAssayLimit: true,
                isPassedCustomerLimit: true,
                isPassedCountryLimit: true,
                isCommented: false,
                originalValue: 0.3121,
                variancePercentageValue: -139.5,
                originalUpperLimit: 0.36,
                originalLowerLimit: 0.32,
                originalTarget: 0.34,
                qcDateTime: '2024-11-05T14:26:00',
                qcSet: '2024-11-05T03:25:00Z',
                assayLimitParameter: undefined,
                customerLimitParameter: undefined,
                countryLimitParameter: undefined,
                advancedData: {
                  isManaged: true,
                },
                xValue: 1730777100000,
                isReagent: false,
              },
            ],
          },
          {
            level: 1,
            lotNumber: '2323',
            statisticValue: {
              mean: 0.175,
              bias: 0,
              sd: 0.0149042499531733,
              cv: 8.51671425895618,
              n: 6,
            },
            dataPoints: [
              {
                qcRunDetailId: '77bc87e8-345f-4aa9-a10a-85974f6601d4',
                qcRunId: '9eec46f3-f7b9-4e0d-8468-232e4a9c4df6',
                isManaged: true,
                isPassed: false,
                isPassedAssayLimit: true,
                isPassedCustomerLimit: true,
                isPassedCountryLimit: true,
                isCommented: false,
                originalValue: 0.1603,
                variancePercentageValue: -133.636364,
                originalUpperLimit: 0.186,
                originalLowerLimit: 0.164,
                originalTarget: 0.175,
                qcDateTime: '2024-11-05T14:25:00',
                qcSet: '2024-11-05T03:25:00Z',
                assayLimitParameter: undefined,
                customerLimitParameter: undefined,
                countryLimitParameter: undefined,
                advancedData: {
                  isManaged: true,
                },
                xValue: 1730777100000,
                isReagent: false,
              },
            ],
          },
        ],
      },
    ];

    const result = component.isDataOrSeriesDataEmpty();

    expect(result).toBe(true);
  });

  it('should return status disabled is true of export button when view mode is table', () => {
    component.viewMode = VIEW_MODE.TABLE;
    component.hasQcDataTable = false;

    const result = component.isDataOrSeriesDataEmpty();

    expect(result).toBe(true);
  });

  it('should return status disabled is false of export button when view mode is table', () => {
    component.viewMode = VIEW_MODE.TABLE;
    component.hasQcDataTable = true;

    const result = component.isDataOrSeriesDataEmpty();

    expect(result).toBe(false);
  });

  it('should filter channels by limit data correctly', () => {
    const channels: ChannelWarning[] = [
      {
        parameters: [
          {
            conversionRate: 1,
            parameterId: 1,
            parameterCode: 'A',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-09-20',
              end: '2024-10-28',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
          {
            conversionRate: 1,
            parameterId: 2,
            parameterCode: 'B',
            unitDisplay: 'g/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-09-20',
              end: '2024-10-28',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        channelId: 0,
        channelName: '',
      },
      {
        parameters: [
          {
            conversionRate: 1,
            parameterId: 3,
            parameterCode: 'C',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-09-20',
              end: '2024-10-28',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
          {
            conversionRate: 1,
            parameterId: 4,
            parameterCode: 'D',
            unitDisplay: 'g/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-09-20',
              end: '2024-10-28',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        channelId: 0,
        channelName: '',
      },
    ];

    const limitData: LimitSetting[] = [
      {
        parameterId: 1,
        parameterCode: 'A',
        unitDisplay: 'mg/L',
      },
      {
        parameterId: 3,
        parameterCode: 'C',
        unitDisplay: 'mg/L',
      },
    ];

    const result = component.filterChannelsByLimitData(channels, limitData);

    expect(result).toBeDefined();
  });

  it('should return an empty array when no channels match the limit data', () => {
    const channels: ChannelWarning[] = [
      {
        parameters: [
          {
            conversionRate: 1,
            parameterId: 1,
            parameterCode: 'A',
            unitDisplay: 'mg/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-09-20',
              end: '2024-10-28',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
          {
            conversionRate: 1,
            parameterId: 2,
            parameterCode: 'B',
            unitDisplay: 'g/L',
            channelId: 0,
            parameterUnitId: 0,
            parameterDisplayName: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '2024-09-20',
              end: '2024-10-28',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        channelId: 0,
        channelName: '',
      },
    ];

    const limitData: LimitSetting[] = [
      {
        parameterId: 3,
        parameterCode: 'C',
        unitDisplay: 'mg/L',
      },
    ];

    const result = component.filterChannelsByLimitData(channels, limitData);

    expect(result).toEqual([]);
  });

  it('should handle empty channelsWarning', () => {
    const channelsWarning: ChannelWarning[] = [];

    component.updateChannels(channelsWarning, false);
    expect(component.channels).toEqual([]);
    expect(component.channelsWarningTrue).toEqual(channelsWarning);
  });

  it('should handle channelsWarning with no parameters', () => {
    const channelsWarning: ChannelWarning[] = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        parameters: [],
      },
    ];

    component.updateChannels(channelsWarning, false);
    expect(component.channels).toEqual([]);
    expect(component.channelsWarningTrue).toEqual(channelsWarning);
  });

  it('should handle channelsWarning with parameters', () => {
    const channelsWarning: ChannelWarning[] = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        parameters: [
          {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 0,
            parameterId: 0,
            parameterCode: '',
            parameterDisplayName: '',
            unitDisplay: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '',
              end: '',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
      },
    ];

    component.updateChannels(channelsWarning, false);
    expect(component.channels.length).toBe(1);
    expect(component.channelsWarningTrue).toEqual(channelsWarning);
  });

  it('should filter channels by limitData', () => {
    component.limitData = [
      {
        parameterId: 1,
        parameterCode: 'parameter1',
        assayLimit: {
          target: 100,
          upperLimit: 110,
          lowerLimit: 90,
        },
        customerLimit: {
          target: 100,
          upperLimit: 110,
          lowerLimit: 90,
          sd: 10,
          cv: 10,
        },
        countryLimit: null,
        limitType: 'assay_limit',
      },
    ];

    const channelsWarning: ChannelWarning[] = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        parameters: [
          {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 0,
            parameterId: 0,
            parameterCode: '',
            parameterDisplayName: '',
            unitDisplay: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '',
              end: '',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
      },
    ];

    jest.spyOn(component, 'filterChannelsByLimitData').mockReturnValue(channelsWarning);
    component.updateChannels(channelsWarning, false);
    expect(component.filterChannelsByLimitData).toHaveBeenCalledWith(channelsWarning, component.limitData);
  });

  it('should filter channels by multiRulesLimitData', () => {
    component.multiRulesLimitData = [
      {
        parameterId: 1,
        parameterCode: 'parameter1',
        assayLimit: {},
      },
    ];

    const channelsWarning: ChannelWarning[] = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        parameters: [
          {
            channelId: 0,
            conversionRate: 1,
            parameterUnitId: 0,
            parameterId: 0,
            parameterCode: '',
            parameterDisplayName: '',
            unitDisplay: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '',
              end: '',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
      },
    ];

    jest.spyOn(component, 'filterChannelsByLimitData').mockReturnValue(channelsWarning);
    component.updateChannels(channelsWarning, false);
    expect(component.filterChannelsByLimitData).toHaveBeenCalledWith(channelsWarning, component.multiRulesLimitData);
  });

  it('should set isWarning and isError correctly', () => {
    const channelsWarning: ChannelWarning[] = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        parameters: [
          {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 0,
            parameterId: 0,
            parameterCode: '',
            parameterDisplayName: '',
            unitDisplay: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '',
              end: '',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        isPassedAssayLimit: false,
        isOutlier: true,
        isWarning: true,
        isPassedCountryLimit: false,
        isPassedCustomerLimit: false,
        isPassedMultiRulesLimit: false,
      },
    ];

    component.chartMode = component.chartModeFilter.COMPARE_TO_XBARM;
    component.updateChannels(channelsWarning, false);
    expect(component.channels[0].isWarning).toBe(true);
    expect(component.channels[0].isError).toBe(true);
  });

  it('should set isWarning and isError correctly when fail assay limit', () => {
    const channelsWarning: ChannelWarning[] = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        parameters: [
          {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 0,
            parameterId: 0,
            parameterCode: '',
            parameterDisplayName: '',
            unitDisplay: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '',
              end: '',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        isPassedAssayLimit: false,
        isOutlier: false,
        isWarning: false,
        isPassedCountryLimit: true,
        isPassedCustomerLimit: true,
        isPassedMultiRulesLimit: true,
      },
    ];

    component.updateChannels(channelsWarning, false);
    expect(component.channels[0].isWarning).toBe(true);
    expect(component.channels[0].isError).toBe(false);
  });

  it('should set isWarning and isError correctly when fail multi rule limit', () => {
    component.isMultiRulesService = true;
    component.isServiceLevel3 = false;

    const channelsWarning: ChannelWarning[] = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        parameters: [
          {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 0,
            parameterId: 0,
            parameterCode: '',
            parameterDisplayName: '',
            unitDisplay: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '',
              end: '',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        isPassedAssayLimit: true,
        isOutlier: false,
        isWarning: false,
        isPassedCountryLimit: true,
        isPassedCustomerLimit: true,
        isPassedMultiRulesLimit: false,
      },
    ];

    component.updateChannels(channelsWarning, false);

    expect(component.channels[0].isWarning).toBe(false);
    expect(component.channels[0].isError).toBe(true);
  });

  it('should set isWarning and isError correctly when fail customer limit', () => {
    const channelsWarning: ChannelWarning[] = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        parameters: [
          {
            conversionRate: 1,
            channelId: 0,
            parameterUnitId: 0,
            parameterId: 0,
            parameterCode: '',
            parameterDisplayName: '',
            unitDisplay: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '',
              end: '',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        isPassedAssayLimit: true,
        isOutlier: false,
        isWarning: false,
        isPassedCountryLimit: true,
        isPassedCustomerLimit: false,
        isPassedMultiRulesLimit: true,
      },
    ];

    component.updateChannels(channelsWarning, false);
    expect(component.channels[0].isWarning).toBe(false);
    expect(component.channels[0].isError).toBe(true);
  });

  it('should set isWarning and isError correctly when fail country limit', () => {
    const channelsWarning: ChannelWarning[] = [
      {
        channelId: 1,
        channelName: 'Channel 1',
        parameters: [
          {
            channelId: 0,
            conversionRate: 1,
            parameterUnitId: 0,
            parameterId: 0,
            parameterCode: '',
            parameterDisplayName: '',
            unitDisplay: '',
            decimalPoint: 0,
            criteriaDateRange: {
              start: '',
              end: '',
            },
            allSeriesInfo: [],
            siteCode: '',
            modelGroup: '',
            analyzerSerial: '',
            isCountryLimitChart: false,
          },
        ],
        isPassedAssayLimit: true,
        isOutlier: false,
        isWarning: false,
        isPassedCountryLimit: false,
        isPassedCustomerLimit: true,
        isPassedMultiRulesLimit: true,
      },
    ];

    component.updateChannels(channelsWarning, true);
    expect(component.channels[0].isWarning).toBe(false);
    expect(component.channels[0].isError).toBe(true);
  });

  it('should enable analyzers and update parameterSelectedAnalyzers correctly', () => {
    const comparisonData: QcComparisonData[] = [
      {
        parameterUnitId: 1,
        seriesData: [],
        hasDataPoint: false,
        paramDateRange: {
          conversionRate: 1,
          channelId: 0,
          parameterUnitId: 0,
          parameterId: 0,
          parameterCode: EMPTY,
          parameterDisplayName: EMPTY,
          unitDisplay: EMPTY,
          decimalPoint: 0,
          criteriaDateRange: {
            start: '2024-05-15',
            end: '2024-05-20',
          },
          allSeriesInfo: [],
          siteCode: EMPTY,
          modelGroup: EMPTY,
          analyzerSerial: EMPTY,
          isCountryLimitChart: false,
        },
        channelId: 0,
      },
    ];

    const response: SDIMultipleComparisonDataResponse = {
      qcData: {
        parameters: [
          {
            parameterId: 1,
            analyzers: [
              {
                analyzerModel: 'model1',
                analyzerSerialNumber: '123456',
                dataPoints: {
                  qcRunId: ['run1'],
                  qcRunDetailId: ['detail1'],
                  sdi: [1],
                  bias: [1],
                  originalValue: [100],
                  qcDatetime: ['2024-05-15'],
                  qcSet: ['set1'],
                  isManaged: [true],
                  isCommented: [true],
                  isOutlier: [false],
                  peerGroupStatisticsParameterId: ['param1'],
                  autoUnmanagedFlags: [true],
                  timeZone: [],
                  groupIndex: [0],
                },
                parameterUnitId: '',
              },
            ],
            decimalPoint: 2,
            unitDisplay: 'unit1',
            parameterUnitId: 0,
            parameterDisplayName: '',
          },
        ],
        channelId: 0,
      },
      peerGroupStats: {
        peerGroupStatisticsParameterId: ['param1'],
        groupMean: [1],
        groupSd: [1],
        groupCv: [1],
        groupN: [1],
      },
    };

    component.chartFilterParameter = MOCK_FILTER_PARAMS;

    component.parameterSelectedAnalyzers = [];

    component.getParameterMapSDIMultipleAnalyzers(comparisonData, response);

    expect(component.chartFilterParameter.analyzers![0].isEnabled).toBeUndefined();
    expect(component.chartFilterParameter.analyzers![1].isEnabled).toBeUndefined();

    expect(component.parameterSelectedAnalyzers).toEqual([]);
  });

  describe('convertMultiRules', () => {
    it('should convert multi-rules correctly', () => {
      const multiRules = {
        '2SD': {
          upperLimit: 15,
          lowerLimit: -15,
          target: 0,
        },
        '3SD': {
          upperLimit: 20,
          lowerLimit: -20,
          target: 0,
        },
      };

      const result = component.convertMultiRules(multiRules, 0.5, 1);

      expect(result).toEqual({
        '2SD': {
          lowerLimit: -7.5,
          target: 0,
          upperLimit: 7.5,
        },
        '3SD': {
          lowerLimit: -10,
          target: 0,
          upperLimit: 10,
        },
      });
    });

    it('should return an empty array if multi-rules is empty', () => {
      const multiRules = {};

      const result = component.convertMultiRules(multiRules, 1, 1);

      expect(result).toEqual({});
    });
  });

  describe('convertCustomerLimit', () => {
    it('should convert customer limit correctly', () => {
      const customerLimit = {
        upperLimit: 15,
        lowerLimit: -15,
        target: 0,
      };

      const result = component.convertCustomerLimit(customerLimit, 0.5, 1);

      expect(result).toEqual({
        upperLimit: 7.5,
        lowerLimit: -7.5,
        target: 0,
      });
    });

    it('should return an empty object if customer limit is empty', () => {
      const customerLimit = {};

      const result = component.convertCustomerLimit(customerLimit, 0.5, 1);

      expect(result).toEqual({});
    });
  });

  describe('convertCountryOrAssayLimit', () => {
    it('should convert country or assay limit correctly', () => {
      const limit = {
        upperLimit: 15,
        lowerLimit: -15,
        target: 0,
      };

      const result = component.convertCountryOrAssayLimit(limit, 0.5, 1);

      expect(result).toEqual({
        upperLimit: 7.5,
        lowerLimit: -7.5,
        target: 0,
      });
    });
  });

  describe('convertUnitCode', () => {
    it('should convert "E3/uL" to "x10<SUP>3</SUP>/&micro;L"', () => {
      const result = component.convertUnitCode('E3/uL');

      expect(result).toBe('x10<SUP>3</SUP>/&micro;L');
    });

    it('should convert "E6/uL" to "x10<SUP>6</SUP>/&micro;L"', () => {
      const result = component.convertUnitCode('E6/uL');

      expect(result).toBe('x10<SUP>6</SUP>/&micro;L');
    });

    it('should return the original value if it does not match the pattern', () => {
      const result = component.convertUnitCode('someValue');

      expect(result).toBe('someValue');
    });

    it('should return the original value if it partially matches the pattern', () => {
      const result = component.convertUnitCode('E/uL');

      expect(result).toBe('E/uL');
    });

    it('should return the original value if it has no exponent', () => {
      const result = component.convertUnitCode('E/uL');

      expect(result).toBe('E/uL');
    });
  });

  describe('onHasQcDataTable', () => {
    it('should set hasQcDataTable to true', () => {
      component.onHasQcDataTable(true);
      expect(component.hasQcDataTable).toBe(true);
    });

    it('should set hasQcDataTable to false', () => {
      component.onHasQcDataTable(false);
      expect(component.hasQcDataTable).toBe(false);
    });
  });

  describe('onExport', () => {
    beforeEach(() => {
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      jest.spyOn(component, 'exportAdvanceChartPdf').mockImplementation(() => {});
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      jest.spyOn(component, 'exportTableViewFile').mockImplementation(() => {});
    });

    it('should call exportAdvanceChartPdf if selectedExportMode is pdf', () => {
      component.selectedExportMode = 'pdf';
      component.onExport();
      expect(component.exportAdvanceChartPdf).toHaveBeenCalled();
      expect(component.exportTableViewFile).not.toHaveBeenCalled();
    });

    it('should call exportTableViewFile if selectedExportMode is csv', () => {
      component.selectedExportMode = 'csv';
      component.onExport();
      expect(component.exportTableViewFile).toHaveBeenCalled();
      expect(component.exportAdvanceChartPdf).not.toHaveBeenCalled();
    });
  });
});
