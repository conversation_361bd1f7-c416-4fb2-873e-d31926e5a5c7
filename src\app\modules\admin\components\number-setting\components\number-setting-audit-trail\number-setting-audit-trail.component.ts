import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AgGridModule } from 'ag-grid-angular';
import { CommonModule } from '@angular/common';
import { DesignSystemModule } from '@sysmex/design-system';

import { AuditTrailLogType } from 'src/app/enums/audit-trail';
import { DialogService } from 'src/app/services/dialog.service';
import { PaginatorComponent } from 'src/app/modules/common/paginator/components/paginator.component';
import { RuleConfigurationAuditTrailDialogBaseComponent } 
  from '../../../rule-configuration-audit-trail-dialog-base/rule-configuration-audit-trail-dialog-base.component';
import { RuleConfigurationService } from 'src/app/modules/admin/services/rule-configuration.service';
import { RuleNumberSettingAuditTrailTableRow } from 'src/app/modules/admin/interfaces/rule-configuration-audit-trail';

@Component({
  selector: 'app-number-setting-audit-trail',
  templateUrl:
    '../../../rule-configuration-audit-trail-dialog-base/rule-configuration-audit-trail-dialog-base.component.html',
  standalone: true,
  imports: [AgGridModule, CommonModule, DesignSystemModule, PaginatorComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NumberSettingAuditTrailComponent
  extends RuleConfigurationAuditTrailDialogBaseComponent<RuleNumberSettingAuditTrailTableRow> {
  @Input() ruleCode?: string;

  constructor(
    override readonly dialogService: DialogService,
    override readonly ruleConfigurationService: RuleConfigurationService,
  ) {
    super(dialogService, ruleConfigurationService);

    this.auditTrailLogType = AuditTrailLogType.RuleNumberSetting;

    this.tableMapperFn = this.ruleConfigurationService.mapRuleNumberSettingAuditTrailData;
  }

  componentOnInit(): void {
    switch (this.ruleCode) {
      case 'rule4':

      case 'rule5':

      case 'rule13':
        this.dialogTitle = $localize`:app-app_dialog-title:Number of Recent Runs Log`;

        this.columnDefs = [
          ...this.sharedColumnDefs,
          {
            field: 'recent_runs',
            headerName: $localize`:app-app_grid-header_label:Number of Recent Runs`,
            cellClass: 'text-right',
            minWidth: 200,
          },
        ];

        break;

      case 'rule6':
        this.dialogTitle = $localize`:title:Number of Consecutive Troubleshooting Events Log`;

        this.columnDefs = [
          ...this.sharedColumnDefs,
          {
            field: 'consecutive_troubleshooting_events',
            headerName: $localize`:app-app_grid-header_label:Number of Consecutive Troubleshooting Events`,
            cellClass: 'text-right',
            minWidth: 320,
          },
        ];

        break;

      case 'rule7':
        this.dialogTitle = 
          $localize`:app-app_dialog-title:Number of Troubleshooting Events & Consecutive Control Run Log`;

        this.columnDefs = [
          ...this.sharedColumnDefs,
          {
            field: 'troubleshooting_events',
            headerName: $localize`:app-app_grid-header_label:Number of Troubleshooting Events`,
            cellClass: 'text-right',
            minWidth: 240,
            headerTooltip: $localize`:app-app_grid-header_label:Number of Troubleshooting Events`,
          },
          {
            field: 'consecutive_control_run_sets',
            headerName: $localize`:app-app_grid-header_label:Number of Consecutive Control Runs`,
            cellClass: 'text-right',
            minWidth: 250,
            headerTooltip: $localize`:app-app_grid-header_label:Number of Consecutive Control Runs`,
          },
        ];

        break;

      case 'rule8':
        this.dialogTitle = $localize`:app-app_dialog-title:Number of Troubleshooting Events & Consecutive Days Log`;

        this.columnDefs = [
          ...this.sharedColumnDefs,
          {
            field: 'troubleshooting_events',
            headerName: $localize`:app-app_grid-header_label:Number of Troubleshooting Events`,
            cellClass: 'text-right',
            minWidth: 300,
          },
          {
            field: 'consecutive_days',
            headerName: $localize`:app-app_grid-header_label:Number of Consecutive Days`,
            cellClass: 'text-right',
            minWidth: 250,
          },
        ];

        break;

      case 'rule9':
        this.dialogTitle = $localize`:app-app_dialog-title:Number of Recent Runs & Difference Allowable Log`;

        this.columnDefs = [
          ...this.sharedColumnDefs,
          {
            field: 'recent_runs',
            headerName: $localize`:app-app_grid-header_label:Number of Recent Runs`,
            cellClass: 'text-right',
            minWidth: 200,
          },
          {
            field: 'difference_allowable_percentage',
            headerName: $localize`:app-app_grid-header_label:Difference Allowable (%)`,
            cellClass: 'text-right',
            minWidth: 200,
          },
        ];

        break;
    }
  }
}
