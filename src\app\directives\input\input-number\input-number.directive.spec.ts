import { ElementRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { TestBed } from '@angular/core/testing';

import { NumberInputDirective } from './input-number.directive';

describe('NumberInputDirective', () => {
  let directive: NumberInputDirective;

  const mockElementRef = {
    nativeElement: {
      attributes: {},
      value: '',
    },
  };

  const mockNgControl = { control: { patchValue: jest.fn() } };

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const event = {
    preventDefault: jest.fn(),
    clipboardData: {
      getData: jest.fn(),
    },
  } as unknown as ClipboardEvent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        NumberInputDirective,
        {
          provide: NgControl,
          useValue: mockNgControl,
        },
        {
          provide: ElementRef,
          useValue: mockElementRef,
        },
      ],
    });

    directive = TestBed.inject(NumberInputDirective);
  });

  it('should create an instance', () => {
    expect(directive).toBeTruthy();
  });

  it('should allow numeric keys and control keys', () => {
    const event = new KeyboardEvent('keydown', {
      key: '5',
    });

    directive.onKeyDown(event);

    expect(event.defaultPrevented).toBeFalsy();
  });

  it('should correctly parse number with leading zeros', () => {
    const result = directive.parseNumber('00123.4500');

    expect(result).toBe('123.4500');
  });

  it('should correctly handle number with decimal and trailing zeros', () => {
    const result = directive.parseNumber('123.4500');

    expect(result).toBe('123.4500');
  });

  it('should allow negative numbers when decimals are enabled', () => {
    directive.decimals = true;

    expect(directive.checkAllowNegative('-123.45')).toBeTruthy();
  });

  it('should match positive integers when decimals are disabled', () => {
    directive.decimals = false;

    expect(directive.validateNumber('123')).toBeTruthy();
  });

  it('should not match non-integer values when decimals are disabled', () => {
    directive.decimals = false;

    expect(directive.validateNumber('123.45')).toBeFalsy();
  });

  it('should match decimal numbers when decimals are enabled', () => {
    directive.decimals = true;

    expect(directive.validateNumber('123.45')).toBeTruthy();
  });

  it('should match numbers with leading and trailing spaces when decimals are enabled', () => {
    directive.decimals = true;

    expect(directive.validateNumber('  123.45  ')).toBeTruthy();
  });

  it('should revert to old value if negative numbers are not allowed and current value is invalid', () => {
    directive.decimals = false;

    directive.negative = false;

    mockElementRef.nativeElement.value = '123.45';

    const oldValue = '123';

    directive.validateValidInput(oldValue);

    expect(mockNgControl.control!.patchValue).toHaveBeenCalledWith(oldValue);
  });

  it('should revert to old value if negative numbers are allowed and current value is invalid', () => {
    directive.decimals = true;

    directive.negative = true;

    mockElementRef.nativeElement.value = 'abc';

    const oldValue = '-123.45';

    directive.validateValidInput(oldValue);

    expect(mockNgControl.control!.patchValue).toHaveBeenCalledWith(oldValue);
  });

  it('should call validateValidInput method after patchValue', () => {
    directive.decimals = true;

    directive.negative = true;

    event.clipboardData!.getData = jest.fn().mockReturnValue('123.45');

    mockElementRef.nativeElement.value = 'oldValue';

    const runSpy = jest.spyOn(directive, 'validateValidInput');

    directive.onPaste(event);

    expect(runSpy).toHaveBeenCalled();
  });

  it('should handle paste with decimals and negative numbers allowed', () => {
    directive.decimals = true;

    directive.negative = true;

    event.clipboardData!.getData = jest.fn().mockReturnValue('12.34abc-56def');

    mockElementRef.nativeElement.value = 'oldValue';

    directive.onPaste(event);

    expect(event.preventDefault).toHaveBeenCalled();
    expect(mockNgControl.control!.patchValue).toHaveBeenCalledWith('12.34-56');
  });

  it('should handle paste with decimals but negative numbers not allowed', () => {
    directive.decimals = true;

    directive.negative = false;

    event.clipboardData!.getData = jest.fn().mockReturnValue('-12.34abc56def');

    mockElementRef.nativeElement.value = 'oldValue';

    directive.onPaste(event);

    expect(event.preventDefault).toHaveBeenCalled();
    expect(mockNgControl.control!.patchValue).toHaveBeenCalled();
  });

  it('should handle paste with negative numbers but decimals not allowed', () => {
    directive.decimals = false;

    directive.negative = true;

    event.clipboardData!.getData = jest.fn().mockReturnValue('12abc-34def');

    mockElementRef.nativeElement.value = 'oldValue';

    directive.onPaste(event);

    expect(event.preventDefault).toHaveBeenCalled();
    expect(mockNgControl.control!.patchValue).toHaveBeenCalled();
  });

  it('should handle change value properly', () => {
    directive.negative = true;

    // Ignore Reason: Access private properties & methods.
    // @ts-ignore
    directive.el.value = '-123.45';

    jest.spyOn(directive, 'parseNumber').mockReturnValue('123.45');

    // @ts-ignore
    directive.handleValueChange();

    // @ts-ignore
    expect(directive.el.control?.patchValue).toHaveBeenCalledWith('-123.45');
  });
});
