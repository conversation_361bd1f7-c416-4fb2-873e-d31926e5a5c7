import { AsyncSubject, Observable } from 'rxjs';
import { Directive, Host, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { map } from 'rxjs/operators';

import { AclWrapperDirective } from './acl-wrapper.directive';
import { EMPTY } from 'src/app/constant';
import { Permission } from './model/acl';
import { StorageService } from 'src/app/services/storage.service';
import { Utils } from 'src/app/helpers/UtilFunctions';

@Directive({
  exportAs: 'AppAcl',
  selector:
    'button[appAcl], div[appAcl], mat-card-content[appAcl],' +
    ' mat-card-header[appAcl], mat-dialog-actions[appAcl], ng-container[appAcl]',
})
export class ButtonAclDirective implements OnInit {
  @Input('appAcl') aclProperty: string = EMPTY;
  @Input() aclFunction: string = EMPTY;
  permission$!: Observable<Permission>;
  permissionFunction!: boolean;

  private readonly _directiveReady$ = new AsyncSubject<string>();

  constructor(
    @Host() private readonly _wrapper: AclWrapperDirective,
    private readonly actRouter: ActivatedRoute,
    private readonly storageService: StorageService,
  ) {
    this.actRouter.data.subscribe((routeData) => {
      this.permission$ = this._directiveReady$.pipe(
        map((property) =>
          this._wrapper.getPermissionByProperty(Utils.isNullOrEmptyString(property) ? routeData['pagecode'] : property),
        ),
      );
    });
  }

  ngOnInit(): void {
    this.storageService.storageChange$.subscribe((change) => {
      if (change.storageArea === 'sessionStorage' && change.key === 'defaultSelectionSite') {
        this.permissionFunction = this._wrapper.getFunctionPermission(this.aclFunction).isAllowed;
      }
    });

    this._directiveReady$.next(this.aclProperty);
    this._directiveReady$.complete();
  }
}
