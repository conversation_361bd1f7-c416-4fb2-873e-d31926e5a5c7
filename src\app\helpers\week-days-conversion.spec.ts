import { readDaysOfWeek, writeDaysOfWeek } from './WeekDaysConversion';

describe('Day of Week functions', () => {
  it('test readDaysOfWeek', () => {
    const selectedDays = 0b0101010; // Selected days are Tuesday, Thursday, and Saturday

    const result = readDaysOfWeek(selectedDays);

    expect(result).toEqual([
      {
        weekday: 'MONDAY',
        value: 1,
        isSelected: false,
      },
      {
        weekday: 'TUESDAY',
        value: 2,
        isSelected: true,
      },
      {
        weekday: 'WEDNESDAY',
        value: 4,
        isSelected: false,
      },
      {
        weekday: 'THURSDAY',
        value: 8,
        isSelected: true,
      },
      {
        weekday: 'FRIDAY',
        value: 16,
        isSelected: false,
      },
      {
        weekday: 'SATURDAY',
        value: 32,
        isSelected: true,
      },
      {
        weekday: 'SUNDAY',
        value: 64,
        isSelected: false,
      },
    ]);
  });

  it('test writeDaysOfWeek', () => {
    const selectedDays = [
      {
        weekday: 'MONDAY',
        value: 1,
        isSelected: false,
      },
      {
        weekday: 'TUESDAY',
        value: 2,
        isSelected: true,
      },
      {
        weekday: 'WEDNESDAY',
        value: 4,
        isSelected: false,
      },
      {
        weekday: 'THURSDAY',
        value: 8,
        isSelected: true,
      },
      {
        weekday: 'FRIDAY',
        value: 16,
        isSelected: false,
      },
      {
        weekday: 'SATURDAY',
        value: 32,
        isSelected: true,
      },
      {
        weekday: 'SUNDAY',
        value: 64,
        isSelected: false,
      },
    ];

    const result = writeDaysOfWeek(selectedDays);

    expect(result).toBe(0b0101010); // Expect Tuesday, Thursday, and Saturday to be selected
  });
});
