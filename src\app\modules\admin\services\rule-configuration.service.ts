import { ColDef, ValueGetterParams } from 'ag-grid-community';
import { Observable, map } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { AuditTrailQueryParams, AuditTrailResponse } from 'src/app/interfaces/audit-trail';
import {
  CELL_STYLE_DEFAULT,
  RULE10_COLUMN_DEFS_BASE,
  RULE12TO13_COLUMN_DEFS_BASE,
  RULE1_COLUMN_DEFS_BASE,
  RULE2TO5_COLUMN_DEFS_BASE,
  RULES,
  RULE_COMPONENTS,
  RULE_CONFIGS,
  SETTING_KEY,
  TEXT_POSITION,
} from '../constants';
import { CocParameter, CocParametersPutRequest, ModelGroup } from '../interfaces/coc-parameters';
import {
  ConstantSettingFilterOptionGetRequest,
  ConstantSettingFilterOptionGetResponse,
  RuleConstantSetting,
  RuleConstantSettingsPutRequest,
} from '../interfaces/constant-setting';
import {
  CreateServiceTicketAuditTrailTableRow,
  PeerGroupSizeAuditTrailResponse,
  PeerGroupSizeAuditTrailTableRow,
  RequiredQcDataPointsAuditTrailTableRow,
  Rule1ConstantSettingAuditTrailResponse,
  Rule1ConstantSettingAuditTrailTableRow,
  Rule2To5ConstantSettingAuditTrailResponse,
  Rule2To5ConstantSettingAuditTrailTableRow,
  RuleAuditTrailResponse,
  RuleBalanceAuditTrailTableRow,
  RuleCocParameterAuditTrailResponse,
  RuleCocParameterAuditTrailTableRow,
  RuleLimitAuditTrailTableRow,
  RuleMultiplierAuditTrailTableRow,
  RuleNumberSettingAuditTrailResponse,
  RuleNumberSettingAuditTrailTableRow,
  RuleParameterAuditTrailResponse,
} from '../interfaces/rule-configuration-audit-trail';
import { PeerGroupSizeSettings, RuleStandardSettings } from '../interfaces/admin-parameters';
import { RuleConfig, RuleEditableParameter, RuleParametersTable, RuleTableData } from '../interfaces/rule';
import { AppConfigService } from 'src/app/services/app-config.service';
import { AuditTrailLogType } from 'src/app/enums/audit-trail';
import { AuditTrailService } from 'src/app/services/audit-trail.service';
import { CONSTANT_SETTING_PARAMETER_TYPE } from 'src/app/constant/localize-constant';
import { HTTP_HEADER_BASE } from 'src/app/constant';
import { NumberInputCellComponent } from '../components/cell-template/number-input-cell/number-input-cell.component';
import { NumberSetting } from '../interfaces/number-setting';
import { Rule12To13ParamSetting } from '../interfaces/rule-param-setting';
import { Utils } from 'src/app/helpers/UtilFunctions';

/**
 * Service to manage rule configurations.
 */
@Injectable({
  providedIn: 'root',
})
export class RuleConfigurationService {
  private readonly apiUrl: string | undefined;
  private readonly ruleConfigurationBasePath = '/api/ruleconfiguration';
  private readonly filterOptionsBasePath = '/api/modelgroupcontrollevel';
  private readonly modelGroupCocBasePath = '/api/modelgroup/coc';
  private readonly standardRuleSettingsPath = '/standard';
  private readonly peerGroupSizePath = '/peergroupsize';
  private readonly constantSettingsPath = '/constantsetting';
  private readonly cocParameterPath = '/cocparameter';
  private readonly requiredQcDataPointPath = '/requireddatapoints';
  private readonly ruleParameterSettingPath = `${this.ruleConfigurationBasePath}/parametersetting`;

  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly auditTrailService: AuditTrailService,
    private readonly http: HttpClient,
  ) {
    this.apiUrl = this.appConfigService.getConfig()?.webApiUrl;
  }

  /**
   * Fetches peer group size settings for a given rule code.
   * @param ruleCode The rule code.
   * @returns An observable of peer group size settings.
   */
  getPeerGroupSizeSettings(ruleCode: string): Observable<PeerGroupSizeSettings[]> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}/${ruleCode}${this.peerGroupSizePath}`;

    return this.http.get<PeerGroupSizeSettings[]>(url);
  }

  /**
   * Updates peer group size settings for a given rule code.
   * @param ruleCode The rule code.
   * @param peerGroupSizeSettings The peer group size settings to update.
   * @returns An observable of void.
   */
  updatePeerGroupSizeSettings(ruleCode: string, peerGroupSizeSettings: PeerGroupSizeSettings[]): Observable<void> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}/${ruleCode}${this.peerGroupSizePath}`;

    const body = JSON.stringify(peerGroupSizeSettings);

    const options = {
      headers: HTTP_HEADER_BASE,
    };

    return this.http.put<void>(url, body, options);
  }

  /**
   * Fetches standard settings for a given rule code.
   * @param ruleCode The rule code.
   * @returns An observable of standard rule settings.
   */
  getStandardSettings(ruleCode: string): Observable<RuleStandardSettings> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}${this.standardRuleSettingsPath}`;

    const options = {
      params: {
        ruleCode: ruleCode,
      },
    };

    return this.http.get<RuleStandardSettings>(url, options);
  }

  /**
   * Updates standard settings for a given rule code.
   * @param ruleCode The rule code.
   * @param settingKey The setting key.
   * @param settingValue The setting value.
   * @returns An observable of void.
   */
  updateStandardSettings(ruleCode: string, settingKey: SETTING_KEY, settingValue: boolean): Observable<void> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}${this.standardRuleSettingsPath}/${ruleCode}`;

    const body = {
      settingKey: settingKey,
      settingValue: settingValue,
    };

    const options = {
      headers: HTTP_HEADER_BASE,
    };

    return this.http.put<void>(url, body, options);
  }

  /**
   * Updates required QC data points.
   * @param requiredQcPoints The required QC points.
   * @returns An observable of void.
   */
  updateRequiredQcDataPoint(requiredQcPoints: number): Observable<void> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}${this.requiredQcDataPointPath}`;

    const body = {
      requiredQcPoints: requiredQcPoints,
    };

    const options = {
      headers: HTTP_HEADER_BASE,
    };

    return this.http.put<void>(url, body, options);
  }

  /**
   * Fetches rule constant settings for a given rule code.
   * @param ruleCode The rule code.
   * @param constantSettingFilterOptionRequest The filter options for constant settings.
   * @returns An observable of rule constant settings.
   */
  getRuleConstantSettings(
    ruleCode: string,
    constantSettingFilterOptionRequest: ConstantSettingFilterOptionGetRequest,
  ): Observable<RuleConstantSetting[]> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}/${ruleCode}${this.constantSettingsPath}`;

    const options = {
      params: {
        modelgroup: constantSettingFilterOptionRequest.modelGroup,
        materialcontrol: constantSettingFilterOptionRequest.materialControl,
        level: constantSettingFilterOptionRequest.level,
      },
    };

    return this.http.get<RuleConstantSetting[]>(url, options).pipe(
      map((res) =>
        res.map((item) => ({
          ...item,
          parameterType: item.keyName
            ? CONSTANT_SETTING_PARAMETER_TYPE[item.keyName]
            : CONSTANT_SETTING_PARAMETER_TYPE[item.parameterType],
        })),
      ),
    );
  }

  /**
   * Updates rule constant settings for a given rule code.
   * @param ruleCode The rule code.
   * @param ruleConstantSettingsPutRequest The request body for updating rule constant settings.
   * @returns An observable of void.
   */
  updateRuleConstantSettings(
    ruleCode: string,
    ruleConstantSettingsPutRequest: RuleConstantSettingsPutRequest,
  ): Observable<void> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}/${ruleCode}${this.constantSettingsPath}`;

    return this.http.put<void>(url, ruleConstantSettingsPutRequest);
  }

  /**
   * Fetches filter options for rule constant settings.
   * @returns An observable of constant setting filter options.
   */
  getRuleConstantSettingsFilterOptions(): Observable<ConstantSettingFilterOptionGetResponse[]> {
    const url = `${this.apiUrl}${this.filterOptionsBasePath}`;

    return this.http.get<ConstantSettingFilterOptionGetResponse[]>(url);
  }

  /**
   * Fetches model groups related to COC parameters.
   * @returns An observable of model groups.
   */
  getModelGroupsRelatedCocParameter(): Observable<ModelGroup[]> {
    const url = `${this.apiUrl}${this.modelGroupCocBasePath}`;

    return this.http.get<ModelGroup[]>(url);
  }

  /**
   * Fetches COC parameters for a given rule code and model group.
   * @param ruleCode The rule code.
   * @param modelGroup The model group.
   * @returns An observable of COC parameters.
   */
  getCocParameters(ruleCode: string, modelGroup: string): Observable<CocParameter[]> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}${this.cocParameterPath}/${ruleCode}`;

    const options = {
      params: {
        modelGroup: modelGroup,
      },
    };

    return this.http.get<CocParameter[]>(url, options);
  }

  /**
   * Updates COC parameters for a given rule code.
   * @param ruleCode The rule code.
   * @param cocParametersPutRequest The request body for updating COC parameters.
   * @returns An observable of void.
   */
  updateCocParameters(ruleCode: string, cocParametersPutRequest: CocParametersPutRequest): Observable<void> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}${this.cocParameterPath}/${ruleCode}`;

    return this.http.put<void>(url, cocParametersPutRequest);
  }

  /**
   * Fetches number settings for a given rule code.
   * @param ruleCode The rule code.
   * @returns An observable of number settings.
   */
  getNumberSettings(ruleCode: string): Observable<NumberSetting[]> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}/${ruleCode}`;

    return this.http.get<NumberSetting[]>(url);
  }

  /**
   * Updates number settings for a given rule code.
   * @param ruleCode The rule code.
   * @param numberSettingsRequest The request body for updating number settings.
   * @returns An observable of number settings.
   */
  updateNumberSettings(ruleCode: string, numberSettingsRequest: NumberSetting[]): Observable<NumberSetting[]> {
    const url = `${this.apiUrl}${this.ruleConfigurationBasePath}/${ruleCode}`;

    return this.http.put<NumberSetting[]>(url, numberSettingsRequest);
  }

  /**
   * Fetches rule parameter settings for a given rule code.
   * @param ruleCode The rule code.
   * @returns An observable of rule parameter settings.
   */
  getRuleParameterSettings(ruleCode: string): Observable<Rule12To13ParamSetting> {
    const url = `${this.apiUrl}${this.ruleParameterSettingPath}/${ruleCode}`;

    return this.http.get<Rule12To13ParamSetting>(url);
  }

  /**
   * Updates rule parameter settings for a given rule code.
   * @param ruleCode The rule code.
   * @param ruleParamSettingRequest The request body for updating rule parameter settings.
   * @returns An observable of void.
   */
  updateRuleParameterSettings(ruleCode: string, ruleParamSettingRequest: Rule12To13ParamSetting): Observable<void> {
    const url = `${this.apiUrl}${this.ruleParameterSettingPath}/${ruleCode}`;

    return this.http.patch<void>(url, ruleParamSettingRequest);
  }

  /**
   * Calculates TEA value.
   * @param params The value getter parameters.
   * @returns The calculated TEA value.
   */
  calculateTEA(params: ValueGetterParams): string {
    const clinicalTEA = params.data.clinicalTEA;

    const multiplier = params.data.multiplier;

    return Utils.roundedNumber(clinicalTEA * multiplier, 4);
  }

  /**
   * Creates table configuration for a given rule code and table ID.
   * @param ruleCode The rule code.
   * @param tableId The table ID.
   * @param tableIdSetting The table ID setting.
   * @param hasSetting Whether the table has settings.
   * @returns The table configuration.
   */
  createTableConfig(
    ruleCode: string,
    tableId: string,
    tableIdSetting: RuleParametersTable | undefined,
    hasSetting = false,
  ) {
    if (!hasSetting) {
      return;
    }

    return {
      columnDefs: this.createRuleColumnDefs(ruleCode, tableId),
      dataMappingFuncKey: tableIdSetting?.dataMappingFunc,
    };
  }

  /**
   * Fetches rule parameters table for a given rule configuration and table ID.
   * @param ruleConfig The rule configuration.
   * @param tableId The table ID.
   * @returns The rule parameters table.
   */
  getRuleParametersTable(ruleConfig: RuleConfig | undefined, tableId: string): RuleParametersTable | undefined {
    if (!ruleConfig) {
      return;
    }

    switch (tableId) {
      case RULE_COMPONENTS.constantSettings:
        return ruleConfig.constantSettingsTable;

      case RULE_COMPONENTS.multiplierTea:
        return ruleConfig.multiplierTeaTable;

      case RULE_COMPONENTS.balanceTea:
        return ruleConfig.balanceTeaTable;

      case RULE_COMPONENTS.upperLowerLimits:
        return ruleConfig.upperLowerLimitTable;

      case RULE_COMPONENTS.cocParameter:
        return ruleConfig.cocParameterTable;

      default:
        return ruleConfig.constantSettingsTable;
    }
  }

  /**
   * Fetches columns for a given rule code and table ID.
   * @param ruleCode The rule code.
   * @param tableId The table ID.
   * @returns The columns.
   */
  getCols(ruleCode: string, tableId: string) {
    const ruleConfig = RULE_CONFIGS.find((rule) => rule.ruleCode === ruleCode);

    const settingTable = this.getRuleParametersTable(ruleConfig, tableId);

    const colDefs = this.getBaseColumnDefs(ruleCode);

    return {
      editableCols: settingTable?.editableColumns,
      colDefs,
    };
  }

  /**
   * Fetches base column definitions for a given rule code.
   * @param ruleCode The rule code.
   * @returns The base column definitions.
   */
  getBaseColumnDefs(ruleCode: string): ColDef[] {
    switch (ruleCode) {
      case RULES.rule1:
        return Utils.deepClone(RULE1_COLUMN_DEFS_BASE);

      case RULES.rule10:
        return Utils.deepClone(RULE10_COLUMN_DEFS_BASE);

      case RULES.rule12:

      case RULES.rule13:
        return Utils.deepClone(RULE12TO13_COLUMN_DEFS_BASE);

      default:
        return Utils.deepClone(RULE2TO5_COLUMN_DEFS_BASE);
    }
  }

  /**
   * Creates rule column definitions for a given rule code and table ID.
   * @param ruleCode The rule code.
   * @param tableId The table ID.
   * @returns The rule column definitions.
   */
  createRuleColumnDefs(ruleCode: string, tableId: string): ColDef[] {
    const ruleRbcBalanceCheck = [RULES.rule10, RULES.rule12, RULES.rule13];
    const { colDefs, editableCols } = this.getCols(ruleCode, tableId);

    editableCols?.forEach((col: RuleEditableParameter) => {
      const cellRendererParams = {
        constraints: col.constraints,
        ...(ruleRbcBalanceCheck.includes(ruleCode) && { editingField: col.id }),
      };

      colDefs.push({
        field: col.id,
        headerName: col.label,
        cellClass: TEXT_POSITION.LEFT,
        headerClass: TEXT_POSITION.LEFT,
        editable: false,
        autoHeight: true,
        cellRenderer: NumberInputCellComponent,
        cellRendererParams,
      });
    });

    if (tableId === 'multiplierTeaSettings') {
      const field = ruleCode === RULES.rule12 ? 'rule12Tea' : 'rule13Tea';
      const headerName =
		$localize`:app-app_grid-header_label: Rule ${ruleCode === RULES.rule12 ? 12 : 13} TEA (%) (Calculated)`;

      colDefs.push({
        field,
        headerName,
        cellClass: TEXT_POSITION.LEFT,
        headerClass: TEXT_POSITION.LEFT,
        flex: 1,
        valueGetter: (params: ValueGetterParams) => this.calculateTEA(params),
        filter: false,
        floatingFilter: false,
        cellStyle: CELL_STYLE_DEFAULT,
      });
    }

    return colDefs;
  }

  /**
   * Fetches rule configuration audit trail log.
   * @param type The audit trail log type.
   * @param key The key.
   * @param query The query parameters.
   * @param mapperFn The mapper function.
   * @returns An observable of the mapped data.
   */
  getRuleConfigurationAuditTrailLog<T>(
    type: AuditTrailLogType,
    key: string,
    query: AuditTrailQueryParams,
    mapperFn: (data: AuditTrailResponse[]) => T[],
  ): Observable<T[]> {
    return this.auditTrailService.getHistoryLists(type, key, query).pipe(map(mapperFn));
  }

  /**
   * Maps required QC data points audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapRequiredQcDataPointsAuditTrailData(data: AuditTrailResponse[]): RequiredQcDataPointsAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const change: RuleAuditTrailResponse = JSON.parse(item.historyValues);

      if (!change) {
        return [];
      }

      return {
        updatedAt: item.createdAt,
        updatedBy: item.createdBy,
        requiredQcDataPoint: change.required_qc_data_point,
      };
    });
  }

  /**
   * Maps create service ticket audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapCreateServiceTicketAuditTrailData(data: AuditTrailResponse[]): CreateServiceTicketAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const change: RuleAuditTrailResponse = JSON.parse(item.historyValues);

      if (!change) {
        return [];
      }

      return {
        updatedAt: item.createdAt,
        updatedBy: item.createdBy,
        isEnabled: change.is_enabled,
        isCreateServiceTicket: change.is_create_service_ticket,
      };
    });
  }

  /**
   * Maps rule number setting audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapRuleNumberSettingAuditTrailData(data: AuditTrailResponse[]): RuleNumberSettingAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const changes: RuleNumberSettingAuditTrailResponse[] = JSON.parse(item.historyValues);

      if (!changes) {
        return [];
      }

      return changes.reduce(
        (result, change) => ({
          ...result,
          [change.setting_key]:
            change.setting_key === 'difference_allowable_percentage'
              ? Math.round((change.setting_value * 100 + Number.EPSILON) * 100) / 100
              : change.setting_value,
        }),
        {
          updatedAt: item.createdAt,
          updatedBy: item.createdBy,
        },
      );
    });
  }

  /**
   * Maps rule 1 constant setting audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapRule1ConstantSettingAuditTrailData(data: AuditTrailResponse[]): Rule1ConstantSettingAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const change: Rule1ConstantSettingAuditTrailResponse = JSON.parse(item.historyValues);

      if (!change) {
        return [];
      }

      return {
        updatedAt: item.createdAt,
        updatedBy: item.createdBy,
        ebqcl: change.ebqcl * 100,
        teaMultiplier: change.tea_multiplier,
      };
    });
  }

  /**
   * Maps rule 2 to 5 constant setting audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapRule2To5ConstantSettingAuditTrailData(data: AuditTrailResponse[]): Rule2To5ConstantSettingAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const change: Rule2To5ConstantSettingAuditTrailResponse = JSON.parse(item.historyValues);

      if (!change) {
        return [];
      }

      return {
        updatedAt: item.createdAt,
        updatedBy: item.createdBy,
        value: change.value,
      };
    });
  }

  /**
   * Maps rule COC parameter audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapRuleCocParameterAuditTrailData(data: AuditTrailResponse[]): RuleCocParameterAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const change: RuleCocParameterAuditTrailResponse = JSON.parse(item.historyValues);

      if (!change) {
        return [];
      }

      return {
        updatedAt: item.createdAt,
        updatedBy: item.createdBy,
        lowerLimit: change.lower_limit,
        upperLimit: change.upper_limit,
      };
    });
  }

  /**
   * Maps rule multiplier audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapRuleMultiplierAuditTrailData(data: AuditTrailResponse[]): RuleMultiplierAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const change: RuleParameterAuditTrailResponse = JSON.parse(item.historyValues);

      if (!change) {
        return [];
      }

      return {
        updatedAt: item.createdAt,
        updatedBy: item.createdBy,
        multiplier: change.multiplier,
        clinicalTea: change.clinical_tea,
        calculatedTea: Number(Utils.roundedNumber(change.clinical_tea * change.multiplier, 4)),
      };
    });
  }

  /**
   * Maps rule balance audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapRuleBalanceAuditTrailData(data: AuditTrailResponse[]): RuleBalanceAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const change: RuleParameterAuditTrailResponse = JSON.parse(item.historyValues);

      if (!change) {
        return [];
      }

      return {
        updatedAt: item.createdAt,
        updatedBy: item.createdBy,
        balanceTea: Number(change.balance_tea),
      };
    });
  }

  /**
   * Maps rule limit audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapRuleLimitAuditTrailData(data: AuditTrailResponse[]): RuleLimitAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const change: RuleParameterAuditTrailResponse = JSON.parse(item.historyValues);

      if (!change) {
        return [];
      }

      return {
        updatedAt: item.createdAt,
        updatedBy: item.createdBy,
        lowerLimit: change.lower_limit,
        upperLimit: change.upper_limit,
      };
    });
  }

  /**
   * Maps peer group size audit trail data.
   * @param data The audit trail data.
   * @returns The mapped data.
   */
  mapPeerGroupSizeAuditTrailData(data: AuditTrailResponse[]): PeerGroupSizeAuditTrailTableRow[] {
    return data.flatMap((item) => {
      const change: PeerGroupSizeAuditTrailResponse = JSON.parse(item.historyValues);

      if (!change) {
        return [];
      }

      return {
        updatedAt: item.createdAt,
        updatedBy: item.createdBy,
        size: change.size,
      };
    });
  }

  /**
   * Adjusts rule value integer to decimal zero.
   * @param data The data to adjust.
   * @returns The adjusted data.
   */
  adjustRuleValueIntegerToDecimalZero<T extends RuleTableData | RuleNumberSettingAuditTrailTableRow>(data: T[]): T[] {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const fields = [
      'ebqcl',
      'teaMultiplier',
      'value',
      'lowerLimit',
      'upperLimit',
      'multiplier',
      'clinicalTEA',
      'balanceTEA',
      'calculatedTea',
      'difference_allowable_percentage',
      'balanceTea',
      'clinicalTea',
    ] as (keyof T)[];

    return data.map((item) => {
      fields.forEach((field) => {
        if (field in item) {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          item[field] = Utils.roundedNumber(parseFloat(item[field] as string), 4) as T[keyof T];
        }
      });

      return item;
    });
  }
}
