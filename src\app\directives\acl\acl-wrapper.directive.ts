import { Directive, Input, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { AsyncSubject } from 'rxjs';

import { DEFAULT_PERMISSION, EMPTY } from 'src/app/constant';
import { FunctionPermission, Permission } from './model/acl';
import { PageAcl, ServicePageAcl } from 'src/app/interfaces/user';
import { RESTRICTED_FUNCTIONS_LOOKUP } from './model/pages-definition.model';
import { Utils } from 'src/app/helpers/UtilFunctions';

import { PmsService } from 'src/app/services/app-permission.service';
import { UserStoreService } from 'src/app/services/user-store.service';

@Directive({
  exportAs: 'AclWrapper',
  selector: '[appAclWrapper]',
})
export class AclWrapperDirective implements OnInit, OnDestroy {
  @Input('appAclWrapper') parentCode: string = EMPTY;
  private _permissions$!: PageAcl[];
  private packagePermissions!: ServicePageAcl;
  private readonly _destroy$ = new AsyncSubject();
  private pageCode: string = EMPTY;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly permissionService: PmsService,
    private readonly userStoreService: UserStoreService,
  ) {}

  ngOnInit(): void {
    this.initializePermissions();
  }

  private initializePermissions(): void {
    if (Utils.isNullOrEmptyString(this.parentCode)) {
      this.activatedRoute.data.subscribe((routeData) => {
        this.parentCode = routeData['parentcode'];
        this.pageCode = routeData['pagecode'];

        this.setPermissions(this.parentCode, this.pageCode);
      });
    } else {
      this.setPermissions(this.parentCode, this.parentCode);
    }
  }

  private setPermissions(parentCode: string, pageCode: string): void {
    this._permissions$ = this.getGroupPages(parentCode);
    this.packagePermissions = this.getServicePageAcl(pageCode);
  }

  ngOnDestroy(): void {
    this._destroy$.next(null);
    this._destroy$.complete();
  }

  public getGroupPages(pageCode: string) {
    const groupAcl = this.findPageByCode(pageCode, this.permissionService.page);

    if (groupAcl) {
      return groupAcl.pages;
    }

    return [];
  }

  private getServicePageAcl(pageCode: string): ServicePageAcl {
    const isAllowedAccess = this.userStoreService.hasSitePackageLevel3();
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const functions = RESTRICTED_FUNCTIONS_LOOKUP[pageCode as keyof typeof RESTRICTED_FUNCTIONS_LOOKUP] ?? [];

    return {
      pageCode,
      functions: functions.map((func) => ({
        functionCode: func,
        canAccess: isAllowedAccess,
      })),
    };
  }

  getPermissionByProperty(property: string): Permission {
    const permissionLookup = this._permissions$.reduce((lookup: Record<string, Permission>, page) => {
      lookup[page.pageCode] = {
        read: true,
        write: page.canWrite,
      };

      return lookup;
    }, {});

    return (
      permissionLookup[property] || {
        read: DEFAULT_PERMISSION.read,
        write: DEFAULT_PERMISSION.write,
      }
    );
  }

  getFunctionPermission(functionCode: string): FunctionPermission {
    this.packagePermissions = this.getServicePageAcl(this.pageCode);

    const functionPermissionLookup = this.packagePermissions.functions.reduce(
      (lookup: Record<string, boolean>, func) => {
        lookup[func.functionCode] = func.canAccess;

        return lookup;
      },
      {},
    );

    return { isAllowed: functionPermissionLookup[functionCode] || false };
  }

  findPageByCode(targetCode: string, pageList: PageAcl[]): PageAcl | undefined {
    const pageLookup = (code: string, pages: PageAcl[]): PageAcl | undefined => {
      for (const page of pages) {
        if (page.pageCode === code) {
          return page;
        }

        const foundPage = pageLookup(code, page.pages);

        if (foundPage) {
          return foundPage;
        }
      }

      return undefined;
    };

    return pageLookup(targetCode, pageList);
  }

  getPermissionsByPageCode(pageCode: string): Permission {
    const page = this.findPageByCode(pageCode, this.permissionService.page);

    if (page) {
      return {
        read: page.isMenuItem,
        write: page.canWrite,
      };
    }

    return {
      read: DEFAULT_PERMISSION.read,
      write: DEFAULT_PERMISSION.write,
    };
  }
}
