import { ElementRef } from '@angular/core';
import { MatTooltip } from '@angular/material/tooltip';

import { EMPTY } from '../constant';
import { TruncateTextDirective } from './truncate-text.directive';

describe('TruncateTextDirective', () => {
  let directive: TruncateTextDirective;
  let elementRef: ElementRef<HTMLElement>;
  let mockElement: HTMLElement;

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const tooltipMock = {
    show: jest.fn(),
    hide: jest.fn(),
    message: EMPTY,
    position: 'above',
  } as unknown as MatTooltip;

  beforeEach(() => {
    mockElement = document.createElement('div');
    elementRef = new ElementRef(mockElement);
    directive = new TruncateTextDirective(elementRef, tooltipMock);
  });

  it('should emit empty string when disabled is set to true', () => {
    directive.disabled = true;

    expect(tooltipMock.message).toBe(EMPTY);
  });

  it('should call checkTextOverflow when disabled is set to false', () => {
    jest.spyOn(directive, 'checkTextOverflow');

    directive.disabled = false;

    expect(directive.checkTextOverflow).toHaveBeenCalled();
  });

  it('should show tooltip on mouse enter', () => {
    directive.onMouseEnter();
    expect(tooltipMock.show).toHaveBeenCalled();
  });

  it('should hide tooltip on mouse leave', () => {
    directive.onMouseLeave();
    expect(tooltipMock.hide).toHaveBeenCalled();
  });

  it('should emit truncated text when text overflows', () => {
    directive.elementTruncate = document.createElement('button');

    directive.element.setAttribute('innerText', 'This is a long text that should be truncated');

    Object.defineProperty(directive.element, 'scrollWidth', { value: 200 });
    Object.defineProperty(directive.element, 'clientWidth', { value: 100 });

    directive.checkTextOverflow();

    expect(tooltipMock.message).toBe(directive.element.innerText);
  });

  it('should emit empty string when text does not overflow', () => {
    directive.elementTruncate = document.createElement('button');

    directive.element.setAttribute('innerText', 'This is a long text that should be truncated');

    Object.defineProperty(directive.element, 'scrollWidth', { value: 100 });
    Object.defineProperty(directive.element, 'clientWidth', { value: 100 });

    directive.checkTextOverflow();

    expect(tooltipMock.message).toBe(EMPTY);
  });

  it('should set elementTruncate correctly in getElementTruncate', () => {
    mockElement = document.createElement('button');

    const span = document.createElement('span');

    span.classList.add('mdc-button__label');
    span.innerText = 'Button Label';
    mockElement.appendChild(span);

    directive.element = mockElement;

    directive.getElementTruncate();

    expect(directive.elementTruncate).toBe(mockElement.querySelector('.mdc-button__label'));
  });

  it('should return correct line height in pixels', () => {
    mockElement.style.fontSize = '16px';
    mockElement.style.lineHeight = '24px';

    const lineHeight = directive.getLineHeightInPixels(mockElement);

    expect(lineHeight).toBe(24);
  });

  it('should return correct line height in pixels when line height is normal', () => {
    mockElement.style.fontSize = '16px';
    mockElement.style.lineHeight = 'normal';

    const lineHeight = directive.getLineHeightInPixels(mockElement);

    expect(lineHeight).toBe(16 * 1.2);
  });
});
