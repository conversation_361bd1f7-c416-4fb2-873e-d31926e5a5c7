import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AgGridModule } from 'ag-grid-angular';
import { CommonModule } from '@angular/common';
import { DesignSystemModule } from '@sysmex/design-system';
import { ValueGetterParams } from 'ag-grid-community';

import { AuditTrailLogType } from 'src/app/enums/audit-trail';
import { CreateServiceTicketAuditTrailTableRow } from 'src/app/modules/admin/interfaces/rule-configuration-audit-trail';
import { DialogService } from 'src/app/services/dialog.service';
import { PaginatorComponent } from 'src/app/modules/common/paginator/components/paginator.component';
import { RuleConfigurationAuditTrailDialogBaseComponent } 
  from '../../../rule-configuration-audit-trail-dialog-base/rule-configuration-audit-trail-dialog-base.component';
import { RuleConfigurationService } from 'src/app/modules/admin/services/rule-configuration.service';

@Component({
  selector: 'app-create-service-ticket-audit-trail',
  templateUrl:
    '../../../rule-configuration-audit-trail-dialog-base/rule-configuration-audit-trail-dialog-base.component.html',
  standalone: true,
  imports: [AgGridModule, CommonModule, DesignSystemModule, PaginatorComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateServiceTicketAuditTrailComponent
  extends RuleConfigurationAuditTrailDialogBaseComponent<CreateServiceTicketAuditTrailTableRow> {
  constructor(
    override readonly dialogService: DialogService,
    override readonly ruleConfigurationService: RuleConfigurationService,
  ) {
    super(dialogService, ruleConfigurationService);

    this.dialogTitle = $localize`:app-app_dialog-title:Enable/Service Ticket Log`;

    this.columnDefs = [
      ...this.sharedColumnDefs,
      {
        field: 'isEnabled',
        headerName: $localize`:app-app_grid-header_label:Status`,
        minWidth: 150,
        valueGetter: (params: ValueGetterParams<CreateServiceTicketAuditTrailTableRow>) =>
          params.data?.isEnabled ? $localize`:app-app_label:Enabled` : $localize`:app-app_label:Disabled`,
      },
      {
        field: 'isCreateServiceTicket',
        headerName: $localize`:app-app_grid-header_label:Service Ticket`,
        minWidth: 150,
        valueGetter: (params: ValueGetterParams<CreateServiceTicketAuditTrailTableRow>) =>
          params.data?.isCreateServiceTicket ? $localize`:app-app_label:On` : $localize`:app-app_label:Off`,
      },
    ];

    this.auditTrailLogType = AuditTrailLogType.Rule;

    this.tableMapperFn = this.ruleConfigurationService.mapCreateServiceTicketAuditTrailData;
  }

  override componentOnInit(): void {
    return;
  }
}
