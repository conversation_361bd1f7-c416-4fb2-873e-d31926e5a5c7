import { ERROR_CODE } from 'src/app/enums/errors';
import { Lot } from './filter-data-info';

export interface AssaySettingFilterOptions {
  modelGroups: string;

  materialControlName: string;

  lotNumbers: Lot;
}

export interface DefaultFilterParams {
  materialControlId: string;

  modelGroupName: string;

  lotNumber: string;
}

export interface ModelGroupControlLevelResponse {
  modelGroup: string;

  materialControlName: string;

  modelGroupDisplay: string;

  levels: number[];
}

export interface AssaySetting {
  parameterCode: string;

  unitCode: string;

  parameterUnitId: number;

  maximumNumberOfLevels: number;

  parameterLimits: ParameterLimits[];
}

export interface ParameterLimits {
  levelName: string;

  fullLot: string;

  parameterAssayLimitDetail: AssayLimitDetail;
}

export interface AssayLimitDetail {
  assayMean: number;

  upperLimit: number;

  lowerLimit: number;

  decimalPoint: number;
}

export interface AssayLimitsAuditTrailDialogParams {
  parameterCode: string;

  parameterUnitId: number;

  parameterLimits: ParameterLimits[];

  lotId: string;

  modelGroup: string;
}

export interface AssayLimitsAuditTrailsTableRow {
  createdBy: string;

  createdAt: string;

  parameterUnitId: number;

  parameterLimits: ParameterLimits[];
}

export type AssayLimitField = 'assayMean' | 'upperLimit' | 'lowerLimit';

export interface ParameterAuditTrailLimitsRaw {
  createdBy: string;

  createdAt: string;

  parameter_unit_id: number;

  levels: ParameterAuditTrailLimits[];
}

export interface ParameterAuditTrailLimits {
  level: number;

  lower_limit: number;

  target: number;

  upper_limit: number;
}

export enum AssayUploadPeriod {
  TODAY = 'today',
  ONE_WEEK = 'oneWeek',
  ONE_MONTH = 'oneMonth',
  ALL = 'all'
}

// eslint-disable-next-line @stylistic/comma-dangle
export enum AssayUploadHistoryStatus {
  IN_PROGRESS = 1,
  ERROR = 2,
  COMPLETED = 3,
  WAITING = 4,
}

export interface AssayUploadHistoryParams {
  startDate: string;

  endDate: string;

  page: number;

  numberOfItems: number;

  sortOption: number;
}

export interface AssayUploadHistory {
  assayUploadHistoryId: number;

  receivedDate: string;

  fileName: string;

  errors: AssayUploadHistoryErrorDetail[];
}

export interface AssayUploadHistoryErrorDetail {
  errorCode: ERROR_CODE;

  errorDetail: string;
}

export interface DownloadAssayUploadResponse {
  fileName: string;

  presignedUrl: string;
}

export interface AssayLimitLatestLot {
  materialControlId: number;

  modelGroupName: string;

  lotNumber: string;
}
