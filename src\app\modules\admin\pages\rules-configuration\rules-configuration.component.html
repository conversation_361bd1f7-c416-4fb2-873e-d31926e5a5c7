<div class="flex-col bg-[var(--grey-20)] w-full">
  <mat-card appearance="outlined" class="p-6 mat-typography mat-mdc-card flex-col mat-mdc-card-outlined w-full">
    <mat-card-title class="pb-7">
      <div class="text-[var(--grey-200)] text-2xl font-bold">
        <span i18n>Level 3 Rules</span>
      </div>
    </mat-card-title>
    <mat-card-content class="!p-0">
      <app-required-qc-data-points></app-required-qc-data-points>
      <mat-tab-group (selectedTabChange)="onTabChange($event)">
        @for (rule of rules; track $index) {
          <mat-tab [disabled]="rule === 'rule11'">
            <ng-template mat-tab-label>
              <span i18n>Rule {{ $index + 1 }}</span>
            </ng-template>
            @if (rule === activatedTab) {
              @if (hasAccessibleSite) {
                <div class="pt-4 text-[var(--grey-200)] text-xl font-bold">
                  {{ ruleStandardSettings?.description }}
                </div>
                <app-rule-configuration-setting [ruleCodeSelected]="activatedTab"></app-rule-configuration-setting>
              } @else if (!firstLoad) {
                <div class="flex justify-center items-center h-full gap-1">
                  <mat-icon svgIcon="info48"></mat-icon>
                  <div class="text-[var(--grey-500)] text-2xl">
                    <span i18n="@@app-app_text-no_site_granted">No site has been granted</span>
                  </div>
                </div>
              }
            }
          </mat-tab>
        }
      </mat-tab-group>
    </mat-card-content>
  </mat-card>
</div>
