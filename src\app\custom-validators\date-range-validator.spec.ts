import { FormControl } from '@angular/forms';
import moment from 'moment';

import { DateRangeValidatorDirective } from './date-range-validator';

describe('DateRangeValidatorDirective', () => {
  let directive: DateRangeValidatorDirective;

  beforeEach(() => {
    directive = new DateRangeValidatorDirective();
  });

  it('should return { required: true } if control value is missing', () => {
    expect(directive.validate(new FormControl(null))).toEqual({ required: true });
  });

  it('should return { required: true } if both start and end dates are missing', () => {
    expect(
      directive.validate(
        new FormControl({
          start: null,
          end: null,
        }),
      ),
    ).toEqual({ required: true });
  });

  it('should return { dateInvalid: true } if start date is missing and end date is present', () => {
    expect(
      directive.validate(
        new FormControl({
          start: null,
          end: moment().toDate(),
        }),
      ),
    ).toEqual({ dateInvalid: true });
  });

  it('should return { dateInvalid: true } if end date is missing and start date is present', () => {
    expect(
      directive.validate(
        new FormControl({
          start: moment().toDate(),
          end: null,
        }),
      ),
    ).toEqual({ dateInvalid: true });
  });

  it('should return { dateInvalid: true } if start date is after end date', () => {
    expect(
      directive.validate(
        new FormControl({
          start: moment('2024-01-02').toDate(),
          end: moment('2024-01-01').toDate(),
        }),
      ),
    ).toEqual({ dateInvalid: true });
  });

  it('should return { dateInvalid: true } if start date is before minDate', () => {
    directive.minDate = moment('2022-12-31').toDate();

    expect(
      directive.validate(
        new FormControl({
          start: moment('2022-12-01').toDate(),
          end: moment('2023-01-02').toDate(),
        }),
      ),
    ).toEqual({ dateInvalid: true });
  });

  it('should return { dateInvalid: true } if end date is after maxDate', () => {
    directive.maxDate = moment('2024-09-16').toDate();

    expect(
      directive.validate(
        new FormControl({
          start: moment('2023-12-30').toDate(),
          end: moment('2024-09-17').toDate(),
        }),
      ),
    ).toEqual({ dateInvalid: true });
  });

  it('should return null if dates are valid and within range', () => {
    directive.minDate = moment('2024-01-01').toDate();
    directive.maxDate = moment('2024-12-31').toDate();

    expect(
      directive.validate(
        new FormControl({
          start: moment('2024-01-01').toDate(),
          end: moment('2024-12-31').toDate(),
        }),
      ),
    ).toBeNull();
  });
});
