import { ActivatedRoute, NavigationEnd, Router, provideRouter } from '@angular/router';
import { ChangeDetectorRef, ElementRef } from '@angular/core';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { BootstrapComponent } from './bootstrap.component';
import { DialogService } from '../services/dialog.service';
import { EMPTY } from '../constant';
import { EventParam } from '../models/event-items';
import { EventUpdateAppInitStatus } from '../models/event-params';
import { LoadingService } from '../services/loading.service';
import { RealTimeNotifyService } from '../services/realtime-notify.service';
import { SecurityService } from '../services/security.service';
import { UserRoleCode } from '../enums/user';
import { UserService } from '../services/user.service';
import { of } from 'rxjs';

describe('BootstrapComponent', () => {
  let component: BootstrapComponent;
  let fixture: ComponentFixture<BootstrapComponent>;
  let loadingService: jest.Mocked<LoadingService>;
  let cdr: jest.Mocked<ChangeDetectorRef>;
  let userService: jest.Mocked<UserService>;
  let dialogService: jest.Mocked<DialogService>;
  let securityService: jest.Mocked<SecurityService>;
  let router: Router;
  let activatedRoute: ActivatedRoute;
  let realtimeNotificationService: jest.Mocked<RealTimeNotifyService<unknown>>;

  beforeEach(async () => {
    const loadingServiceMock = {
      forceDisplayLoading: jest.fn(),
    };
    const cdrMock = {
      detectChanges: jest.fn(),
    };
    const userServiceMock = {
      getUser: jest.fn().mockReturnValue(of({ isAcceptedTermsConditions: true })),
      updateUserTermsAndConditionsAcceptance: jest.fn().mockReturnValue(of({})),
    };
    const dialogServiceMock = {
      openDialog$: { next: jest.fn() },
      closeDialog$: { next: jest.fn() },
      updateDialogData: jest.fn(),
    };
    const securityServiceMock = {
      logout: jest.fn().mockResolvedValue(undefined),
    };
    const realtimeNotificationServiceMock = {
      startConnection: jest.fn(),
      stopConnection: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [BootstrapComponent],
      providers: [
        provideRouter([]),
        {
          provide: LoadingService,
          useValue: loadingServiceMock,
        },
        {
          provide: ChangeDetectorRef,
          useValue: cdrMock,
        },
        {
          provide: UserService,
          useValue: userServiceMock,
        },
        {
          provide: DialogService,
          useValue: dialogServiceMock,
        },
        {
          provide: SecurityService,
          useValue: securityServiceMock,
        },
        {
          provide: RealTimeNotifyService,
          useValue: realtimeNotificationServiceMock,
        },
      ],
    }).compileComponents();
    router = TestBed.inject(Router);
    activatedRoute = TestBed.inject(ActivatedRoute);
    fixture = TestBed.createComponent(BootstrapComponent);
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    loadingService = TestBed.inject(LoadingService) as jest.Mocked<LoadingService>;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    cdr = TestBed.inject(ChangeDetectorRef) as jest.Mocked<ChangeDetectorRef>;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    userService = TestBed.inject(UserService) as jest.Mocked<UserService>;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    dialogService = TestBed.inject(DialogService) as jest.Mocked<DialogService>;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    securityService = TestBed.inject(SecurityService) as jest.Mocked<SecurityService>;
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    realtimeNotificationService = TestBed.inject(RealTimeNotifyService) as jest.Mocked<RealTimeNotifyService<unknown>>;
    component = fixture.componentInstance;
    // Initialize dialogConfig before calling handleScrollOnTermsConditions
    component.dialogConfig = {
      data: {
        actions: [
          {
            label: 'Cancel',
            skipPermission: true,
            onClick: jest.fn(),
          },
          {
            label: 'Agree',
            color: 'primary',
            disable: true,
            skipPermission: true,
            onClick: jest.fn(),
          },
        ],
      },
      disableClose: true,
    };

    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should set showSplash to false when getCurrentRoute returns true', fakeAsync(() => {
    // Arrange
    component.showSplash = true;

    jest.spyOn(component, 'subscribeEvent');
    jest.spyOn(component, 'getCurrentRoute').mockReturnValue(of(true));

    // Act
    component.ngOnInit();

    tick();

    // Assert
    expect(component.subscribeEvent).toHaveBeenCalledWith(EventUpdateAppInitStatus.CLASS_NAME);
    expect(component.getCurrentRoute).toHaveBeenCalled();
    expect(component.showSplash).toBeFalsy();
  }));

  it('should not change showSplash when getCurrentRoute returns false', fakeAsync(() => {
    // Arrange
    component.showSplash = true;

    jest.spyOn(component, 'subscribeEvent');
    jest.spyOn(component, 'getCurrentRoute').mockReturnValue(of(false));
    jest.spyOn(realtimeNotificationService, 'startConnection');

    // Act
    component.ngOnInit();

    tick();

    // Assert
    expect(realtimeNotificationService.startConnection).toHaveBeenCalled();
    expect(component.getCurrentRoute).toHaveBeenCalled();
    expect(component.showSplash).toBeTruthy();
  }));

  it('should not change showSplash when it is already false', fakeAsync(() => {
    // Arrange
    component.showSplash = false;

    jest.spyOn(component, 'subscribeEvent');
    jest.spyOn(component, 'getCurrentRoute').mockReturnValue(of(true));

    // Act
    component.ngOnInit();

    tick();

    // Assert
    expect(component.getCurrentRoute).toHaveBeenCalled();
    expect(component.showSplash).toBeFalsy();
  }));

  it('should initialize with default values', () => {
    expect(component.windowWidth).toBe(EMPTY);
    expect(component.showSplash).toBe(true);
  });

  it('should call forceDisplayLoading with false in constructor', () => {
    expect(loadingService.forceDisplayLoading).toHaveBeenCalledWith(false);
  });

  it('should subscribe to EventUpdateAppInitStatus.CLASS_NAME on init', () => {
    jest.spyOn(component, 'subscribeEvent');

    const navigationEnd = new NavigationEnd(1, '/home', '/home');

    jest.spyOn(router, 'events', 'get').mockReturnValue(of(navigationEnd));
    component.ingnoredRoutes = ['access-denied'];

    component.onInit();
    expect(component.subscribeEvent).toHaveBeenCalledWith(EventUpdateAppInitStatus.CLASS_NAME);
  });

  it('should call forceDisplayLoading with true and update windowWidth and showSplash on destroy', () => {
    component.onDestroy();

    expect(loadingService.forceDisplayLoading).toHaveBeenCalledWith(true);
    expect(component.windowWidth).toBe(`-${window.innerWidth}px`);
    expect(component.showSplash).toBe(false);
    expect(realtimeNotificationService.stopConnection).toHaveBeenCalled();
  });

  it('should handle EventUpdateAppInitStatus event correctly', fakeAsync(() => {
    const eventParam: EventParam = {
      objs: [new EventUpdateAppInitStatus(false)],
      className: '',
    };

    component.onEvent(eventParam);
    tick();

    expect(loadingService.forceDisplayLoading).toHaveBeenCalledWith(true);
    expect(component.windowWidth).toBe(`-${window.innerWidth}px`);
    expect(component.showSplash).toBe(false);
  }));

  it('should not handle event if obj is null', () => {
    const eventParam: EventParam = {
      objs: [],
      className: '',
    };

    component.onEvent(eventParam);

    expect(loadingService.forceDisplayLoading).toHaveBeenCalled();
    expect(cdr.detectChanges).not.toHaveBeenCalled();
  });

  it('should update terms and conditions acceptance', () => {
    component.updateTermsAndConditionsAcceptance();

    expect(userService.updateUserTermsAndConditionsAcceptance).toHaveBeenCalled();
    expect(dialogService.closeDialog$.next).toHaveBeenCalled();
    expect(component.windowWidth).toBe(`-${window.innerWidth}px`);
    expect(component.showSplash).toBe(false);
  });

  it('should log out user', async () => {
    await component.logOut();
    expect(securityService.logout).toHaveBeenCalled();
  });

  it('should handle user initialization and start session', fakeAsync(() => {
    const eventParam: EventParam = {
      objs: [new EventUpdateAppInitStatus(false)],
      className: '',
    };

    userService.getUser.mockReturnValue(
      of({
        userGuid: '',
        profilePicture: '',
        firstName: '',
        lastName: '',
        phoneNumber: '',
        email: '',
        paceLicense: '',
        language: {
          languageCode: '',
          languageName: '',
        },
        roleCode: UserRoleCode.SystemAdmin,
        roleName: '',
        access: [],
        isAcceptedTermsConditions: true,
        termsConditions: { termsConditionsContent: '' },
      }),
    );

    component.onEvent(eventParam);

    tick();

    expect(userService.getUser).toHaveBeenCalled();
    expect(loadingService.forceDisplayLoading).toHaveBeenCalledWith(true);
    expect(component.windowWidth).toBe(`-${window.innerWidth}px`);
    expect(component.showSplash).toBe(false);
  }));

  it('should handle user initialization and show terms and conditions', () => {
    jest.useFakeTimers();

    component.skipTermsAndConditions = false;

    const termsElementMock = {
      scrollTop: 0,
      offsetHeight: 100,
      scrollHeight: 200,
      onscroll: jest.fn(),
    };

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    component.termsConditionsContent = { nativeElement: termsElementMock } as ElementRef;

    const eventParam: EventParam = {
      objs: [new EventUpdateAppInitStatus(false)],
      className: '',
    };

    userService.getUser.mockReturnValue(
      of({
        userGuid: '',
        profilePicture: '',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '',
        email: '',
        paceLicense: '',
        language: {
          languageCode: 'EN',
          languageName: 'English',
        },
        roleCode: UserRoleCode.SystemAdmin,
        roleName: '',
        access: [],
        isAcceptedTermsConditions: false,
        termsConditions: { termsConditionsContent: 'Terms content' },
      }),
    );

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const handleScrollOnTermsConditionsSpy = jest.spyOn(
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      component as BootstrapComponent,
      'handleScrollOnTermsConditions',
    );

    component.onEvent(eventParam);

    expect(userService.getUser).toHaveBeenCalled();
    expect(component.termsAndConditions).toBe('Terms content');
    expect(dialogService.openDialog$.next).toHaveBeenCalled();

    // Fast-forward until all timers have been executed
    jest.runAllTimers();

    expect(handleScrollOnTermsConditionsSpy).toHaveBeenCalled();
  });

  it('should handle scroll on terms and conditions', () => {
    const termsElementMock = {
      scrollTop: 0,
      offsetHeight: 100,
      scrollHeight: 200,
      onscroll: jest.fn(),
    };

    component.termsConditionsContent = { nativeElement: termsElementMock };

    component.handleScrollOnTermsConditions();

    termsElementMock.scrollTop = 150;
    termsElementMock.onscroll();

    expect(dialogService.updateDialogData).toHaveBeenCalled();
  });

  it('should enable the agree button if terms element is null', () => {
    // Set the termsConditionsContent to undefined
    component.termsConditionsContent = undefined;

    // Call the method
    component.handleScrollOnTermsConditions();

    // Expectations
    expect(component.dialogConfig.data.actions[1].disable).toBe(false);
    expect(dialogService.updateDialogData).toHaveBeenCalledWith(component.dialogConfig.data);
  });

  it('should enable the agree button if content is smaller than container (no scrollbar)', () => {
    const termsElementMock = {
      scrollHeight: 100,
      clientHeight: 200,
    };

    // Set the terms element to our mock
    component.termsConditionsContent = { nativeElement: termsElementMock };

    // Call the method
    component.handleScrollOnTermsConditions();

    // Expectations
    expect(component.dialogConfig.data.actions[1].disable).toBe(false);
    expect(dialogService.updateDialogData).toHaveBeenCalledWith(component.dialogConfig.data);
  });

  it('should open terms and conditions dialog', () => {
    jest.useFakeTimers();

    const termsElementMock = {
      scrollTop: 0,
      offsetHeight: 100,
      scrollHeight: 200,
      onscroll: jest.fn(),
    };

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    component.termsConditionsContent = { nativeElement: termsElementMock } as ElementRef;

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const handleScrollOnTermsConditionsSpy = jest.spyOn(
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      component as BootstrapComponent,
      'handleScrollOnTermsConditions',
    );

    component['openTermsConditionsDialog']();

    expect(dialogService.openDialog$.next).toHaveBeenCalled();

    // Fast-forward until all timers have been executed
    jest.runAllTimers();
    expect(handleScrollOnTermsConditionsSpy).toHaveBeenCalled();
  });
});
