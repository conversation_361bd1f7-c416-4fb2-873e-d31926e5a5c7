import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { EMPTY } from 'src/app/constant';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[appNumbersOnly]',
})
export class NumbersOnlyDirective {
  @Input() allowDecimals = true;
  @Input() allowSign = false;
  @Input() allowNull = true;

  decimalSeparator = '.';
  thousandSeparator = '.';
  previousValue: string = EMPTY;

  // --------------------------------------
  //  Regular expressions
  integerUnsigned = '^[0-9]*$';
  integerSigned = '^-?[0-9]+$';
  decimalUnsigned = '^[0-9]+(.[0-9]+)?$';
  decimalSigned = '^-?[0-9]+(.[0-9]+)?$';

  /**
   * Class constructor
   * @param hostElement
   */

  private readonly el: NgControl;
  private readonly erf: ElementRef;

  constructor(
    private readonly ngControl: NgControl,
    elementRef: ElementRef,
  ) {
    this.el = ngControl;

    this.erf = elementRef;

    this.decimalSeparator = '.';

    this.thousandSeparator = ',';
  }

  /**
   * Event handler for host's change event
   */
  @HostListener('change', ['$event']) onInput() {
    this.validateValue(this.el.value);
  }

  /**
   * Event handler for host's paste event
   * @param e
   */
  @HostListener('paste', ['$event']) onPaste(e: ClipboardEvent) {
    if (this.erf.nativeElement.attributes.readonly) {
      e.preventDefault();

      return;
    }

    // get and validate data from clipboard
    const value = e.clipboardData?.getData('text/plain') ?? EMPTY;

    this.validateValueOnPaste(value);
  }

  /**
   * Event handler for host's keydown event
   * @param e
   */
  @HostListener('keydown', ['$event']) onKeyDown(e: KeyboardEvent) {
    // typescript can't infer the type of e.target
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const target = e.target as HTMLInputElement;

    if (!target) {
      return;
    }

    const cursorPosition: number = target.selectionStart ?? 0;
    const originalValue: string = target.value;
    const key: string = this.getName(e);
    const controlOrCommand = e.ctrlKey || e.metaKey;
    // allowed keys apart from numeric characters
    const allowedKeys = this.getAllowedKeys(originalValue, cursorPosition);

    // allow some non-numeric characters
    if (this.isKeyAllowed(key, controlOrCommand, allowedKeys)) {
      // let it happen, don't do anything
      return;
    }

    // save value before keydown event
    this.previousValue = originalValue;

    // allow number characters only
    const isNumber = new RegExp(this.integerUnsigned).test(key);

    if (isNumber) {
      return;
    } else {
      e.preventDefault();
    }
  }

  // /**
  //  * Test whether value is a valid number or not
  //  * @param value
  //  */
  validateValue(value: string): void {
    if (this.allowNull && (!value || value === EMPTY)) {
      if (this.el.control) {
        this.el.control.patchValue(value);
      }

      return;
    }

    value = this.removeThousandSeparator(value, this.thousandSeparator);
    value = this.fixValidateDecimalPosition(value);

    // choose the appropriate regular expression
    const regex: string = this.getValidateRegexPattern();

    // test number with regular expression, when
    // number is invalid, replace it with a zero

    const valid: boolean = new RegExp(regex).test(value);

    if (this.el.control) {
      this.el.control.patchValue(valid ? value : EMPTY);
    }
  }

  /**
   * Test whether value is a valid number or not
   * @param value
   */
  validateValueOnPaste(value: string): void {
    if (this.allowNull && (!value || value === EMPTY)) {
      if (this.el.control) {
        this.el.control.patchValue(value);
      }

      return;
    }

    const res = this.isNumberValue(value, this.allowDecimals, this.allowSign);

    if (this.el.control && !res.valid) {
      this.el.control.patchValue(EMPTY);
    }
  }

  /**
   * Get key's name
   * @param e
   */
  getName(e: KeyboardEvent): string {
    if (e.key) {
      return e.key;
    }

    return EMPTY;
  }

  removeThousandSeparator(value: string, thousandSeparator: string): string {
    if (/[\u202F\u00A0]/.test(thousandSeparator)) {
      // thousand group in french
      return value.replace(new RegExp('\\' + 's', 'g'), EMPTY);
    } else {
      value = value.replace(new RegExp('\\' + 's', 'g'), EMPTY);

      return value.replace(new RegExp('\\' + thousandSeparator, 'g'), EMPTY);
    }
  }

  isNumberValue(tValue: string, allowDecimals = true, allowSign = false): { valid: boolean; value: string } {
    const integerUnsigned = '^[0-9]*$';
    const integerSigned = '^-?[0-9]+$';
    const decimalUnsigned = '^[0-9]+(.[0-9]+)?$';
    const decimalSigned = '^-?[0-9]+(.[0-9]+)?$';
    const decimalSeparator = '.';
    let regex: string = decimalUnsigned; // allowDecimals && !allowSign

    if (!allowDecimals && !allowSign) {
      regex = integerUnsigned;
    } else if (!allowDecimals && allowSign) {
      regex = integerSigned;
    } else if (allowDecimals && allowSign) {
      regex = decimalSigned;
    }

    // when a numbers begins with a decimal separator,
    // fix it adding a zero in the beginning
    const firstCharacter = tValue.charAt(0);

    if (firstCharacter == decimalSeparator) {
      tValue = 0 + tValue;
    }

    // when a numbers ends with a decimal separator,
    // fix it adding a zero in the end
    const lastCharacter = tValue.charAt(tValue.length - 1);

    if (lastCharacter == decimalSeparator) {
      tValue = tValue + 0;
    }

    // test number with regular expression, when
    // number is invalid, replace it with a zero
    const valid: boolean = new RegExp(regex).test(tValue);

    return {
      valid,
      value: valid ? tValue : EMPTY,
    };
  }

  private getAllowedKeys(originalValue: string, cursorPosition: number): string[] {
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Escape', 'Tab', 'Enter'];
    const signExists = originalValue.includes('-');
    const separatorExists = originalValue.includes(this.decimalSeparator);
    const separatorIsCloseToSign = signExists && cursorPosition <= 1;

    if (this.allowDecimals && !separatorIsCloseToSign && !separatorExists) {
      allowedKeys.push(this.decimalSeparator === '.' ? '.' : ',');
    }

    const firstCharacterIsSeparator = !originalValue.startsWith(this.decimalSeparator);

    if (this.allowSign && !signExists && firstCharacterIsSeparator && cursorPosition === 0) {
      allowedKeys.push('-');
    }

    return allowedKeys;
  }

  private isKeyAllowed(key: string, controlOrCommand: boolean, allowedKeys: string[]): boolean {
    return (
      allowedKeys.indexOf(key) != -1 ||
      // Allow: Ctrl+A and Command+A
      (key == 'a' && controlOrCommand) ||
      // Allow: Ctrl+C and Command+C
      (key == 'c' && controlOrCommand) ||
      // Allow: Ctrl+V and Command+V
      (key == 'v' && controlOrCommand) ||
      // Allow: Ctrl+X and Command+X
      (key == 'x' && controlOrCommand)
    );
  }

  private getValidateRegexPattern(): string {
    if (this.allowDecimals) {
      return this.allowSign ? this.decimalSigned : this.decimalUnsigned;
    }

    return this.allowSign ? this.integerSigned : this.integerUnsigned;
  }

  private fixValidateDecimalPosition(value: string): string {
    // when a numbers begins with a decimal separator,
    // fix it adding a zero in the beginning
    if (value.startsWith(this.decimalSeparator)) {
      value = '0' + value;
    }

    // when a numbers ends with a decimal separator,
    // fix it adding a zero in the end
    if (value.endsWith(this.decimalSeparator)) {
      value += '0';
    }

    return value;
  }
}
