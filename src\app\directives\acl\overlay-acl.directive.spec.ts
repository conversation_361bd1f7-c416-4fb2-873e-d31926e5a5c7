import { ElementRef, Renderer2 } from '@angular/core';

import { AsyncSubject } from 'rxjs';

import { OverlayAclDirective } from './overlay-acl.directive';

describe('OverlayAclDirective', () => {
  let directive: OverlayAclDirective;
  let mockElementRef: ElementRef;
  let mockRenderer2: Renderer2;
  const mockAclWrapperDirective = {
    parentCode: '',
    permissions: [],
    packagePermissions: {
      pageCode: '123',
      functions: [],
    },
    destroyWrapper$: new AsyncSubject(),
    pageCode: '',
    getFunctionPermission: jest.fn().mockReturnValue({ isAllowed: true }),
  };

  const mockStorageService = {
    storageChange$: {
      subscribe: jest.fn().mockImplementation((callback) => {
        callback({
          storageArea: 'sessionStorage',
          key: 'defaultSelectionSite',
        });

        return { unsubscribe: jest.fn() };
      }),
    },
  };

  beforeEach(() => {
    mockElementRef = {
      nativeElement: document.createElement('div'),
    };

    mockRenderer2 = {
      createElement: jest.fn().mockImplementation((tag) => document.createElement(tag)),
      setStyle: jest.fn(),
      appendChild: jest.fn(),
      removeChild: jest.fn(),
      createText: jest.fn().mockImplementation((text) => document.createTextNode(text)),
      data: {},
      destroy: jest.fn(),
      createComment: jest.fn(),
      destroyNode: jest.fn(),
      insertBefore: jest.fn(),
      selectRootElement: jest.fn(),
      parentNode: jest.fn(),
      nextSibling: jest.fn(),
      setAttribute: jest.fn(),
      removeAttribute: jest.fn(),
      addClass: jest.fn(),
      removeClass: jest.fn(),
      removeStyle: jest.fn(),
      setProperty: jest.fn(),
      setValue: jest.fn(),
      listen: jest.fn(),
    };

    directive = new OverlayAclDirective(
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
      mockAclWrapperDirective as any,
      mockRenderer2,
      mockElementRef,
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
      mockStorageService as any,
    );
  });

  it('should create an instance', () => {
    expect(directive).toBeTruthy();
  });

  it('should create overlay when not allowed', () => {
    jest.spyOn(mockAclWrapperDirective, 'getFunctionPermission').mockReturnValue({ isAllowed: false });

    directive.ngOnInit();

    expect(mockRenderer2.createElement).toHaveBeenCalledWith('div');
    expect(mockRenderer2.setStyle).toHaveBeenCalledTimes(13); // Called 13 times for each style set
    expect(mockRenderer2.appendChild).toHaveBeenCalledTimes(3); // Called for overlay, text, and textContent
  });

  it('should remove overlay when destroyed', () => {
    jest.spyOn(mockAclWrapperDirective, 'getFunctionPermission').mockReturnValue({ isAllowed: false });

    directive.ngOnInit();

    directive.ngOnDestroy();

    expect(mockRenderer2.removeChild).toHaveBeenCalled();
  });

  it('should not create overlay when allowed', () => {
    jest.spyOn(mockAclWrapperDirective, 'getFunctionPermission').mockReturnValue({ isAllowed: true });

    directive.ngOnInit();

    expect(mockRenderer2.createElement).not.toHaveBeenCalled();
  });
});
