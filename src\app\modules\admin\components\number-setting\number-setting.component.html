<div appAclWrapper>
  <div class="flex items-center justify-between" appAcl #acl="AppAcl">
    <div class="grid grid-flow-col gap-x-8">
      @for (input of inputValues; track $index) {
        <div>
          <p class="text-[var(--grey-200)] text-lg font-bold">{{ input.label }}</p>
          @if (isEditMode) {
            <ds-input
              #inputModels
              [type]="input.type === 'number' ? 'number' : 'text'"
              [label]="input.label"
              [(ngModel)]="input.value"
              [dsError]="input.type === 'number' ? numberErrorMessages : percentErrorMessages"
              appRuleConfigGridInputValidator
              [constraints]="input.type === 'number' ? numberInputConstraints : percentInputConstraints"
              [ruleCode]="ruleCode"
              [inputValues]="inputValues"
              (ngModelChange)="markAllInputForCheck()"
              appNumberInput
              [decimals]="input.type === 'percent'"
              [required]="true"
              [min]="input.min"
              [max]="input.max"
            />
          } @else {
            <p class="text-[var(--black-200)] text-lg font-bold">{{ input.value }}</p>
          }
        </div>
      }

      @if ((acl.permission$ | async)?.write) {
        <div class="flex flex-col justify-end py-8">
          <button mat-stroked-button color="primary" (click)="onOpenAuditTrail()">
            <span i18n>Audit Trail</span>
          </button>
        </div>
      }
    </div>

    @if (isEditMode) {
      <div class="ml-auto">
        <button mat-raised-button color="secondary" (click)="onCancel()">
          <span i18n="@@app-app_button-cancel">Cancel</span>
        </button>
        <button mat-raised-button color="primary" (click)="onSave()" [disabled]="!isAllInputValid()">
          <span i18n="@@app-app_button-save">Save</span>
        </button>
      </div>
    } @else {
      @if ((acl.permission$ | async)?.write) {
        <div class="ml-auto">
          <button mat-raised-button color="secondary" (click)="toggleEditMode(true)">
            <mat-icon svgIcon="edit24"></mat-icon>
            <span i18n="@@app-app_button-edit">Edit</span>
          </button>
        </div>
      }
    }
  </div>
</div>

<ng-template #saveConfirmDialogContent>
  <div class="text-base">
    <span i18n="@@app-app_button-confirm_save">Are you sure you want to save these changes?</span>
  </div>
  @if (this.hasRecentRuns) {
    <div class="text-base">
      <span i18n>The changes to the {{ inputValues[0].label }} will also apply to other Rules.</span>
    </div>
  }
</ng-template>

<app-number-setting-audit-trail
  [ruleCode]="ruleCode"
  [dialogParams]="openAuditTrailDialog$ | async"
></app-number-setting-audit-trail>
