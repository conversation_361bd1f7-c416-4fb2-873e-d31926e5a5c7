import { Event, NavigationEnd, Router } from '@angular/router';
import { Observable, Subject, map, switchMap, tap } from 'rxjs';
import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

import {
  AnalyzerResponse,
  AnalyzerStatusPriority,
  GroupedAnalyzers,
} from '../modules/analyzer-status/interfaces/analyzer';
import { EventAlertPreferencesChanged, EventDisableNotify, EventSiteChange } from '../models/event-params';
import { ReceiveMessage, UIEventType } from '../interfaces/receiveMessage';
import { ANALYZER_STATUS } from '../modules/analyzer-status/interfaces/enums';
import { AnalyzerAlertDetailsComponent } from './analyzer-alert-details/analyzer-alert-details.component';
import { AnalyzerService } from '../modules/analyzer-status/services/analyzer.service';
import { AnalyzerTroubleshootingComponent }
  from '../modules/analyzer-status/components/analyzer-troubleshooting/analyzer-troubleshooting.component';
import { DialogType } from '../enums';
import { EMPTY } from '../constant';
import { EventAbsComponent } from '../services/event-communicate/component-evt-base';
import { EventParam } from '../models/event-items';
import { MessageManagementService } from '../services/message-management.service';
import { NotificationDisplaySettingsParams } from '../modules/settings/interfaces/site-setting';
import { SiteSettingsService } from '../services/site.service';
import { UserStoreService } from '../services/user-store.service';
import { Utils } from '../helpers/UtilFunctions';

export interface AlertAnalyzerStatusContent {
  analyzerModel: string;

  analyzerSerial: string;

  statusCode: number;

  statusCodeBf?: number;

  isStart: boolean;

  isBfMode: boolean;

  isBfWarning: boolean;

  isRequiredWbMode: boolean;

  enableTroubleshootingTime: string;

  approachingTime: string;

  approachingTimeBf?: string;

  qcDue: string;

  qcDueBf?: string;

  isIotConnected?: boolean;

  isCheckXbarmData: boolean;

  isWarningCustomerLimit?: boolean;

  isWarningCountryLimit?: boolean;

  isWarningAssayLimit?: boolean;

  isWarningMultiRulesLimit?: boolean;

  isWarningCustomerLimitBf?: boolean;

  isWarningCountryLimitBf?: boolean;

  isWarningAssayLimitBf?: boolean;

  isWarningMultiRulesLimitBf?: boolean;

  hasBfRun?: boolean;
}

interface AnalyzerStatusHTMLProps {
  color: string;

  numberOfAnalyzers: number;
}

export interface AnalyzerAlertStatusDialog {
  siteCode: string;

  alertPreferences?: NotificationDisplaySettingsParams;
}

@Component({
  selector: 'app-analyzer-alert',
  standalone: true,
  templateUrl: './analyzer-alert.component.html',
  imports: [
    MatCardModule,
    MatIconModule,
    AnalyzerAlertDetailsComponent,
    CommonModule,
    MatButtonModule,
    AnalyzerTroubleshootingComponent,
  ],
  providers: [AnalyzerService],
})
export class AnalyzerAlertComponent extends EventAbsComponent {
  currentSiteCode!: string;

  mapAlertAnalyzerStatuses!: AnalyzerStatusHTMLProps[];

  canNotify = false;

  isDisplayingAlertBanner = false;

  alertAnalyzerStatusContent: ReceiveMessage<AlertAnalyzerStatusContent>[] = [];

  openDialog$ = new Subject<{ type: number; data: AnalyzerAlertStatusDialog }>();

  alertPreferences?: NotificationDisplaySettingsParams;

  isServiceL2 = false;

  currentPageUrl!: string;

  constructor(
    private readonly analyzerService: AnalyzerService,
    private readonly msgManagementService: MessageManagementService<AlertAnalyzerStatusContent>,
    private readonly siteSettingsService: SiteSettingsService,
    private readonly userStoreService: UserStoreService,
    private readonly router: Router,
  ) {
    super();
  }

  onInit(): void {
    this.subscribeEvent(
      EventDisableNotify.CLASS_NAME,
      EventSiteChange.CLASS_NAME,
      EventAlertPreferencesChanged.CLASS_NAME,
    );

    this.isServiceL2 = Utils.isServiceLevel2(this.userStoreService);

    const currentSiteCode = sessionStorage.getItem('defaultSelectionSite');

    this.currentSiteCode = currentSiteCode ?? EMPTY;

    this.router.events.subscribe((event) => this.handleRouterEvent(event));

    this.checkCanNotifyAnalyzerStatus();
  }

  onDestroy(): void {
    this.subscription.unsubscribe();
  }

  checkCanNotifyAnalyzerStatus(): void {
    this.currentPageUrl = this.router.url;

    if (this.currentPageUrl === '/analyzer-status/summary') {
      this.canNotify = false;

      return;
    }

    this.canNotify = true;

    if (!this.isServiceL2) {
      this.loadAlertPreferencesAndSetInitialAlert(this.currentSiteCode).subscribe((alertPreferences) => {
        this.alertPreferences = alertPreferences;
      });
    }
  }

  onEvent(params: EventParam): void {
    params.objs.forEach(async (obj) => {
      if (!obj) {
        return;
      }

      if (obj instanceof EventDisableNotify) {
        this.canNotify = obj.canNotify();
        this.isShowingAlertBanner();
      }

      if (obj instanceof EventSiteChange) {
        this.isServiceL2 = Utils.isServiceLevel2(this.userStoreService);

        this.currentSiteCode = obj.siteCode;

        if (this.currentPageUrl === '/analyzer-status/summary') {
          this.canNotify = false;
          this.isShowingAlertBanner();

          return;
        }

        if (!this.isServiceL2) {
          this.loadAlertPreferencesAndSetInitialAlert(this.currentSiteCode).subscribe((alertPreferences) => {
            this.alertPreferences = alertPreferences;
          });
        }
      }

      if (obj instanceof EventAlertPreferencesChanged) {
        this.alertPreferences = obj.alertPreferences;

        this.mapAlertAnalyzerStatuses = this.mapAnalyzerStatusWithHTMLProps(this.alertAnalyzerStatusContent);

        this.isShowingAlertBanner();
      }
    });
  }

  handleRouterEvent(event: Event) {
    if (!(event instanceof NavigationEnd)) {
      return;
    }

    if (this.currentPageUrl !== event.urlAfterRedirects) {
      this.currentPageUrl = event.urlAfterRedirects;
    }

    if (this.currentPageUrl === '/analyzer-status/summary') {
      this.canNotify = false;
      this.isShowingAlertBanner();

      return;
    }

    if (!this.isServiceL2) {
      this.loadAlertPreferencesAndSetInitialAlert(this.currentSiteCode).subscribe((alertPreferences) => {
        this.alertPreferences = alertPreferences;
      });
    }
  }

  getAlertPreferences(): Observable<NotificationDisplaySettingsParams> {
    return this.siteSettingsService
      .getSiteSettings(this.currentSiteCode)
      .pipe(map((siteSettings) => siteSettings.alertPreferences));
  }

  loadAlertPreferencesAndSetInitialAlert(siteCode: string): Observable<NotificationDisplaySettingsParams> {
    return this.getAlertPreferences().pipe(
      switchMap((alertPreferences) =>
        this.setInitialAnalyzerAlert(siteCode, alertPreferences).pipe(map(() => alertPreferences)),
      ),
    );
  }

  setInitialAnalyzerAlert(
    siteCode: string,
    alertPreferences: NotificationDisplaySettingsParams,
  ): Observable<ReceiveMessage<AlertAnalyzerStatusContent>[]> {
    return this.analyzerService.getAnalyzers(siteCode).pipe(
      map((siteData) => {
        const sites: GroupedAnalyzers = siteData;
        const childrenAnalyzers = sites.parentAnalyzers.flatMap((parent) => parent.analyzers ?? []);
        const analyzersResponse = [...sites.aloneAnalyzers, ...childrenAnalyzers];

        return this.mapMsgQueue(analyzersResponse);
      }),
      tap((msgQueue) => {
        this.msgManagementService.setInitMsgQueue(msgQueue);

        this.alertAnalyzerStatusContent = this.msgManagementService.getMsgQueue();
        this.mapAlertAnalyzerStatuses = this.mapAnalyzerStatusWithHTMLProps(
          this.alertAnalyzerStatusContent,
          alertPreferences,
        );

        this.isShowingAlertBanner();

        this.onReceivedNewAlert();
      }),
    );
  }

  onReceivedNewAlert(): void {
    this.msgManagementService.onReceiveMsg
      .pipe(map((pushMsg) => pushMsg.queueMsg.filter((msg) => msg.siteCode === this.currentSiteCode)))
      .subscribe((filteredMsgs) => {
        this.alertAnalyzerStatusContent = [...filteredMsgs];

        this.mapAlertAnalyzerStatuses = this.mapAnalyzerStatusWithHTMLProps(
          this.alertAnalyzerStatusContent,
          this.alertPreferences,
        );
      });
  }

  mapMsgQueue(analyzersResponse: AnalyzerResponse[]) {
    return analyzersResponse.map((analyzer) => {
      const msg: ReceiveMessage<AlertAnalyzerStatusContent> = {
        uiEventType: UIEventType.ANALYZER_STATUS,
        siteCode: this.currentSiteCode,
        eventId: EMPTY,
        sentTime: EMPTY,
        content: {
          ...analyzer.analyzerStatus,
          analyzerModel: analyzer.modelCode,
          analyzerSerial: analyzer.serialNumber,
          statusCode: analyzer.analyzerStatus.status,
          statusCodeBf: analyzer.analyzerStatus.statusBf,
          isIotConnected: analyzer.analyzerStatus.isIotConnected,
          isStart: false,
          isBfMode: false,
          isBfWarning: analyzer.analyzerStatus.isBfWarning,
          isRequiredWbMode: false,
          enableTroubleshootingTime: analyzer.analyzerStatus.enableTroubleshootingTime ?? EMPTY,
          approachingTime: analyzer.analyzerStatus.approachingTime,
          approachingTimeBf: analyzer.analyzerStatus.approachingTimeBf,
          qcDue: analyzer.analyzerStatus.qcDue,
          qcDueBf: analyzer.analyzerStatus.qcDueBf,
        },
      };

      return msg;
    });
  }

  isShowingAlertBanner() {
    this.isDisplayingAlertBanner =
      this.canNotify && this.mapAlertAnalyzerStatuses?.filter((item) => item.numberOfAnalyzers > 0).length > 0;
  }

  mapAnalyzerStatusWithHTMLProps(
    alertAnalyzers: ReceiveMessage<AlertAnalyzerStatusContent>[],
    alertPreferences?: NotificationDisplaySettingsParams,
  ): AnalyzerStatusHTMLProps[] {
    if (!alertAnalyzers.length || !alertPreferences) {
      return [];
    }

    const greenStatus: AnalyzerStatusHTMLProps = {
      color: 'green',
      numberOfAnalyzers: 0,
    };

    const yellowStatus: AnalyzerStatusHTMLProps = {
      color: 'yellow',
      numberOfAnalyzers: 0,
    };

    const redStatus: AnalyzerStatusHTMLProps = {
      color: 'red',
      numberOfAnalyzers: 0,
    };

    const greyStatus: AnalyzerStatusHTMLProps = {
      color: 'grey',
      numberOfAnalyzers: 0,
    };

    const wbStatus: AnalyzerStatusPriority = 'WB';

    alertAnalyzers.forEach((analyzer) => {
      const analyzerStatusPriorityParams = {
        status: analyzer.content.isIotConnected === false ? ANALYZER_STATUS.Offline : analyzer.content.statusCode,
        qcDue: analyzer.content?.qcDue,
        approachingTime: analyzer.content?.approachingTime,
        isBfWarning: analyzer.content?.isBfWarning,
        statusBf: analyzer.content?.statusCodeBf,
        qcDueBf: analyzer.content?.qcDueBf,
        approachingTimeBf: analyzer.content?.approachingTimeBf,
        isIotConnected: analyzer.content.isIotConnected,
        hasBfRun: analyzer.content.hasBfRun,
      };

      const priority = this.analyzerService.defineAnalyzerStatusPriority(analyzerStatusPriorityParams);

      const statusCode =
        priority === wbStatus ? analyzerStatusPriorityParams?.status : analyzerStatusPriorityParams?.statusBf;
      const { qcDueStatus } = this.analyzerService.mapQcDueStatus(
        analyzer.content?.qcDue,
        analyzer.content?.approachingTime,
        analyzer.content?.qcDueBf,
        analyzer.content?.approachingTimeBf,
        priority,
        statusCode === ANALYZER_STATUS.Offline,
      );

      switch (statusCode) {
        case ANALYZER_STATUS.RunQC:

        case ANALYZER_STATUS.Troubleshoot:

        case ANALYZER_STATUS.RepeatQCLevel:
          yellowStatus.numberOfAnalyzers++;

          break;

        case ANALYZER_STATUS.RunNextLevel:

        case ANALYZER_STATUS.ReadyForSamples:
          if (qcDueStatus === 'QcPastDue') {
            yellowStatus.numberOfAnalyzers++;
          } else {
            greenStatus.numberOfAnalyzers++;
          }

          break;

        case ANALYZER_STATUS.ServiceNotified:
          redStatus.numberOfAnalyzers++;

          break;

        case ANALYZER_STATUS.Offline:
          greyStatus.numberOfAnalyzers++;

          break;

        default:
          break;
      }
    });

    const allowAnalyzerStatusColor = this.getAllowAnalyzerStatusColor(alertPreferences);

    let analyzerStatusProps: AnalyzerStatusHTMLProps[] = [greenStatus, yellowStatus, redStatus, greyStatus];

    analyzerStatusProps = analyzerStatusProps.filter((status) => allowAnalyzerStatusColor.includes(status.color));

    return analyzerStatusProps;
  }

  openAnalyzerAlertDialog() {
    this.openDialog$.next({
      type: DialogType.ANALYZER_ALERT,
      data: {
        siteCode: this.currentSiteCode,
        alertPreferences: this.alertPreferences,
      },
    });
  }

  getAllowAnalyzerStatusColor(preferences: NotificationDisplaySettingsParams): string[] {
    const colorMapping: Record<string, string> = {
      isGreen: 'green',
      isRed: 'red',
      isYellow: 'yellow',
      isGray: 'grey',
    };

    return Object.entries(preferences)
      .filter(([_, value]) => value)
      .map(([key, _]) => colorMapping[key]);
  }

  analyzerAlertStatusLabel(statusColor: string) {
    switch (statusColor) {
      case 'green':
        return $localize`:@@app-app_status-analyzer_alert-status-green:Green - Ready for Samples`;

      case 'yellow':
        return $localize`:@@app-app_status-analyzer_alert-status-yellow:Yellow - Action Required`;

      case 'red':
        return $localize`:@@app-app_status-analyzer_alert-status-red:Red - Issue Identified`;

      case 'grey':
        return $localize`:@@app-app_status-analyzer_alert-status-gray:Gray - Offline`;

      default:
        return '';
    }
  }
}
