import { Directive, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Subscription, delay, interval } from 'rxjs';
import { ONE_SECOND_IN_MILLISECONDS } from '../constant';

@Directive({
  selector: '[appCountdown]',
  standalone: true,
})
export class CountdownDirective implements OnInit, OnDestroy {
  @Input('appCountdown') milliseconds = 0;
  @Output() countDownFinished = new EventEmitter<number>();

  private subscription!: Subscription;

  ngOnInit() {
    this.subscription = interval(ONE_SECOND_IN_MILLISECONDS)
      .pipe(delay(ONE_SECOND_IN_MILLISECONDS))
      .subscribe(() => {
        this.calculateTimeRemaining();
      });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  calculateTimeRemaining() {
    if (this.milliseconds <= 0) {
      this.subscription.unsubscribe();

      return;
    }

    this.milliseconds = this.milliseconds - ONE_SECOND_IN_MILLISECONDS;

    this.countDownFinished.emit(this.milliseconds);
  }
}
