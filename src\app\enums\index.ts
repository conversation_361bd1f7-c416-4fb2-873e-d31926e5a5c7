export enum LOT_LEVEL {
  LEVEL_0 = 0,
  LEVEL_1 = 1,
  LEVEL_2 = 2,
  LEVEL_3 = 3
}

export enum ANALYZER_EVENT_TYPE {
  REAGENT_EVENT,
  CALIBRATION_EVENT,
  SERVICE_EVENT
}

export enum SORT_DIRECTION_TEXT {
  DESC = 'desc',
  ASC = 'asc'
}

export enum SORT_DIRECTION {
  SORT_DESCENDING = -1,
  SORT_ASCENDING = 1
}

export enum SORT_ORDER {
  DESC = 'desc',
  ASC = 'asc'
}

export enum POINT_MARKER {
  CIRCLE = 'circle',
  SQUARE = 'square',
  TRIANGLE = 'triangle'
}

export enum DialogType {
  CHART_COMMENT,
  TABLE_COMMENT,
  ANALYZER_ALERT,
  REAGENT_CHANGES,
  SERVICE_HISTORY
}

export enum ParametersType {
  SUPPORTED_PARAMETERS,
  XBARM_PARAMETERS
}

export enum TABLE_ACTION {
  CREATE,
  VIEW,
  UPDATE,
  DELETE,
  STATUS
}
