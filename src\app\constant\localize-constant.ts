export const TABLE_HEADER_LOCALIZE = {
  SITENAME: $localize`:app-app_grid-header_label:Site Name`,
  MODEL: $localize`:app-app_grid-header_label:Model`,
  NICKNAME: $localize`:app-app_grid-header_label:Nickname`,
  SERIALID: $localize`:app-app_grid-header_label:Serial ID`,
  SELECTION_CHECKBOX: $localize`:app-app_grid-header_label:Select`,
  PRIMARY_CHECKBOX: $localize`:app-app_grid-header_label:Primary`,
  SYSMEX_ITEM: $localize`:app-app_grid-header_label:Item`,
  SYSMEX_UNIT: $localize`:app-app_grid-header_label:Unit`,
  SYSMEX_MEAN: $localize`:@@app-app_grid_sysmex_mean_header:Mean`,
  SYSMEX_UPPER: $localize`:app-app_grid-header_label:Upper`,
  SYSMEX_LOWER: $localize`:app-app_grid-header_label:Lower`,
  ANALYZER_NAME: $localize`:app-app_grid-header_label:Analyzer Name`,
  STATUS: $localize`:app-app_grid-header_label:Status`,
  STATUS_WB: $localize`:app-app_grid-header_label:Whole Blood`,
  STATUS_BF: $localize`:app-app_grid-header_label:Body Fluid`,
  QC_DUE_WB: $localize`:app-app_grid-header_label:Whole Blood QC Due`,
  QC_DUE_BF: $localize`:app-app_grid-header_label:Body Fluid QC Due`,
  ACTION: $localize`:app-app_grid-header_label:Action`,
};

export const DROPDOWN_HEADER_LOCALIZE = {
  SELECT_SITE: $localize`:app-app_dropdown-title:Select site`,
  SITE: $localize`:app-app_dropdown-title:Site`,
  DISCIPLINE: $localize`:app-app_dropdown-title:Discipline`,
  MODELGROUP: $localize`:app-app_dropdown-title:Model Group`,
  MODEL: $localize`:app-app_dropdown-title:Model`,
};

export const INPUT_HEADER_LOCALIZE = {
  NAME_SERIAL_NUMBER: $localize`:app-app_label:Name or serial number`,
};

export const DROPDOWN_VALUE_LOCALIZE = {
  NONE: $localize`:app-app_dropdown-value:None`,
  HEMATOLOGY: $localize`:app-app_dropdown-value:Hematology`,
};

export const LIMIT_TYPE_OPTION: Record<string, string> = {
  assay_limit: $localize`:app-app_dropdown-value:Assay Limit`,
  customer_limit: $localize`:app-app_dropdown-value:Customer Limit and Assay Limit`,
  country_limit: $localize`:app-app_dropdown-value:Country Specific Limit and Assay Limit`,
  multi_rules: $localize`:app-app_dropdown-value:Multi Rules Limit and Assay Limit`,
};

export const ACTIVITY_TYPE_OPTION: Record<string, string> = {
  'Performed Service Reset': $localize`:app-app_dropdown-value:Performed Service Reset`,
  Troubleshooting: $localize`:app-app_dropdown-value:Troubleshooting`,
  'Started Service Mode': $localize`:app-app_dropdown-value:Started Service Mode`,
  'Ended Service Mode': $localize`:app-app_dropdown-value:Ended Service Mode`,
  'Performed Logic Reset': $localize`:app-app_dropdown-value:Performed Logic Reset`,
  'Commented on QC Value': $localize`:app-app_dropdown-value:Commented on QC Value`,
  'Managed/Unmanaged QC Value': $localize`:app-app_dropdown-value:Managed/Unmanaged QC Value`,
  'Edited Site Preferences': $localize`:app-app_dropdown-value:Edited Site Preferences`,
  'Edited Customer Limits': $localize`:app-app_dropdown-value:Edited Customer Limits`,
  'Edited XbarM Customer Limits': $localize`:app-app_dropdown-value:Edited XbarM Customer Limits`,
  'Edited Multi Rules Configuration': $localize`:app-app_dropdown-value:Edited Multi Rules Configuration`,
};

export const RULE_STANDARD_SETTING_OPTION: Record<string, string> = {
  rule1: $localize`:app-app_dropdown-value:Rule 1 - Single Run Failure`,
  rule2: $localize`:app-app_dropdown-value:Rule 2 - Check Accuracy All Runs`,
  rule3: $localize`:app-app_dropdown-value:Rule 3 - Check Precision All Runs`,
  rule4: $localize`:app-app_dropdown-value:Rule 4 - Check Accuracy Recent Runs`,
  rule5: $localize`:app-app_dropdown-value:Rule 5 - Check Accuracy Recent Runs`,
  rule6: $localize`:app-app_dropdown-value:Rule 6 - Check Consecutive Failures`,
  rule7: $localize`:app-app_dropdown-value:Rule 7 - Check Multiple Failures Recent Runs`,
  rule8: $localize`:app-app_dropdown-value:Rule 8 - Check Multiple Failures Recent Days`,
  rule9: $localize`:app-app_dropdown-value:Rule 9 - Check Linearity Recent Runs`,
  rule10: $localize`:app-app_dropdown-value:Rule 10 - COC Limits`,
  rule11: $localize`:app-app_dropdown-value:Rule 11 of 13 Sysmex rules for recent runs precision validation`,
  rule12: $localize`:app-app_dropdown-value:Rule 12 - RBC Balance Check Lot to Date`,
  rule13: $localize`:app-app_dropdown-value:Rule 13 - RBC Balance Check Recent`,
};

export const AFFILIATED_PEER_GROUP_TYPE: Record<string, string> = {
  'Local group': $localize`:app-app_dropdown-value:Local group`,
};

export const AFFILIATED_PEER_GROUP: Record<string, string> = {
  'Entire peer group': $localize`:app-app_dropdown-value:Entire peer group`,
};

export const CONSTANT_SETTING_PARAMETER_TYPE: Record<string, string> = {
  Counted: $localize`:app-app_constant-setting-value:Counted`,
  Calculated: $localize`:app-app_constant-setting-value:Calculated`,
  DCP: $localize`:app-app_constant-setting-value:DCP`,
};
