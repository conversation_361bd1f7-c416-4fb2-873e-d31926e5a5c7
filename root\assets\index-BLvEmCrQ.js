(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))i(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const r of s.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&i(r)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function i(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();/**
* @vue/shared v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**/function mi(e,t){const n=new Set(e.split(","));return t?i=>n.has(i.toLowerCase()):i=>n.has(i)}const ee={},vt=[],_e=()=>{},dr=()=>!1,bn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),gi=e=>e.startsWith("onUpdate:"),re=Object.assign,yi=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},hr=Object.prototype.hasOwnProperty,V=(e,t)=>hr.call(e,t),K=Array.isArray,bt=e=>kn(e)==="[object Map]",Zo=e=>kn(e)==="[object Set]",U=e=>typeof e=="function",se=e=>typeof e=="string",Pt=e=>typeof e=="symbol",te=e=>e!==null&&typeof e=="object",Bo=e=>(te(e)||U(e))&&U(e.then)&&U(e.catch),Go=Object.prototype.toString,kn=e=>Go.call(e),pr=e=>kn(e).slice(8,-1),Xo=e=>kn(e)==="[object Object]",_i=e=>se(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Kt=mi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),xn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},mr=/-(\w)/g,St=xn(e=>e.replace(mr,(t,n)=>n?n.toUpperCase():"")),gr=/\B([A-Z])/g,Rt=xn(e=>e.replace(gr,"-$1").toLowerCase()),Jo=xn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Nn=xn(e=>e?`on${Jo(e)}`:""),Qe=(e,t)=>!Object.is(e,t),zn=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},gn=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},yr=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Bi;const qo=()=>Bi||(Bi=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function wi(e){if(K(e)){const t={};for(let n=0;n<e.length;n++){const i=e[n],o=se(i)?br(i):wi(i);if(o)for(const s in o)t[s]=o[s]}return t}else if(se(e)||te(e))return e}const _r=/;(?![^(]*\))/g,wr=/:([^]+)/,vr=/\/\*[^]*?\*\//g;function br(e){const t={};return e.replace(vr,"").split(_r).forEach(n=>{if(n){const i=n.split(wr);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function vi(e){let t="";if(se(e))t=e;else if(K(e))for(let n=0;n<e.length;n++){const i=vi(e[n]);i&&(t+=i+" ")}else if(te(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const kr="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",xr=mi(kr);function Yo(e){return!!e||e===""}const Qo=e=>se(e)?e:e==null?"":K(e)||te(e)&&(e.toString===Go||!U(e.toString))?JSON.stringify(e,es,2):String(e),es=(e,t)=>t&&t.__v_isRef?es(e,t.value):bt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[i,o],s)=>(n[Fn(i,s)+" =>"]=o,n),{})}:Zo(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Fn(n))}:Pt(t)?Fn(t):te(t)&&!K(t)&&!Xo(t)?String(t):t,Fn=(e,t="")=>{var n;return Pt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ke;class Sr{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=ke,!t&&ke&&(this.index=(ke.scopes||(ke.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=ke;try{return ke=this,t()}finally{ke=n}}}on(){ke=this}off(){ke=this.parent}stop(t){if(this._active){let n,i;for(n=0,i=this.effects.length;n<i;n++)this.effects[n].stop();for(n=0,i=this.cleanups.length;n<i;n++)this.cleanups[n]();if(this.scopes)for(n=0,i=this.scopes.length;n<i;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0,this._active=!1}}}function Er(e,t=ke){t&&t.active&&t.effects.push(e)}function Ir(){return ke}let ct;class bi{constructor(t,n,i,o){this.fn=t,this.trigger=n,this.scheduler=i,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,Er(this,o)}get dirty(){if(this._dirtyLevel===2||this._dirtyLevel===3){this._dirtyLevel=1,ut();for(let t=0;t<this._depsLength;t++){const n=this.deps[t];if(n.computed&&(Cr(n.computed),this._dirtyLevel>=4))break}this._dirtyLevel===1&&(this._dirtyLevel=0),ft()}return this._dirtyLevel>=4}set dirty(t){this._dirtyLevel=t?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=qe,n=ct;try{return qe=!0,ct=this,this._runnings++,Gi(this),this.fn()}finally{Xi(this),this._runnings--,ct=n,qe=t}}stop(){var t;this.active&&(Gi(this),Xi(this),(t=this.onStop)==null||t.call(this),this.active=!1)}}function Cr(e){return e.value}function Gi(e){e._trackId++,e._depsLength=0}function Xi(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)ts(e.deps[t],e);e.deps.length=e._depsLength}}function ts(e,t){const n=e.get(t);n!==void 0&&t._trackId!==n&&(e.delete(t),e.size===0&&e.cleanup())}let qe=!0,Yn=0;const ns=[];function ut(){ns.push(qe),qe=!1}function ft(){const e=ns.pop();qe=e===void 0?!0:e}function ki(){Yn++}function xi(){for(Yn--;!Yn&&Qn.length;)Qn.shift()()}function is(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const i=e.deps[e._depsLength];i!==t?(i&&ts(i,e),e.deps[e._depsLength++]=t):e._depsLength++}}const Qn=[];function os(e,t,n){ki();for(const i of e.keys()){let o;i._dirtyLevel<t&&(o??(o=e.get(i)===i._trackId))&&(i._shouldSchedule||(i._shouldSchedule=i._dirtyLevel===0),i._dirtyLevel=t),i._shouldSchedule&&(o??(o=e.get(i)===i._trackId))&&(i.trigger(),(!i._runnings||i.allowRecurse)&&i._dirtyLevel!==2&&(i._shouldSchedule=!1,i.scheduler&&Qn.push(i.scheduler)))}xi()}const ss=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},ei=new WeakMap,at=Symbol(""),ti=Symbol("");function me(e,t,n){if(qe&&ct){let i=ei.get(e);i||ei.set(e,i=new Map);let o=i.get(n);o||i.set(n,o=ss(()=>i.delete(n))),is(ct,o)}}function He(e,t,n,i,o,s){const r=ei.get(e);if(!r)return;let a=[];if(t==="clear")a=[...r.values()];else if(n==="length"&&K(e)){const c=Number(i);r.forEach((u,l)=>{(l==="length"||!Pt(l)&&l>=c)&&a.push(u)})}else switch(n!==void 0&&a.push(r.get(n)),t){case"add":K(e)?_i(n)&&a.push(r.get("length")):(a.push(r.get(at)),bt(e)&&a.push(r.get(ti)));break;case"delete":K(e)||(a.push(r.get(at)),bt(e)&&a.push(r.get(ti)));break;case"set":bt(e)&&a.push(r.get(at));break}ki();for(const c of a)c&&os(c,4);xi()}const Or=mi("__proto__,__v_isRef,__isVue"),rs=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Pt)),Ji=Pr();function Pr(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const i=D(this);for(let s=0,r=this.length;s<r;s++)me(i,"get",s+"");const o=i[t](...n);return o===-1||o===!1?i[t](...n.map(D)):o}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){ut(),ki();const i=D(this)[t].apply(this,n);return xi(),ft(),i}}),e}function Rr(e){const t=D(this);return me(t,"has",e),t.hasOwnProperty(e)}class cs{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,i){const o=this._isReadonly,s=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return s;if(n==="__v_raw")return i===(o?s?Vr:fs:s?us:ls).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const r=K(t);if(!o){if(r&&V(Ji,n))return Reflect.get(Ji,n,i);if(n==="hasOwnProperty")return Rr}const a=Reflect.get(t,n,i);return(Pt(n)?rs.has(n):Or(n))||(o||me(t,"get",n),s)?a:ge(a)?r&&_i(n)?a:a.value:te(a)?o?hs(a):En(a):a}}class as extends cs{constructor(t=!1){super(!1,t)}set(t,n,i,o){let s=t[n];if(!this._isShallow){const c=Et(s);if(!yn(i)&&!Et(i)&&(s=D(s),i=D(i)),!K(t)&&ge(s)&&!ge(i))return c?!1:(s.value=i,!0)}const r=K(t)&&_i(n)?Number(n)<t.length:V(t,n),a=Reflect.set(t,n,i,o);return t===D(o)&&(r?Qe(i,s)&&He(t,"set",n,i):He(t,"add",n,i)),a}deleteProperty(t,n){const i=V(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&i&&He(t,"delete",n,void 0),o}has(t,n){const i=Reflect.has(t,n);return(!Pt(n)||!rs.has(n))&&me(t,"has",n),i}ownKeys(t){return me(t,"iterate",K(t)?"length":at),Reflect.ownKeys(t)}}class Tr extends cs{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Ar=new as,jr=new Tr,Lr=new as(!0),Si=e=>e,Sn=e=>Reflect.getPrototypeOf(e);function nn(e,t,n=!1,i=!1){e=e.__v_raw;const o=D(e),s=D(t);n||(Qe(t,s)&&me(o,"get",t),me(o,"get",s));const{has:r}=Sn(o),a=i?Si:n?Ci:Zt;if(r.call(o,t))return a(e.get(t));if(r.call(o,s))return a(e.get(s));e!==o&&e.get(t)}function on(e,t=!1){const n=this.__v_raw,i=D(n),o=D(e);return t||(Qe(e,o)&&me(i,"has",e),me(i,"has",o)),e===o?n.has(e):n.has(e)||n.has(o)}function sn(e,t=!1){return e=e.__v_raw,!t&&me(D(e),"iterate",at),Reflect.get(e,"size",e)}function qi(e){e=D(e);const t=D(this);return Sn(t).has.call(t,e)||(t.add(e),He(t,"add",e,e)),this}function Yi(e,t){t=D(t);const n=D(this),{has:i,get:o}=Sn(n);let s=i.call(n,e);s||(e=D(e),s=i.call(n,e));const r=o.call(n,e);return n.set(e,t),s?Qe(t,r)&&He(n,"set",e,t):He(n,"add",e,t),this}function Qi(e){const t=D(this),{has:n,get:i}=Sn(t);let o=n.call(t,e);o||(e=D(e),o=n.call(t,e)),i&&i.call(t,e);const s=t.delete(e);return o&&He(t,"delete",e,void 0),s}function eo(){const e=D(this),t=e.size!==0,n=e.clear();return t&&He(e,"clear",void 0,void 0),n}function rn(e,t){return function(i,o){const s=this,r=s.__v_raw,a=D(r),c=t?Si:e?Ci:Zt;return!e&&me(a,"iterate",at),r.forEach((u,l)=>i.call(o,c(u),c(l),s))}}function cn(e,t,n){return function(...i){const o=this.__v_raw,s=D(o),r=bt(s),a=e==="entries"||e===Symbol.iterator&&r,c=e==="keys"&&r,u=o[e](...i),l=n?Si:t?Ci:Zt;return!t&&me(s,"iterate",c?ti:at),{next(){const{value:f,done:h}=u.next();return h?{value:f,done:h}:{value:a?[l(f[0]),l(f[1])]:l(f),done:h}},[Symbol.iterator](){return this}}}}function We(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Mr(){const e={get(s){return nn(this,s)},get size(){return sn(this)},has:on,add:qi,set:Yi,delete:Qi,clear:eo,forEach:rn(!1,!1)},t={get(s){return nn(this,s,!1,!0)},get size(){return sn(this)},has:on,add:qi,set:Yi,delete:Qi,clear:eo,forEach:rn(!1,!0)},n={get(s){return nn(this,s,!0)},get size(){return sn(this,!0)},has(s){return on.call(this,s,!0)},add:We("add"),set:We("set"),delete:We("delete"),clear:We("clear"),forEach:rn(!0,!1)},i={get(s){return nn(this,s,!0,!0)},get size(){return sn(this,!0)},has(s){return on.call(this,s,!0)},add:We("add"),set:We("set"),delete:We("delete"),clear:We("clear"),forEach:rn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=cn(s,!1,!1),n[s]=cn(s,!0,!1),t[s]=cn(s,!1,!0),i[s]=cn(s,!0,!0)}),[e,n,t,i]}const[Nr,zr,Fr,Kr]=Mr();function Ei(e,t){const n=t?e?Kr:Fr:e?zr:Nr;return(i,o,s)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?i:Reflect.get(V(n,o)&&o in i?n:i,o,s)}const Ur={get:Ei(!1,!1)},Hr={get:Ei(!1,!0)},$r={get:Ei(!0,!1)},ls=new WeakMap,us=new WeakMap,fs=new WeakMap,Vr=new WeakMap;function Dr(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Wr(e){return e.__v_skip||!Object.isExtensible(e)?0:Dr(pr(e))}function En(e){return Et(e)?e:Ii(e,!1,Ar,Ur,ls)}function ds(e){return Ii(e,!1,Lr,Hr,us)}function hs(e){return Ii(e,!0,jr,$r,fs)}function Ii(e,t,n,i,o){if(!te(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=o.get(e);if(s)return s;const r=Wr(e);if(r===0)return e;const a=new Proxy(e,r===2?i:n);return o.set(e,a),a}function kt(e){return Et(e)?kt(e.__v_raw):!!(e&&e.__v_isReactive)}function Et(e){return!!(e&&e.__v_isReadonly)}function yn(e){return!!(e&&e.__v_isShallow)}function ps(e){return kt(e)||Et(e)}function D(e){const t=e&&e.__v_raw;return t?D(t):e}function ms(e){return Object.isExtensible(e)&&gn(e,"__v_skip",!0),e}const Zt=e=>te(e)?En(e):e,Ci=e=>te(e)?hs(e):e;class gs{constructor(t,n,i,o){this.getter=t,this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new bi(()=>t(this._value),()=>un(this,this.effect._dirtyLevel===2?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=i}get value(){const t=D(this);return(!t._cacheable||t.effect.dirty)&&Qe(t._value,t._value=t.effect.run())&&un(t,4),ys(t),t.effect._dirtyLevel>=2&&un(t,2),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function Zr(e,t,n=!1){let i,o;const s=U(e);return s?(i=e,o=_e):(i=e.get,o=e.set),new gs(i,o,s||!o,n)}function ys(e){var t;qe&&ct&&(e=D(e),is(ct,(t=e.dep)!=null?t:e.dep=ss(()=>e.dep=void 0,e instanceof gs?e:void 0)))}function un(e,t=4,n){e=D(e);const i=e.dep;i&&os(i,t)}function ge(e){return!!(e&&e.__v_isRef===!0)}function pe(e){return _s(e,!1)}function Br(e){return _s(e,!0)}function _s(e,t){return ge(e)?e:new Gr(e,t)}class Gr{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:D(t),this._value=n?t:Zt(t)}get value(){return ys(this),this._value}set value(t){const n=this.__v_isShallow||yn(t)||Et(t);t=n?t:D(t),Qe(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:Zt(t),un(this,4))}}function lt(e){return ge(e)?e.value:e}const Xr={get:(e,t,n)=>lt(Reflect.get(e,t,n)),set:(e,t,n,i)=>{const o=e[t];return ge(o)&&!ge(n)?(o.value=n,!0):Reflect.set(e,t,n,i)}};function ws(e){return kt(e)?e:new Proxy(e,Xr)}/**
* @vue/runtime-core v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ye(e,t,n,i){try{return i?e(...i):e()}catch(o){In(o,t,n)}}function Ee(e,t,n,i){if(U(e)){const s=Ye(e,t,n,i);return s&&Bo(s)&&s.catch(r=>{In(r,t,n)}),s}const o=[];for(let s=0;s<e.length;s++)o.push(Ee(e[s],t,n,i));return o}function In(e,t,n,i=!0){const o=t?t.vnode:null;if(t){let s=t.parent;const r=t.proxy,a=`https://vuejs.org/error-reference/#runtime-${n}`;for(;s;){const u=s.ec;if(u){for(let l=0;l<u.length;l++)if(u[l](e,r,a)===!1)return}s=s.parent}const c=t.appContext.config.errorHandler;if(c){Ye(c,null,10,[e,r,a]);return}}Jr(e,n,o,i)}function Jr(e,t,n,i=!0){console.error(e)}let Bt=!1,ni=!1;const le=[];let Le=0;const xt=[];let Be=null,st=0;const vs=Promise.resolve();let Oi=null;function bs(e){const t=Oi||vs;return e?t.then(this?e.bind(this):e):t}function qr(e){let t=Le+1,n=le.length;for(;t<n;){const i=t+n>>>1,o=le[i],s=Gt(o);s<e||s===e&&o.pre?t=i+1:n=i}return t}function Pi(e){(!le.length||!le.includes(e,Bt&&e.allowRecurse?Le+1:Le))&&(e.id==null?le.push(e):le.splice(qr(e.id),0,e),ks())}function ks(){!Bt&&!ni&&(ni=!0,Oi=vs.then(Ss))}function Yr(e){const t=le.indexOf(e);t>Le&&le.splice(t,1)}function Qr(e){K(e)?xt.push(...e):(!Be||!Be.includes(e,e.allowRecurse?st+1:st))&&xt.push(e),ks()}function to(e,t,n=Bt?Le+1:0){for(;n<le.length;n++){const i=le[n];if(i&&i.pre){if(e&&i.id!==e.uid)continue;le.splice(n,1),n--,i()}}}function xs(e){if(xt.length){const t=[...new Set(xt)].sort((n,i)=>Gt(n)-Gt(i));if(xt.length=0,Be){Be.push(...t);return}for(Be=t,st=0;st<Be.length;st++)Be[st]();Be=null,st=0}}const Gt=e=>e.id==null?1/0:e.id,ec=(e,t)=>{const n=Gt(e)-Gt(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Ss(e){ni=!1,Bt=!0,le.sort(ec);try{for(Le=0;Le<le.length;Le++){const t=le[Le];t&&t.active!==!1&&Ye(t,null,14)}}finally{Le=0,le.length=0,xs(),Bt=!1,Oi=null,(le.length||xt.length)&&Ss()}}function tc(e,t,...n){if(e.isUnmounted)return;const i=e.vnode.props||ee;let o=n;const s=t.startsWith("update:"),r=s&&t.slice(7);if(r&&r in i){const l=`${r==="modelValue"?"model":r}Modifiers`,{number:f,trim:h}=i[l]||ee;h&&(o=n.map(m=>se(m)?m.trim():m)),f&&(o=n.map(yr))}let a,c=i[a=Nn(t)]||i[a=Nn(St(t))];!c&&s&&(c=i[a=Nn(Rt(t))]),c&&Ee(c,e,6,o);const u=i[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Ee(u,e,6,o)}}function Es(e,t,n=!1){const i=t.emitsCache,o=i.get(e);if(o!==void 0)return o;const s=e.emits;let r={},a=!1;if(!U(e)){const c=u=>{const l=Es(u,t,!0);l&&(a=!0,re(r,l))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!s&&!a?(te(e)&&i.set(e,null),null):(K(s)?s.forEach(c=>r[c]=null):re(r,s),te(e)&&i.set(e,r),r)}function Cn(e,t){return!e||!bn(t)?!1:(t=t.slice(2).replace(/Once$/,""),V(e,t[0].toLowerCase()+t.slice(1))||V(e,Rt(t))||V(e,t))}let Me=null,Is=null;function _n(e){const t=Me;return Me=e,Is=e&&e.type.__scopeId||null,t}function nc(e,t=Me,n){if(!t||e._n)return e;const i=(...o)=>{i._d&&fo(-1);const s=_n(t);let r;try{r=e(...o)}finally{_n(s),i._d&&fo(1)}return r};return i._n=!0,i._c=!0,i._d=!0,i}function Kn(e){const{type:t,vnode:n,proxy:i,withProxy:o,props:s,propsOptions:[r],slots:a,attrs:c,emit:u,render:l,renderCache:f,data:h,setupState:m,ctx:y,inheritAttrs:x}=e;let R,S;const k=_n(e);try{if(n.shapeFlag&4){const H=o||i,J=H;R=Ae(l.call(J,H,f,s,m,h,y)),S=c}else{const H=t;R=Ae(H.length>1?H(s,{attrs:c,slots:a,emit:u}):H(s,null)),S=t.props?c:ic(c)}}catch(H){Vt.length=0,In(H,e,1),R=we(Xt)}let j=R;if(S&&x!==!1){const H=Object.keys(S),{shapeFlag:J}=j;H.length&&J&7&&(r&&H.some(gi)&&(S=oc(S,r)),j=It(j,S))}return n.dirs&&(j=It(j),j.dirs=j.dirs?j.dirs.concat(n.dirs):n.dirs),n.transition&&(j.transition=n.transition),R=j,_n(k),R}const ic=e=>{let t;for(const n in e)(n==="class"||n==="style"||bn(n))&&((t||(t={}))[n]=e[n]);return t},oc=(e,t)=>{const n={};for(const i in e)(!gi(i)||!(i.slice(9)in t))&&(n[i]=e[i]);return n};function sc(e,t,n){const{props:i,children:o,component:s}=e,{props:r,children:a,patchFlag:c}=t,u=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return i?no(i,r,u):!!r;if(c&8){const l=t.dynamicProps;for(let f=0;f<l.length;f++){const h=l[f];if(r[h]!==i[h]&&!Cn(u,h))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:i===r?!1:i?r?no(i,r,u):!0:!!r;return!1}function no(e,t,n){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let o=0;o<i.length;o++){const s=i[o];if(t[s]!==e[s]&&!Cn(n,s))return!0}return!1}function rc({vnode:e,parent:t},n){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=n,t=t.parent;else break}}const cc=Symbol.for("v-ndc"),ac=e=>e.__isSuspense;function lc(e,t){t&&t.pendingBranch?K(e)?t.effects.push(...e):t.effects.push(e):Qr(e)}const uc=Symbol.for("v-scx"),fc=()=>ve(uc),an={};function Ut(e,t,n){return Cs(e,t,n)}function Cs(e,t,{immediate:n,deep:i,flush:o,once:s,onTrack:r,onTrigger:a}=ee){if(t&&s){const z=t;t=(...q)=>{z(...q),J()}}const c=fe,u=z=>i===!0?z:_t(z,i===!1?1:void 0);let l,f=!1,h=!1;if(ge(e)?(l=()=>e.value,f=yn(e)):kt(e)?(l=()=>u(e),f=!0):K(e)?(h=!0,f=e.some(z=>kt(z)||yn(z)),l=()=>e.map(z=>{if(ge(z))return z.value;if(kt(z))return u(z);if(U(z))return Ye(z,c,2)})):U(e)?t?l=()=>Ye(e,c,2):l=()=>(m&&m(),Ee(e,c,3,[y])):l=_e,t&&i){const z=l;l=()=>_t(z())}let m,y=z=>{m=j.onStop=()=>{Ye(z,c,4),m=j.onStop=void 0}},x;if(An)if(y=_e,t?n&&Ee(t,c,3,[l(),h?[]:void 0,y]):l(),o==="sync"){const z=fc();x=z.__watcherHandles||(z.__watcherHandles=[])}else return _e;let R=h?new Array(e.length).fill(an):an;const S=()=>{if(!(!j.active||!j.dirty))if(t){const z=j.run();(i||f||(h?z.some((q,ne)=>Qe(q,R[ne])):Qe(z,R)))&&(m&&m(),Ee(t,c,3,[z,R===an?void 0:h&&R[0]===an?[]:R,y]),R=z)}else j.run()};S.allowRecurse=!!t;let k;o==="sync"?k=S:o==="post"?k=()=>he(S,c&&c.suspense):(S.pre=!0,c&&(S.id=c.uid),k=()=>Pi(S));const j=new bi(l,_e,k),H=Ir(),J=()=>{j.stop(),H&&yi(H.effects,j)};return t?n?S():R=j.run():o==="post"?he(j.run.bind(j),c&&c.suspense):j.run(),x&&x.push(J),J}function dc(e,t,n){const i=this.proxy,o=se(e)?e.includes(".")?Os(i,e):()=>i[e]:e.bind(i,i);let s;U(t)?s=t:(s=t.handler,n=t);const r=en(this),a=Cs(o,s.bind(i),n);return r(),a}function Os(e,t){const n=t.split(".");return()=>{let i=e;for(let o=0;o<n.length&&i;o++)i=i[n[o]];return i}}function _t(e,t,n=0,i){if(!te(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if(i=i||new Set,i.has(e))return e;if(i.add(e),ge(e))_t(e.value,t,n,i);else if(K(e))for(let o=0;o<e.length;o++)_t(e[o],t,n,i);else if(Zo(e)||bt(e))e.forEach(o=>{_t(o,t,n,i)});else if(Xo(e))for(const o in e)_t(e[o],t,n,i);return e}function tt(e,t,n,i){const o=e.dirs,s=t&&t.dirs;for(let r=0;r<o.length;r++){const a=o[r];s&&(a.oldValue=s[r].value);let c=a.dir[i];c&&(ut(),Ee(c,n,8,[e.el,a,e,t]),ft())}}/*! #__NO_SIDE_EFFECTS__ */function Tt(e,t){return U(e)?re({name:e.name},t,{setup:e}):e}const fn=e=>!!e.type.__asyncLoader,Ps=e=>e.type.__isKeepAlive;function hc(e,t){Rs(e,"a",t)}function pc(e,t){Rs(e,"da",t)}function Rs(e,t,n=fe){const i=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(On(t,i,n),n){let o=n.parent;for(;o&&o.parent;)Ps(o.parent.vnode)&&mc(i,t,n,o),o=o.parent}}function mc(e,t,n,i){const o=On(t,e,i,!0);Ts(()=>{yi(i[t],o)},n)}function On(e,t,n=fe,i=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...r)=>{if(n.isUnmounted)return;ut();const a=en(n),c=Ee(t,n,e,r);return a(),ft(),c});return i?o.unshift(s):o.push(s),s}}const $e=e=>(t,n=fe)=>(!An||e==="sp")&&On(e,(...i)=>t(...i),n),gc=$e("bm"),Ri=$e("m"),yc=$e("bu"),_c=$e("u"),wc=$e("bum"),Ts=$e("um"),vc=$e("sp"),bc=$e("rtg"),kc=$e("rtc");function xc(e,t=fe){On("ec",e,t)}const ii=e=>e?Ds(e)?Mi(e)||e.proxy:ii(e.parent):null,Ht=re(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ii(e.parent),$root:e=>ii(e.root),$emit:e=>e.emit,$options:e=>Ti(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,Pi(e.update)}),$nextTick:e=>e.n||(e.n=bs.bind(e.proxy)),$watch:e=>dc.bind(e)}),Un=(e,t)=>e!==ee&&!e.__isScriptSetup&&V(e,t),Sc={get({_:e},t){const{ctx:n,setupState:i,data:o,props:s,accessCache:r,type:a,appContext:c}=e;let u;if(t[0]!=="$"){const m=r[t];if(m!==void 0)switch(m){case 1:return i[t];case 2:return o[t];case 4:return n[t];case 3:return s[t]}else{if(Un(i,t))return r[t]=1,i[t];if(o!==ee&&V(o,t))return r[t]=2,o[t];if((u=e.propsOptions[0])&&V(u,t))return r[t]=3,s[t];if(n!==ee&&V(n,t))return r[t]=4,n[t];oi&&(r[t]=0)}}const l=Ht[t];let f,h;if(l)return t==="$attrs"&&me(e,"get",t),l(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==ee&&V(n,t))return r[t]=4,n[t];if(h=c.config.globalProperties,V(h,t))return h[t]},set({_:e},t,n){const{data:i,setupState:o,ctx:s}=e;return Un(o,t)?(o[t]=n,!0):i!==ee&&V(i,t)?(i[t]=n,!0):V(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:i,appContext:o,propsOptions:s}},r){let a;return!!n[r]||e!==ee&&V(e,r)||Un(t,r)||(a=s[0])&&V(a,r)||V(i,r)||V(Ht,r)||V(o.config.globalProperties,r)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:V(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function io(e){return K(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let oi=!0;function Ec(e){const t=Ti(e),n=e.proxy,i=e.ctx;oi=!1,t.beforeCreate&&oo(t.beforeCreate,e,"bc");const{data:o,computed:s,methods:r,watch:a,provide:c,inject:u,created:l,beforeMount:f,mounted:h,beforeUpdate:m,updated:y,activated:x,deactivated:R,beforeDestroy:S,beforeUnmount:k,destroyed:j,unmounted:H,render:J,renderTracked:z,renderTriggered:q,errorCaptured:ne,serverPrefetch:ze,expose:Ce,inheritAttrs:Ve,components:et,directives:Oe,filters:At}=t;if(u&&Ic(u,i,null),r)for(const G in r){const W=r[G];U(W)&&(i[G]=W.bind(n))}if(o){const G=o.call(n,n);te(G)&&(e.data=En(G))}if(oi=!0,s)for(const G in s){const W=s[G],Fe=U(W)?W.bind(n,n):U(W.get)?W.get.bind(n,n):_e,De=!U(W)&&U(W.set)?W.set.bind(n):_e,Pe=xe({get:Fe,set:De});Object.defineProperty(i,G,{enumerable:!0,configurable:!0,get:()=>Pe.value,set:de=>Pe.value=de})}if(a)for(const G in a)As(a[G],i,n,G);if(c){const G=U(c)?c.call(n):c;Reflect.ownKeys(G).forEach(W=>{dn(W,G[W])})}l&&oo(l,e,"c");function ie(G,W){K(W)?W.forEach(Fe=>G(Fe.bind(n))):W&&G(W.bind(n))}if(ie(gc,f),ie(Ri,h),ie(yc,m),ie(_c,y),ie(hc,x),ie(pc,R),ie(xc,ne),ie(kc,z),ie(bc,q),ie(wc,k),ie(Ts,H),ie(vc,ze),K(Ce))if(Ce.length){const G=e.exposed||(e.exposed={});Ce.forEach(W=>{Object.defineProperty(G,W,{get:()=>n[W],set:Fe=>n[W]=Fe})})}else e.exposed||(e.exposed={});J&&e.render===_e&&(e.render=J),Ve!=null&&(e.inheritAttrs=Ve),et&&(e.components=et),Oe&&(e.directives=Oe)}function Ic(e,t,n=_e){K(e)&&(e=si(e));for(const i in e){const o=e[i];let s;te(o)?"default"in o?s=ve(o.from||i,o.default,!0):s=ve(o.from||i):s=ve(o),ge(s)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s.value,set:r=>s.value=r}):t[i]=s}}function oo(e,t,n){Ee(K(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,n)}function As(e,t,n,i){const o=i.includes(".")?Os(n,i):()=>n[i];if(se(e)){const s=t[e];U(s)&&Ut(o,s)}else if(U(e))Ut(o,e.bind(n));else if(te(e))if(K(e))e.forEach(s=>As(s,t,n,i));else{const s=U(e.handler)?e.handler.bind(n):t[e.handler];U(s)&&Ut(o,s,e)}}function Ti(e){const t=e.type,{mixins:n,extends:i}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:r}}=e.appContext,a=s.get(t);let c;return a?c=a:!o.length&&!n&&!i?c=t:(c={},o.length&&o.forEach(u=>wn(c,u,r,!0)),wn(c,t,r)),te(t)&&s.set(t,c),c}function wn(e,t,n,i=!1){const{mixins:o,extends:s}=t;s&&wn(e,s,n,!0),o&&o.forEach(r=>wn(e,r,n,!0));for(const r in t)if(!(i&&r==="expose")){const a=Cc[r]||n&&n[r];e[r]=a?a(e[r],t[r]):t[r]}return e}const Cc={data:so,props:ro,emits:ro,methods:zt,computed:zt,beforeCreate:ue,created:ue,beforeMount:ue,mounted:ue,beforeUpdate:ue,updated:ue,beforeDestroy:ue,beforeUnmount:ue,destroyed:ue,unmounted:ue,activated:ue,deactivated:ue,errorCaptured:ue,serverPrefetch:ue,components:zt,directives:zt,watch:Pc,provide:so,inject:Oc};function so(e,t){return t?e?function(){return re(U(e)?e.call(this,this):e,U(t)?t.call(this,this):t)}:t:e}function Oc(e,t){return zt(si(e),si(t))}function si(e){if(K(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ue(e,t){return e?[...new Set([].concat(e,t))]:t}function zt(e,t){return e?re(Object.create(null),e,t):t}function ro(e,t){return e?K(e)&&K(t)?[...new Set([...e,...t])]:re(Object.create(null),io(e),io(t??{})):t}function Pc(e,t){if(!e)return t;if(!t)return e;const n=re(Object.create(null),e);for(const i in t)n[i]=ue(e[i],t[i]);return n}function js(){return{app:null,config:{isNativeTag:dr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Rc=0;function Tc(e,t){return function(i,o=null){U(i)||(i=re({},i)),o!=null&&!te(o)&&(o=null);const s=js(),r=new WeakSet;let a=!1;const c=s.app={_uid:Rc++,_component:i,_props:o,_container:null,_context:s,_instance:null,version:na,get config(){return s.config},set config(u){},use(u,...l){return r.has(u)||(u&&U(u.install)?(r.add(u),u.install(c,...l)):U(u)&&(r.add(u),u(c,...l))),c},mixin(u){return s.mixins.includes(u)||s.mixins.push(u),c},component(u,l){return l?(s.components[u]=l,c):s.components[u]},directive(u,l){return l?(s.directives[u]=l,c):s.directives[u]},mount(u,l,f){if(!a){const h=we(i,o);return h.appContext=s,f===!0?f="svg":f===!1&&(f=void 0),l&&t?t(h,u):e(h,u,f),a=!0,c._container=u,u.__vue_app__=c,Mi(h.component)||h.component.proxy}},unmount(){a&&(e(null,c._container),delete c._container.__vue_app__)},provide(u,l){return s.provides[u]=l,c},runWithContext(u){const l=$t;$t=c;try{return u()}finally{$t=l}}};return c}}let $t=null;function dn(e,t){if(fe){let n=fe.provides;const i=fe.parent&&fe.parent.provides;i===n&&(n=fe.provides=Object.create(i)),n[e]=t}}function ve(e,t,n=!1){const i=fe||Me;if(i||$t){const o=i?i.parent==null?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:$t._context.provides;if(o&&e in o)return o[e];if(arguments.length>1)return n&&U(t)?t.call(i&&i.proxy):t}}function Ac(e,t,n,i=!1){const o={},s={};gn(s,Tn,1),e.propsDefaults=Object.create(null),Ls(e,t,o,s);for(const r in e.propsOptions[0])r in o||(o[r]=void 0);n?e.props=i?o:ds(o):e.type.props?e.props=o:e.props=s,e.attrs=s}function jc(e,t,n,i){const{props:o,attrs:s,vnode:{patchFlag:r}}=e,a=D(o),[c]=e.propsOptions;let u=!1;if((i||r>0)&&!(r&16)){if(r&8){const l=e.vnode.dynamicProps;for(let f=0;f<l.length;f++){let h=l[f];if(Cn(e.emitsOptions,h))continue;const m=t[h];if(c)if(V(s,h))m!==s[h]&&(s[h]=m,u=!0);else{const y=St(h);o[y]=ri(c,a,y,m,e,!1)}else m!==s[h]&&(s[h]=m,u=!0)}}}else{Ls(e,t,o,s)&&(u=!0);let l;for(const f in a)(!t||!V(t,f)&&((l=Rt(f))===f||!V(t,l)))&&(c?n&&(n[f]!==void 0||n[l]!==void 0)&&(o[f]=ri(c,a,f,void 0,e,!0)):delete o[f]);if(s!==a)for(const f in s)(!t||!V(t,f))&&(delete s[f],u=!0)}u&&He(e,"set","$attrs")}function Ls(e,t,n,i){const[o,s]=e.propsOptions;let r=!1,a;if(t)for(let c in t){if(Kt(c))continue;const u=t[c];let l;o&&V(o,l=St(c))?!s||!s.includes(l)?n[l]=u:(a||(a={}))[l]=u:Cn(e.emitsOptions,c)||(!(c in i)||u!==i[c])&&(i[c]=u,r=!0)}if(s){const c=D(n),u=a||ee;for(let l=0;l<s.length;l++){const f=s[l];n[f]=ri(o,c,f,u[f],e,!V(u,f))}}return r}function ri(e,t,n,i,o,s){const r=e[n];if(r!=null){const a=V(r,"default");if(a&&i===void 0){const c=r.default;if(r.type!==Function&&!r.skipFactory&&U(c)){const{propsDefaults:u}=o;if(n in u)i=u[n];else{const l=en(o);i=u[n]=c.call(null,t),l()}}else i=c}r[0]&&(s&&!a?i=!1:r[1]&&(i===""||i===Rt(n))&&(i=!0))}return i}function Ms(e,t,n=!1){const i=t.propsCache,o=i.get(e);if(o)return o;const s=e.props,r={},a=[];let c=!1;if(!U(e)){const l=f=>{c=!0;const[h,m]=Ms(f,t,!0);re(r,h),m&&a.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}if(!s&&!c)return te(e)&&i.set(e,vt),vt;if(K(s))for(let l=0;l<s.length;l++){const f=St(s[l]);co(f)&&(r[f]=ee)}else if(s)for(const l in s){const f=St(l);if(co(f)){const h=s[l],m=r[f]=K(h)||U(h)?{type:h}:re({},h);if(m){const y=uo(Boolean,m.type),x=uo(String,m.type);m[0]=y>-1,m[1]=x<0||y<x,(y>-1||V(m,"default"))&&a.push(f)}}}const u=[r,a];return te(e)&&i.set(e,u),u}function co(e){return e[0]!=="$"&&!Kt(e)}function ao(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function lo(e,t){return ao(e)===ao(t)}function uo(e,t){return K(t)?t.findIndex(n=>lo(n,e)):U(t)&&lo(t,e)?0:-1}const Ns=e=>e[0]==="_"||e==="$stable",Ai=e=>K(e)?e.map(Ae):[Ae(e)],Lc=(e,t,n)=>{if(t._n)return t;const i=nc((...o)=>Ai(t(...o)),n);return i._c=!1,i},zs=(e,t,n)=>{const i=e._ctx;for(const o in e){if(Ns(o))continue;const s=e[o];if(U(s))t[o]=Lc(o,s,i);else if(s!=null){const r=Ai(s);t[o]=()=>r}}},Fs=(e,t)=>{const n=Ai(t);e.slots.default=()=>n},Mc=(e,t)=>{if(e.vnode.shapeFlag&32){const n=t._;n?(e.slots=D(t),gn(t,"_",n)):zs(t,e.slots={})}else e.slots={},t&&Fs(e,t);gn(e.slots,Tn,1)},Nc=(e,t,n)=>{const{vnode:i,slots:o}=e;let s=!0,r=ee;if(i.shapeFlag&32){const a=t._;a?n&&a===1?s=!1:(re(o,t),!n&&a===1&&delete o._):(s=!t.$stable,zs(t,o)),r=t}else t&&(Fs(e,t),r={default:1});if(s)for(const a in o)!Ns(a)&&r[a]==null&&delete o[a]};function ci(e,t,n,i,o=!1){if(K(e)){e.forEach((h,m)=>ci(h,t&&(K(t)?t[m]:t),n,i,o));return}if(fn(i)&&!o)return;const s=i.shapeFlag&4?Mi(i.component)||i.component.proxy:i.el,r=o?null:s,{i:a,r:c}=e,u=t&&t.r,l=a.refs===ee?a.refs={}:a.refs,f=a.setupState;if(u!=null&&u!==c&&(se(u)?(l[u]=null,V(f,u)&&(f[u]=null)):ge(u)&&(u.value=null)),U(c))Ye(c,a,12,[r,l]);else{const h=se(c),m=ge(c);if(h||m){const y=()=>{if(e.f){const x=h?V(f,c)?f[c]:l[c]:c.value;o?K(x)&&yi(x,s):K(x)?x.includes(s)||x.push(s):h?(l[c]=[s],V(f,c)&&(f[c]=l[c])):(c.value=[s],e.k&&(l[e.k]=c.value))}else h?(l[c]=r,V(f,c)&&(f[c]=r)):m&&(c.value=r,e.k&&(l[e.k]=r))};r?(y.id=-1,he(y,n)):y()}}}const he=lc;function zc(e){return Fc(e)}function Fc(e,t){const n=qo();n.__VUE__=!0;const{insert:i,remove:o,patchProp:s,createElement:r,createText:a,createComment:c,setText:u,setElementText:l,parentNode:f,nextSibling:h,setScopeId:m=_e,insertStaticContent:y}=e,x=(d,p,g,v=null,_=null,I=null,P=void 0,E=null,C=!!p.dynamicChildren)=>{if(d===p)return;d&&!Lt(d,p)&&(v=w(d),de(d,_,I,!0),d=null),p.patchFlag===-2&&(C=!1,p.dynamicChildren=null);const{type:b,ref:A,shapeFlag:N}=p;switch(b){case Pn:R(d,p,g,v);break;case Xt:S(d,p,g,v);break;case $n:d==null&&k(p,g,v,P);break;case Ue:et(d,p,g,v,_,I,P,E,C);break;default:N&1?J(d,p,g,v,_,I,P,E,C):N&6?Oe(d,p,g,v,_,I,P,E,C):(N&64||N&128)&&b.process(d,p,g,v,_,I,P,E,C,L)}A!=null&&_&&ci(A,d&&d.ref,I,p||d,!p)},R=(d,p,g,v)=>{if(d==null)i(p.el=a(p.children),g,v);else{const _=p.el=d.el;p.children!==d.children&&u(_,p.children)}},S=(d,p,g,v)=>{d==null?i(p.el=c(p.children||""),g,v):p.el=d.el},k=(d,p,g,v)=>{[d.el,d.anchor]=y(d.children,p,g,v,d.el,d.anchor)},j=({el:d,anchor:p},g,v)=>{let _;for(;d&&d!==p;)_=h(d),i(d,g,v),d=_;i(p,g,v)},H=({el:d,anchor:p})=>{let g;for(;d&&d!==p;)g=h(d),o(d),d=g;o(p)},J=(d,p,g,v,_,I,P,E,C)=>{p.type==="svg"?P="svg":p.type==="math"&&(P="mathml"),d==null?z(p,g,v,_,I,P,E,C):ze(d,p,_,I,P,E,C)},z=(d,p,g,v,_,I,P,E)=>{let C,b;const{props:A,shapeFlag:N,transition:M,dirs:F}=d;if(C=d.el=r(d.type,I,A&&A.is,A),N&8?l(C,d.children):N&16&&ne(d.children,C,null,v,_,Hn(d,I),P,E),F&&tt(d,null,v,"created"),q(C,d,d.scopeId,P,v),A){for(const X in A)X!=="value"&&!Kt(X)&&s(C,X,null,A[X],I,d.children,v,_,ce);"value"in A&&s(C,"value",null,A.value,I),(b=A.onVnodeBeforeMount)&&Te(b,v,d)}F&&tt(d,null,v,"beforeMount");const $=Kc(_,M);$&&M.beforeEnter(C),i(C,p,g),((b=A&&A.onVnodeMounted)||$||F)&&he(()=>{b&&Te(b,v,d),$&&M.enter(C),F&&tt(d,null,v,"mounted")},_)},q=(d,p,g,v,_)=>{if(g&&m(d,g),v)for(let I=0;I<v.length;I++)m(d,v[I]);if(_){let I=_.subTree;if(p===I){const P=_.vnode;q(d,P,P.scopeId,P.slotScopeIds,_.parent)}}},ne=(d,p,g,v,_,I,P,E,C=0)=>{for(let b=C;b<d.length;b++){const A=d[b]=E?Ge(d[b]):Ae(d[b]);x(null,A,p,g,v,_,I,P,E)}},ze=(d,p,g,v,_,I,P)=>{const E=p.el=d.el;let{patchFlag:C,dynamicChildren:b,dirs:A}=p;C|=d.patchFlag&16;const N=d.props||ee,M=p.props||ee;let F;if(g&&nt(g,!1),(F=M.onVnodeBeforeUpdate)&&Te(F,g,p,d),A&&tt(p,d,g,"beforeUpdate"),g&&nt(g,!0),b?Ce(d.dynamicChildren,b,E,g,v,Hn(p,_),I):P||W(d,p,E,null,g,v,Hn(p,_),I,!1),C>0){if(C&16)Ve(E,p,N,M,g,v,_);else if(C&2&&N.class!==M.class&&s(E,"class",null,M.class,_),C&4&&s(E,"style",N.style,M.style,_),C&8){const $=p.dynamicProps;for(let X=0;X<$.length;X++){const Q=$[X],oe=N[Q],be=M[Q];(be!==oe||Q==="value")&&s(E,Q,oe,be,_,d.children,g,v,ce)}}C&1&&d.children!==p.children&&l(E,p.children)}else!P&&b==null&&Ve(E,p,N,M,g,v,_);((F=M.onVnodeUpdated)||A)&&he(()=>{F&&Te(F,g,p,d),A&&tt(p,d,g,"updated")},v)},Ce=(d,p,g,v,_,I,P)=>{for(let E=0;E<p.length;E++){const C=d[E],b=p[E],A=C.el&&(C.type===Ue||!Lt(C,b)||C.shapeFlag&70)?f(C.el):g;x(C,b,A,null,v,_,I,P,!0)}},Ve=(d,p,g,v,_,I,P)=>{if(g!==v){if(g!==ee)for(const E in g)!Kt(E)&&!(E in v)&&s(d,E,g[E],null,P,p.children,_,I,ce);for(const E in v){if(Kt(E))continue;const C=v[E],b=g[E];C!==b&&E!=="value"&&s(d,E,b,C,P,p.children,_,I,ce)}"value"in v&&s(d,"value",g.value,v.value,P)}},et=(d,p,g,v,_,I,P,E,C)=>{const b=p.el=d?d.el:a(""),A=p.anchor=d?d.anchor:a("");let{patchFlag:N,dynamicChildren:M,slotScopeIds:F}=p;F&&(E=E?E.concat(F):F),d==null?(i(b,g,v),i(A,g,v),ne(p.children||[],g,A,_,I,P,E,C)):N>0&&N&64&&M&&d.dynamicChildren?(Ce(d.dynamicChildren,M,g,_,I,P,E),(p.key!=null||_&&p===_.subTree)&&Ks(d,p,!0)):W(d,p,g,A,_,I,P,E,C)},Oe=(d,p,g,v,_,I,P,E,C)=>{p.slotScopeIds=E,d==null?p.shapeFlag&512?_.ctx.activate(p,g,v,P,C):At(p,g,v,_,I,P,C):dt(d,p,C)},At=(d,p,g,v,_,I,P)=>{const E=d.component=Jc(d,v,_);if(Ps(d)&&(E.ctx.renderer=L),qc(E),E.asyncDep){if(_&&_.registerDep(E,ie),!d.el){const C=E.subTree=we(Xt);S(null,C,p,g)}}else ie(E,d,p,g,_,I,P)},dt=(d,p,g)=>{const v=p.component=d.component;if(sc(d,p,g))if(v.asyncDep&&!v.asyncResolved){G(v,p,g);return}else v.next=p,Yr(v.update),v.effect.dirty=!0,v.update();else p.el=d.el,v.vnode=p},ie=(d,p,g,v,_,I,P)=>{const E=()=>{if(d.isMounted){let{next:A,bu:N,u:M,parent:F,vnode:$}=d;{const mt=Us(d);if(mt){A&&(A.el=$.el,G(d,A,P)),mt.asyncDep.then(()=>{d.isUnmounted||E()});return}}let X=A,Q;nt(d,!1),A?(A.el=$.el,G(d,A,P)):A=$,N&&zn(N),(Q=A.props&&A.props.onVnodeBeforeUpdate)&&Te(Q,F,A,$),nt(d,!0);const oe=Kn(d),be=d.subTree;d.subTree=oe,x(be,oe,f(be.el),w(be),d,_,I),A.el=oe.el,X===null&&rc(d,oe.el),M&&he(M,_),(Q=A.props&&A.props.onVnodeUpdated)&&he(()=>Te(Q,F,A,$),_)}else{let A;const{el:N,props:M}=p,{bm:F,m:$,parent:X}=d,Q=fn(p);if(nt(d,!1),F&&zn(F),!Q&&(A=M&&M.onVnodeBeforeMount)&&Te(A,X,p),nt(d,!0),N&&Y){const oe=()=>{d.subTree=Kn(d),Y(N,d.subTree,d,_,null)};Q?p.type.__asyncLoader().then(()=>!d.isUnmounted&&oe()):oe()}else{const oe=d.subTree=Kn(d);x(null,oe,g,v,d,_,I),p.el=oe.el}if($&&he($,_),!Q&&(A=M&&M.onVnodeMounted)){const oe=p;he(()=>Te(A,X,oe),_)}(p.shapeFlag&256||X&&fn(X.vnode)&&X.vnode.shapeFlag&256)&&d.a&&he(d.a,_),d.isMounted=!0,p=g=v=null}},C=d.effect=new bi(E,_e,()=>Pi(b),d.scope),b=d.update=()=>{C.dirty&&C.run()};b.id=d.uid,nt(d,!0),b()},G=(d,p,g)=>{p.component=d;const v=d.vnode.props;d.vnode=p,d.next=null,jc(d,p.props,v,g),Nc(d,p.children,g),ut(),to(d),ft()},W=(d,p,g,v,_,I,P,E,C=!1)=>{const b=d&&d.children,A=d?d.shapeFlag:0,N=p.children,{patchFlag:M,shapeFlag:F}=p;if(M>0){if(M&128){De(b,N,g,v,_,I,P,E,C);return}else if(M&256){Fe(b,N,g,v,_,I,P,E,C);return}}F&8?(A&16&&ce(b,_,I),N!==b&&l(g,N)):A&16?F&16?De(b,N,g,v,_,I,P,E,C):ce(b,_,I,!0):(A&8&&l(g,""),F&16&&ne(N,g,v,_,I,P,E,C))},Fe=(d,p,g,v,_,I,P,E,C)=>{d=d||vt,p=p||vt;const b=d.length,A=p.length,N=Math.min(b,A);let M;for(M=0;M<N;M++){const F=p[M]=C?Ge(p[M]):Ae(p[M]);x(d[M],F,g,null,_,I,P,E,C)}b>A?ce(d,_,I,!0,!1,N):ne(p,g,v,_,I,P,E,C,N)},De=(d,p,g,v,_,I,P,E,C)=>{let b=0;const A=p.length;let N=d.length-1,M=A-1;for(;b<=N&&b<=M;){const F=d[b],$=p[b]=C?Ge(p[b]):Ae(p[b]);if(Lt(F,$))x(F,$,g,null,_,I,P,E,C);else break;b++}for(;b<=N&&b<=M;){const F=d[N],$=p[M]=C?Ge(p[M]):Ae(p[M]);if(Lt(F,$))x(F,$,g,null,_,I,P,E,C);else break;N--,M--}if(b>N){if(b<=M){const F=M+1,$=F<A?p[F].el:v;for(;b<=M;)x(null,p[b]=C?Ge(p[b]):Ae(p[b]),g,$,_,I,P,E,C),b++}}else if(b>M)for(;b<=N;)de(d[b],_,I,!0),b++;else{const F=b,$=b,X=new Map;for(b=$;b<=M;b++){const ye=p[b]=C?Ge(p[b]):Ae(p[b]);ye.key!=null&&X.set(ye.key,b)}let Q,oe=0;const be=M-$+1;let mt=!1,Di=0;const jt=new Array(be);for(b=0;b<be;b++)jt[b]=0;for(b=F;b<=N;b++){const ye=d[b];if(oe>=be){de(ye,_,I,!0);continue}let Re;if(ye.key!=null)Re=X.get(ye.key);else for(Q=$;Q<=M;Q++)if(jt[Q-$]===0&&Lt(ye,p[Q])){Re=Q;break}Re===void 0?de(ye,_,I,!0):(jt[Re-$]=b+1,Re>=Di?Di=Re:mt=!0,x(ye,p[Re],g,null,_,I,P,E,C),oe++)}const Wi=mt?Uc(jt):vt;for(Q=Wi.length-1,b=be-1;b>=0;b--){const ye=$+b,Re=p[ye],Zi=ye+1<A?p[ye+1].el:v;jt[b]===0?x(null,Re,g,Zi,_,I,P,E,C):mt&&(Q<0||b!==Wi[Q]?Pe(Re,g,Zi,2):Q--)}}},Pe=(d,p,g,v,_=null)=>{const{el:I,type:P,transition:E,children:C,shapeFlag:b}=d;if(b&6){Pe(d.component.subTree,p,g,v);return}if(b&128){d.suspense.move(p,g,v);return}if(b&64){P.move(d,p,g,L);return}if(P===Ue){i(I,p,g);for(let N=0;N<C.length;N++)Pe(C[N],p,g,v);i(d.anchor,p,g);return}if(P===$n){j(d,p,g);return}if(v!==2&&b&1&&E)if(v===0)E.beforeEnter(I),i(I,p,g),he(()=>E.enter(I),_);else{const{leave:N,delayLeave:M,afterLeave:F}=E,$=()=>i(I,p,g),X=()=>{N(I,()=>{$(),F&&F()})};M?M(I,$,X):X()}else i(I,p,g)},de=(d,p,g,v=!1,_=!1)=>{const{type:I,props:P,ref:E,children:C,dynamicChildren:b,shapeFlag:A,patchFlag:N,dirs:M}=d;if(E!=null&&ci(E,null,g,d,!0),A&256){p.ctx.deactivate(d);return}const F=A&1&&M,$=!fn(d);let X;if($&&(X=P&&P.onVnodeBeforeUnmount)&&Te(X,p,d),A&6)tn(d.component,g,v);else{if(A&128){d.suspense.unmount(g,v);return}F&&tt(d,null,p,"beforeUnmount"),A&64?d.type.remove(d,p,g,_,L,v):b&&(I!==Ue||N>0&&N&64)?ce(b,p,g,!1,!0):(I===Ue&&N&384||!_&&A&16)&&ce(C,p,g),v&&ht(d)}($&&(X=P&&P.onVnodeUnmounted)||F)&&he(()=>{X&&Te(X,p,d),F&&tt(d,null,p,"unmounted")},g)},ht=d=>{const{type:p,el:g,anchor:v,transition:_}=d;if(p===Ue){pt(g,v);return}if(p===$n){H(d);return}const I=()=>{o(g),_&&!_.persisted&&_.afterLeave&&_.afterLeave()};if(d.shapeFlag&1&&_&&!_.persisted){const{leave:P,delayLeave:E}=_,C=()=>P(g,I);E?E(d.el,I,C):C()}else I()},pt=(d,p)=>{let g;for(;d!==p;)g=h(d),o(d),d=g;o(p)},tn=(d,p,g)=>{const{bum:v,scope:_,update:I,subTree:P,um:E}=d;v&&zn(v),_.stop(),I&&(I.active=!1,de(P,d,p,g)),E&&he(E,p),he(()=>{d.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},ce=(d,p,g,v=!1,_=!1,I=0)=>{for(let P=I;P<d.length;P++)de(d[P],p,g,v,_)},w=d=>d.shapeFlag&6?w(d.component.subTree):d.shapeFlag&128?d.suspense.next():h(d.anchor||d.el);let T=!1;const O=(d,p,g)=>{d==null?p._vnode&&de(p._vnode,null,null,!0):x(p._vnode||null,d,p,null,null,null,g),T||(T=!0,to(),xs(),T=!1),p._vnode=d},L={p:x,um:de,m:Pe,r:ht,mt:At,mc:ne,pc:W,pbc:Ce,n:w,o:e};let Z,Y;return t&&([Z,Y]=t(L)),{render:O,hydrate:Z,createApp:Tc(O,Z)}}function Hn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function nt({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Kc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ks(e,t,n=!1){const i=e.children,o=t.children;if(K(i)&&K(o))for(let s=0;s<i.length;s++){const r=i[s];let a=o[s];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[s]=Ge(o[s]),a.el=r.el),n||Ks(r,a)),a.type===Pn&&(a.el=r.el)}}function Uc(e){const t=e.slice(),n=[0];let i,o,s,r,a;const c=e.length;for(i=0;i<c;i++){const u=e[i];if(u!==0){if(o=n[n.length-1],e[o]<u){t[i]=o,n.push(i);continue}for(s=0,r=n.length-1;s<r;)a=s+r>>1,e[n[a]]<u?s=a+1:r=a;u<e[n[s]]&&(s>0&&(t[i]=n[s-1]),n[s]=i)}}for(s=n.length,r=n[s-1];s-- >0;)n[s]=r,r=t[r];return n}function Us(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Us(t)}const Hc=e=>e.__isTeleport,Ue=Symbol.for("v-fgt"),Pn=Symbol.for("v-txt"),Xt=Symbol.for("v-cmt"),$n=Symbol.for("v-stc"),Vt=[];let Se=null;function Rn(e=!1){Vt.push(Se=e?null:[])}function $c(){Vt.pop(),Se=Vt[Vt.length-1]||null}let Jt=1;function fo(e){Jt+=e}function Hs(e){return e.dynamicChildren=Jt>0?Se||vt:null,$c(),Jt>0&&Se&&Se.push(e),e}function ji(e,t,n,i,o,s){return Hs(Vs(e,t,n,i,o,s,!0))}function Vc(e,t,n,i,o){return Hs(we(e,t,n,i,o,!0))}function ai(e){return e?e.__v_isVNode===!0:!1}function Lt(e,t){return e.type===t.type&&e.key===t.key}const Tn="__vInternal",$s=({key:e})=>e??null,hn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?se(e)||ge(e)||U(e)?{i:Me,r:e,k:t,f:!!n}:e:null);function Vs(e,t=null,n=null,i=0,o=null,s=e===Ue?0:1,r=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&$s(t),ref:t&&hn(t),scopeId:Is,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:i,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Me};return a?(Li(c,n),s&128&&e.normalize(c)):n&&(c.shapeFlag|=se(n)?8:16),Jt>0&&!r&&Se&&(c.patchFlag>0||s&6)&&c.patchFlag!==32&&Se.push(c),c}const we=Dc;function Dc(e,t=null,n=null,i=0,o=null,s=!1){if((!e||e===cc)&&(e=Xt),ai(e)){const a=It(e,t,!0);return n&&Li(a,n),Jt>0&&!s&&Se&&(a.shapeFlag&6?Se[Se.indexOf(e)]=a:Se.push(a)),a.patchFlag|=-2,a}if(ta(e)&&(e=e.__vccOpts),t){t=Wc(t);let{class:a,style:c}=t;a&&!se(a)&&(t.class=vi(a)),te(c)&&(ps(c)&&!K(c)&&(c=re({},c)),t.style=wi(c))}const r=se(e)?1:ac(e)?128:Hc(e)?64:te(e)?4:U(e)?2:0;return Vs(e,t,n,i,o,r,s,!0)}function Wc(e){return e?ps(e)||Tn in e?re({},e):e:null}function It(e,t,n=!1){const{props:i,ref:o,patchFlag:s,children:r}=e,a=t?Bc(i||{},t):i;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&$s(a),ref:t&&t.ref?n&&o?K(o)?o.concat(hn(t)):[o,hn(t)]:hn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:r,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ue?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&It(e.ssContent),ssFallback:e.ssFallback&&It(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Zc(e=" ",t=0){return we(Pn,null,e,t)}function Ae(e){return e==null||typeof e=="boolean"?we(Xt):K(e)?we(Ue,null,e.slice()):typeof e=="object"?Ge(e):we(Pn,null,String(e))}function Ge(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:It(e)}function Li(e,t){let n=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(K(t))n=16;else if(typeof t=="object")if(i&65){const o=t.default;o&&(o._c&&(o._d=!1),Li(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!(Tn in t)?t._ctx=Me:o===3&&Me&&(Me.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else U(t)?(t={default:t,_ctx:Me},n=32):(t=String(t),i&64?(n=16,t=[Zc(t)]):n=8);e.children=t,e.shapeFlag|=n}function Bc(...e){const t={};for(let n=0;n<e.length;n++){const i=e[n];for(const o in i)if(o==="class")t.class!==i.class&&(t.class=vi([t.class,i.class]));else if(o==="style")t.style=wi([t.style,i.style]);else if(bn(o)){const s=t[o],r=i[o];r&&s!==r&&!(K(s)&&s.includes(r))&&(t[o]=s?[].concat(s,r):r)}else o!==""&&(t[o]=i[o])}return t}function Te(e,t,n,i=null){Ee(e,t,7,[n,i])}const Gc=js();let Xc=0;function Jc(e,t,n){const i=e.type,o=(t?t.appContext:e.appContext)||Gc,s={uid:Xc++,vnode:e,type:i,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,scope:new Sr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ms(i,o),emitsOptions:Es(i,o),emit:null,emitted:null,propsDefaults:ee,inheritAttrs:i.inheritAttrs,ctx:ee,data:ee,props:ee,attrs:ee,slots:ee,refs:ee,setupState:ee,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=tc.bind(null,s),e.ce&&e.ce(s),s}let fe=null,vn,li;{const e=qo(),t=(n,i)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(i),s=>{o.length>1?o.forEach(r=>r(s)):o[0](s)}};vn=t("__VUE_INSTANCE_SETTERS__",n=>fe=n),li=t("__VUE_SSR_SETTERS__",n=>An=n)}const en=e=>{const t=fe;return vn(e),e.scope.on(),()=>{e.scope.off(),vn(t)}},ho=()=>{fe&&fe.scope.off(),vn(null)};function Ds(e){return e.vnode.shapeFlag&4}let An=!1;function qc(e,t=!1){t&&li(t);const{props:n,children:i}=e.vnode,o=Ds(e);Ac(e,n,o,t),Mc(e,i);const s=o?Yc(e,t):void 0;return t&&li(!1),s}function Yc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=ms(new Proxy(e.ctx,Sc));const{setup:i}=n;if(i){const o=e.setupContext=i.length>1?ea(e):null,s=en(e);ut();const r=Ye(i,e,0,[e.props,o]);if(ft(),s(),Bo(r)){if(r.then(ho,ho),t)return r.then(a=>{po(e,a,t)}).catch(a=>{In(a,e,0)});e.asyncDep=r}else po(e,r,t)}else Ws(e,t)}function po(e,t,n){U(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:te(t)&&(e.setupState=ws(t)),Ws(e,n)}let mo;function Ws(e,t,n){const i=e.type;if(!e.render){if(!t&&mo&&!i.render){const o=i.template||Ti(e).template;if(o){const{isCustomElement:s,compilerOptions:r}=e.appContext.config,{delimiters:a,compilerOptions:c}=i,u=re(re({isCustomElement:s,delimiters:a},r),c);i.render=mo(o,u)}}e.render=i.render||_e}{const o=en(e);ut();try{Ec(e)}finally{ft(),o()}}}function Qc(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,n){return me(e,"get","$attrs"),t[n]}}))}function ea(e){const t=n=>{e.exposed=n||{}};return{get attrs(){return Qc(e)},slots:e.slots,emit:e.emit,expose:t}}function Mi(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(ws(ms(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ht)return Ht[n](e)},has(t,n){return n in t||n in Ht}}))}function ta(e){return U(e)&&"__vccOpts"in e}const xe=(e,t)=>Zr(e,t,An);function Zs(e,t,n){const i=arguments.length;return i===2?te(t)&&!K(t)?ai(t)?we(e,null,[t]):we(e,t):we(e,null,t):(i>3?n=Array.prototype.slice.call(arguments,2):i===3&&ai(n)&&(n=[n]),we(e,t,n))}const na="3.4.21";/**
* @vue/runtime-dom v3.4.21
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const ia="http://www.w3.org/2000/svg",oa="http://www.w3.org/1998/Math/MathML",Xe=typeof document<"u"?document:null,go=Xe&&Xe.createElement("template"),sa={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,i)=>{const o=t==="svg"?Xe.createElementNS(ia,e):t==="mathml"?Xe.createElementNS(oa,e):Xe.createElement(e,n?{is:n}:void 0);return e==="select"&&i&&i.multiple!=null&&o.setAttribute("multiple",i.multiple),o},createText:e=>Xe.createTextNode(e),createComment:e=>Xe.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Xe.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,i,o,s){const r=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===s||!(o=o.nextSibling)););else{go.innerHTML=i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e;const a=go.content;if(i==="svg"||i==="mathml"){const c=a.firstChild;for(;c.firstChild;)a.appendChild(c.firstChild);a.removeChild(c)}t.insertBefore(a,n)}return[r?r.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},ra=Symbol("_vtc");function ca(e,t,n){const i=e[ra];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const yo=Symbol("_vod"),aa=Symbol("_vsh"),la=Symbol(""),ua=/(^|;)\s*display\s*:/;function fa(e,t,n){const i=e.style,o=se(n);let s=!1;if(n&&!o){if(t)if(se(t))for(const r of t.split(";")){const a=r.slice(0,r.indexOf(":")).trim();n[a]==null&&pn(i,a,"")}else for(const r in t)n[r]==null&&pn(i,r,"");for(const r in n)r==="display"&&(s=!0),pn(i,r,n[r])}else if(o){if(t!==n){const r=i[la];r&&(n+=";"+r),i.cssText=n,s=ua.test(n)}}else t&&e.removeAttribute("style");yo in e&&(e[yo]=s?i.display:"",e[aa]&&(i.display="none"))}const _o=/\s*!important$/;function pn(e,t,n){if(K(n))n.forEach(i=>pn(e,t,i));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const i=da(e,t);_o.test(n)?e.setProperty(Rt(i),n.replace(_o,""),"important"):e[i]=n}}const wo=["Webkit","Moz","ms"],Vn={};function da(e,t){const n=Vn[t];if(n)return n;let i=St(t);if(i!=="filter"&&i in e)return Vn[t]=i;i=Jo(i);for(let o=0;o<wo.length;o++){const s=wo[o]+i;if(s in e)return Vn[t]=s}return t}const vo="http://www.w3.org/1999/xlink";function ha(e,t,n,i,o){if(i&&t.startsWith("xlink:"))n==null?e.removeAttributeNS(vo,t.slice(6,t.length)):e.setAttributeNS(vo,t,n);else{const s=xr(t);n==null||s&&!Yo(n)?e.removeAttribute(t):e.setAttribute(t,s?"":n)}}function pa(e,t,n,i,o,s,r){if(t==="innerHTML"||t==="textContent"){i&&r(i,o,s),e[t]=n??"";return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const u=a==="OPTION"?e.getAttribute("value")||"":e.value,l=n??"";(u!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let c=!1;if(n===""||n==null){const u=typeof e[t];u==="boolean"?n=Yo(n):n==null&&u==="string"?(n="",c=!0):u==="number"&&(n=0,c=!0)}try{e[t]=n}catch{}c&&e.removeAttribute(t)}function ma(e,t,n,i){e.addEventListener(t,n,i)}function ga(e,t,n,i){e.removeEventListener(t,n,i)}const bo=Symbol("_vei");function ya(e,t,n,i,o=null){const s=e[bo]||(e[bo]={}),r=s[t];if(i&&r)r.value=i;else{const[a,c]=_a(t);if(i){const u=s[t]=ba(i,o);ma(e,a,u,c)}else r&&(ga(e,a,r,c),s[t]=void 0)}}const ko=/(?:Once|Passive|Capture)$/;function _a(e){let t;if(ko.test(e)){t={};let i;for(;i=e.match(ko);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Rt(e.slice(2)),t]}let Dn=0;const wa=Promise.resolve(),va=()=>Dn||(wa.then(()=>Dn=0),Dn=Date.now());function ba(e,t){const n=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=n.attached)return;Ee(ka(i,n.value),t,5,[i])};return n.value=e,n.attached=va(),n}function ka(e,t){if(K(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(i=>o=>!o._stopped&&i&&i(o))}else return t}const xo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,xa=(e,t,n,i,o,s,r,a,c)=>{const u=o==="svg";t==="class"?ca(e,i,u):t==="style"?fa(e,n,i):bn(t)?gi(t)||ya(e,t,n,i,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Sa(e,t,i,u))?pa(e,t,i,s,r,a,c):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),ha(e,t,i,u))};function Sa(e,t,n,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&xo(t)&&U(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return xo(t)&&se(n)?!1:t in e}const Ea=re({patchProp:xa},sa);let So;function Ia(){return So||(So=zc(Ea))}const Ca=(...e)=>{const t=Ia().createApp(...e),{mount:n}=t;return t.mount=i=>{const o=Pa(i);if(!o)return;const s=t._component;!U(s)&&!s.render&&!s.template&&(s.template=o.innerHTML),o.innerHTML="";const r=n(o,!1,Oa(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),r},t};function Oa(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Pa(e){return se(e)?document.querySelector(e):e}const Bs=Symbol("$auth0");function je(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function"){var o=0;for(i=Object.getOwnPropertySymbols(e);o<i.length;o++)t.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(e,i[o])&&(n[i[o]]=e[i[o]])}return n}var wt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ni(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function zi(e,t){return e(t={exports:{}},t.exports),t.exports}var ot=zi(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function i(){var o=this;this.locked=new Map,this.addToLocked=function(s,r){var a=o.locked.get(s);a===void 0?r===void 0?o.locked.set(s,[]):o.locked.set(s,[r]):r!==void 0&&(a.unshift(r),o.locked.set(s,a))},this.isLocked=function(s){return o.locked.has(s)},this.lock=function(s){return new Promise(function(r,a){o.isLocked(s)?o.addToLocked(s,r):(o.addToLocked(s),r())})},this.unlock=function(s){var r=o.locked.get(s);if(r!==void 0&&r.length!==0){var a=r.pop();o.locked.set(s,r),a!==void 0&&setTimeout(a,0)}else o.locked.delete(s)}}return i.getInstance=function(){return i.instance===void 0&&(i.instance=new i),i.instance},i}();t.default=function(){return n.getInstance()}});Ni(ot);var Ra=Ni(zi(function(e,t){var n=wt&&wt.__awaiter||function(c,u,l,f){return new(l||(l=Promise))(function(h,m){function y(S){try{R(f.next(S))}catch(k){m(k)}}function x(S){try{R(f.throw(S))}catch(k){m(k)}}function R(S){S.done?h(S.value):new l(function(k){k(S.value)}).then(y,x)}R((f=f.apply(c,u||[])).next())})},i=wt&&wt.__generator||function(c,u){var l,f,h,m,y={label:0,sent:function(){if(1&h[0])throw h[1];return h[1]},trys:[],ops:[]};return m={next:x(0),throw:x(1),return:x(2)},typeof Symbol=="function"&&(m[Symbol.iterator]=function(){return this}),m;function x(R){return function(S){return function(k){if(l)throw new TypeError("Generator is already executing.");for(;y;)try{if(l=1,f&&(h=2&k[0]?f.return:k[0]?f.throw||((h=f.return)&&h.call(f),0):f.next)&&!(h=h.call(f,k[1])).done)return h;switch(f=0,h&&(k=[2&k[0],h.value]),k[0]){case 0:case 1:h=k;break;case 4:return y.label++,{value:k[1],done:!1};case 5:y.label++,f=k[1],k=[0];continue;case 7:k=y.ops.pop(),y.trys.pop();continue;default:if(!((h=(h=y.trys).length>0&&h[h.length-1])||k[0]!==6&&k[0]!==2)){y=0;continue}if(k[0]===3&&(!h||k[1]>h[0]&&k[1]<h[3])){y.label=k[1];break}if(k[0]===6&&y.label<h[1]){y.label=h[1],h=k;break}if(h&&y.label<h[2]){y.label=h[2],y.ops.push(k);break}h[2]&&y.ops.pop(),y.trys.pop();continue}k=u.call(c,y)}catch(j){k=[6,j],f=0}finally{l=h=0}if(5&k[0])throw k[1];return{value:k[0]?k[1]:void 0,done:!0}}([R,S])}}};Object.defineProperty(t,"__esModule",{value:!0});var o="browser-tabs-lock-key";function s(c){return new Promise(function(u){return setTimeout(u,c)})}function r(c){for(var u="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz",l="",f=0;f<c;f++)l+=u[Math.floor(Math.random()*u.length)];return l}var a=function(){function c(){this.acquiredIatSet=new Set,this.id=Date.now().toString()+r(15),this.acquireLock=this.acquireLock.bind(this),this.releaseLock=this.releaseLock.bind(this),this.releaseLock__private__=this.releaseLock__private__.bind(this),this.waitForSomethingToChange=this.waitForSomethingToChange.bind(this),this.refreshLockWhileAcquired=this.refreshLockWhileAcquired.bind(this),c.waiters===void 0&&(c.waiters=[])}return c.prototype.acquireLock=function(u,l){return l===void 0&&(l=5e3),n(this,void 0,void 0,function(){var f,h,m,y,x,R;return i(this,function(S){switch(S.label){case 0:f=Date.now()+r(4),h=Date.now()+l,m=o+"-"+u,y=window.localStorage,S.label=1;case 1:return Date.now()<h?[4,s(30)]:[3,8];case 2:return S.sent(),y.getItem(m)!==null?[3,5]:(x=this.id+"-"+u+"-"+f,[4,s(Math.floor(25*Math.random()))]);case 3:return S.sent(),y.setItem(m,JSON.stringify({id:this.id,iat:f,timeoutKey:x,timeAcquired:Date.now(),timeRefreshed:Date.now()})),[4,s(30)];case 4:return S.sent(),(R=y.getItem(m))!==null&&(R=JSON.parse(R)).id===this.id&&R.iat===f?(this.acquiredIatSet.add(f),this.refreshLockWhileAcquired(m,f),[2,!0]):[3,7];case 5:return c.lockCorrector(),[4,this.waitForSomethingToChange(h)];case 6:S.sent(),S.label=7;case 7:return f=Date.now()+r(4),[3,1];case 8:return[2,!1]}})})},c.prototype.refreshLockWhileAcquired=function(u,l){return n(this,void 0,void 0,function(){var f=this;return i(this,function(h){return setTimeout(function(){return n(f,void 0,void 0,function(){var m,y;return i(this,function(x){switch(x.label){case 0:return[4,ot.default().lock(l)];case 1:return x.sent(),this.acquiredIatSet.has(l)?(m=window.localStorage,(y=m.getItem(u))===null?(ot.default().unlock(l),[2]):((y=JSON.parse(y)).timeRefreshed=Date.now(),m.setItem(u,JSON.stringify(y)),ot.default().unlock(l),this.refreshLockWhileAcquired(u,l),[2])):(ot.default().unlock(l),[2])}})})},1e3),[2]})})},c.prototype.waitForSomethingToChange=function(u){return n(this,void 0,void 0,function(){return i(this,function(l){switch(l.label){case 0:return[4,new Promise(function(f){var h=!1,m=Date.now(),y=!1;function x(){if(y||(window.removeEventListener("storage",x),c.removeFromWaiting(x),clearTimeout(R),y=!0),!h){h=!0;var S=50-(Date.now()-m);S>0?setTimeout(f,S):f()}}window.addEventListener("storage",x),c.addToWaiting(x);var R=setTimeout(x,Math.max(0,u-Date.now()))})];case 1:return l.sent(),[2]}})})},c.addToWaiting=function(u){this.removeFromWaiting(u),c.waiters!==void 0&&c.waiters.push(u)},c.removeFromWaiting=function(u){c.waiters!==void 0&&(c.waiters=c.waiters.filter(function(l){return l!==u}))},c.notifyWaiters=function(){c.waiters!==void 0&&c.waiters.slice().forEach(function(u){return u()})},c.prototype.releaseLock=function(u){return n(this,void 0,void 0,function(){return i(this,function(l){switch(l.label){case 0:return[4,this.releaseLock__private__(u)];case 1:return[2,l.sent()]}})})},c.prototype.releaseLock__private__=function(u){return n(this,void 0,void 0,function(){var l,f,h;return i(this,function(m){switch(m.label){case 0:return l=window.localStorage,f=o+"-"+u,(h=l.getItem(f))===null?[2]:(h=JSON.parse(h)).id!==this.id?[3,2]:[4,ot.default().lock(h.iat)];case 1:m.sent(),this.acquiredIatSet.delete(h.iat),l.removeItem(f),ot.default().unlock(h.iat),c.notifyWaiters(),m.label=2;case 2:return[2]}})})},c.lockCorrector=function(){for(var u=Date.now()-5e3,l=window.localStorage,f=Object.keys(l),h=!1,m=0;m<f.length;m++){var y=f[m];if(y.includes(o)){var x=l.getItem(y);x!==null&&((x=JSON.parse(x)).timeRefreshed===void 0&&x.timeAcquired<u||x.timeRefreshed!==void 0&&x.timeRefreshed<u)&&(l.removeItem(y),h=!0)}}h&&c.notifyWaiters()},c.waiters=void 0,c}();t.default=a}));const Ta={timeoutInSeconds:60},Gs={name:"auth0-spa-js",version:"2.1.2"},Xs=()=>Date.now();class ae extends Error{constructor(t,n){super(n),this.error=t,this.error_description=n,Object.setPrototypeOf(this,ae.prototype)}static fromPayload({error:t,error_description:n}){return new ae(t,n)}}class Fi extends ae{constructor(t,n,i,o=null){super(t,n),this.state=i,this.appState=o,Object.setPrototypeOf(this,Fi.prototype)}}class qt extends ae{constructor(){super("timeout","Timeout"),Object.setPrototypeOf(this,qt.prototype)}}class Ki extends qt{constructor(t){super(),this.popup=t,Object.setPrototypeOf(this,Ki.prototype)}}class Ui extends ae{constructor(t){super("cancelled","Popup closed"),this.popup=t,Object.setPrototypeOf(this,Ui.prototype)}}class Hi extends ae{constructor(t,n,i){super(t,n),this.mfa_token=i,Object.setPrototypeOf(this,Hi.prototype)}}class jn extends ae{constructor(t,n){super("missing_refresh_token",`Missing Refresh Token (audience: '${Eo(t,["default"])}', scope: '${Eo(n)}')`),this.audience=t,this.scope=n,Object.setPrototypeOf(this,jn.prototype)}}function Eo(e,t=[]){return e&&!t.includes(e)?e:""}const mn=()=>window.crypto,Wn=()=>{const e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.";let t="";return Array.from(mn().getRandomValues(new Uint8Array(43))).forEach(n=>t+=e[n%e.length]),t},Io=e=>btoa(e),ui=e=>{var{clientId:t}=e,n=je(e,["clientId"]);return new URLSearchParams((i=>Object.keys(i).filter(o=>i[o]!==void 0).reduce((o,s)=>Object.assign(Object.assign({},o),{[s]:i[s]}),{}))(Object.assign({client_id:t},n))).toString()},Co=e=>(t=>decodeURIComponent(atob(t).split("").map(n=>"%"+("00"+n.charCodeAt(0).toString(16)).slice(-2)).join("")))(e.replace(/_/g,"/").replace(/-/g,"+")),Aa=async(e,t)=>{const n=await fetch(e,t);return{ok:n.ok,json:await n.json()}},ja=async(e,t,n,i,o,s,r=1e4)=>o?(async(a,c,u,l,f,h,m)=>{return y={auth:{audience:c,scope:u},timeout:f,fetchUrl:a,fetchOptions:l,useFormData:m},x=h,new Promise(function(R,S){const k=new MessageChannel;k.port1.onmessage=function(j){j.data.error?S(new Error(j.data.error)):R(j.data),k.port1.close()},x.postMessage(y,[k.port2])});var y,x})(e,t,n,i,r,o,s):(async(a,c,u)=>{const l=new AbortController;let f;return c.signal=l.signal,Promise.race([Aa(a,c),new Promise((h,m)=>{f=setTimeout(()=>{l.abort(),m(new Error("Timeout when executing 'fetch'"))},u)})]).finally(()=>{clearTimeout(f)})})(e,i,r);async function La(e,t){var{baseUrl:n,timeout:i,audience:o,scope:s,auth0Client:r,useFormData:a}=e,c=je(e,["baseUrl","timeout","audience","scope","auth0Client","useFormData"]);const u=a?ui(c):JSON.stringify(c);return await async function(l,f,h,m,y,x,R){let S,k=null;for(let ne=0;ne<3;ne++)try{S=await ja(l,h,m,y,x,R,f),k=null;break}catch(ze){k=ze}if(k)throw k;const j=S.json,{error:H,error_description:J}=j,z=je(j,["error","error_description"]),{ok:q}=S;if(!q){const ne=J||`HTTP error. Unable to fetch ${l}`;throw H==="mfa_required"?new Hi(H,ne,z.mfa_token):H==="missing_refresh_token"?new jn(h,m):new ae(H||"request_error",ne)}return z}(`${n}/oauth/token`,i,o||"default",s,{method:"POST",body:u,headers:{"Content-Type":a?"application/x-www-form-urlencoded":"application/json","Auth0-Client":btoa(JSON.stringify(r||Gs))}},t,a)}const ln=(...e)=>{return(t=e.filter(Boolean).join(" ").trim().split(/\s+/),Array.from(new Set(t))).join(" ");var t};class Ne{constructor(t,n="@@auth0spajs@@",i){this.prefix=n,this.suffix=i,this.clientId=t.clientId,this.scope=t.scope,this.audience=t.audience}toKey(){return[this.prefix,this.clientId,this.audience,this.scope,this.suffix].filter(Boolean).join("::")}static fromKey(t){const[n,i,o,s]=t.split("::");return new Ne({clientId:i,scope:s,audience:o},n)}static fromCacheEntry(t){const{scope:n,audience:i,client_id:o}=t;return new Ne({scope:n,audience:i,clientId:o})}}class Ma{set(t,n){localStorage.setItem(t,JSON.stringify(n))}get(t){const n=window.localStorage.getItem(t);if(n)try{return JSON.parse(n)}catch{return}}remove(t){localStorage.removeItem(t)}allKeys(){return Object.keys(window.localStorage).filter(t=>t.startsWith("@@auth0spajs@@"))}}class Js{constructor(){this.enclosedCache=function(){let t={};return{set(n,i){t[n]=i},get(n){const i=t[n];if(i)return i},remove(n){delete t[n]},allKeys:()=>Object.keys(t)}}()}}class Na{constructor(t,n,i){this.cache=t,this.keyManifest=n,this.nowProvider=i||Xs}async setIdToken(t,n,i){var o;const s=this.getIdTokenCacheKey(t);await this.cache.set(s,{id_token:n,decodedToken:i}),await((o=this.keyManifest)===null||o===void 0?void 0:o.add(s))}async getIdToken(t){const n=await this.cache.get(this.getIdTokenCacheKey(t.clientId));if(!n&&t.scope&&t.audience){const i=await this.get(t);return!i||!i.id_token||!i.decodedToken?void 0:{id_token:i.id_token,decodedToken:i.decodedToken}}if(n)return{id_token:n.id_token,decodedToken:n.decodedToken}}async get(t,n=0){var i;let o=await this.cache.get(t.toKey());if(!o){const a=await this.getCacheKeys();if(!a)return;const c=this.matchExistingCacheKey(t,a);c&&(o=await this.cache.get(c))}if(!o)return;const s=await this.nowProvider(),r=Math.floor(s/1e3);return o.expiresAt-n<r?o.body.refresh_token?(o.body={refresh_token:o.body.refresh_token},await this.cache.set(t.toKey(),o),o.body):(await this.cache.remove(t.toKey()),void await((i=this.keyManifest)===null||i===void 0?void 0:i.remove(t.toKey()))):o.body}async set(t){var n;const i=new Ne({clientId:t.client_id,scope:t.scope,audience:t.audience}),o=await this.wrapCacheEntry(t);await this.cache.set(i.toKey(),o),await((n=this.keyManifest)===null||n===void 0?void 0:n.add(i.toKey()))}async clear(t){var n;const i=await this.getCacheKeys();i&&(await i.filter(o=>!t||o.includes(t)).reduce(async(o,s)=>{await o,await this.cache.remove(s)},Promise.resolve()),await((n=this.keyManifest)===null||n===void 0?void 0:n.clear()))}async wrapCacheEntry(t){const n=await this.nowProvider();return{body:t,expiresAt:Math.floor(n/1e3)+t.expires_in}}async getCacheKeys(){var t;return this.keyManifest?(t=await this.keyManifest.get())===null||t===void 0?void 0:t.keys:this.cache.allKeys?this.cache.allKeys():void 0}getIdTokenCacheKey(t){return new Ne({clientId:t},"@@auth0spajs@@","@@user@@").toKey()}matchExistingCacheKey(t,n){return n.filter(i=>{var o;const s=Ne.fromKey(i),r=new Set(s.scope&&s.scope.split(" ")),a=((o=t.scope)===null||o===void 0?void 0:o.split(" "))||[],c=s.scope&&a.reduce((u,l)=>u&&r.has(l),!0);return s.prefix==="@@auth0spajs@@"&&s.clientId===t.clientId&&s.audience===t.audience&&c})[0]}}class za{constructor(t,n,i){this.storage=t,this.clientId=n,this.cookieDomain=i,this.storageKey=`a0.spajs.txs.${this.clientId}`}create(t){this.storage.save(this.storageKey,t,{daysUntilExpire:1,cookieDomain:this.cookieDomain})}get(){return this.storage.get(this.storageKey)}remove(){this.storage.remove(this.storageKey,{cookieDomain:this.cookieDomain})}}const Mt=e=>typeof e=="number",Fa=["iss","aud","exp","nbf","iat","jti","azp","nonce","auth_time","at_hash","c_hash","acr","amr","sub_jwk","cnf","sip_from_tag","sip_date","sip_callid","sip_cseq_num","sip_via_branch","orig","dest","mky","events","toe","txn","rph","sid","vot","vtm"];var rt=zi(function(e,t){var n=wt&&wt.__assign||function(){return n=Object.assign||function(c){for(var u,l=1,f=arguments.length;l<f;l++)for(var h in u=arguments[l])Object.prototype.hasOwnProperty.call(u,h)&&(c[h]=u[h]);return c},n.apply(this,arguments)};function i(c,u){if(!u)return"";var l="; "+c;return u===!0?l:l+"="+u}function o(c,u,l){return encodeURIComponent(c).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/\(/g,"%28").replace(/\)/g,"%29")+"="+encodeURIComponent(u).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent)+function(f){if(typeof f.expires=="number"){var h=new Date;h.setMilliseconds(h.getMilliseconds()+864e5*f.expires),f.expires=h}return i("Expires",f.expires?f.expires.toUTCString():"")+i("Domain",f.domain)+i("Path",f.path)+i("Secure",f.secure)+i("SameSite",f.sameSite)}(l)}function s(c){for(var u={},l=c?c.split("; "):[],f=/(%[\dA-F]{2})+/gi,h=0;h<l.length;h++){var m=l[h].split("="),y=m.slice(1).join("=");y.charAt(0)==='"'&&(y=y.slice(1,-1));try{u[m[0].replace(f,decodeURIComponent)]=y.replace(f,decodeURIComponent)}catch{}}return u}function r(){return s(document.cookie)}function a(c,u,l){document.cookie=o(c,u,n({path:"/"},l))}t.__esModule=!0,t.encode=o,t.parse=s,t.getAll=r,t.get=function(c){return r()[c]},t.set=a,t.remove=function(c,u){a(c,"",n(n({},u),{expires:-1}))}});Ni(rt),rt.encode,rt.parse,rt.getAll;var Ka=rt.get,qs=rt.set,Ys=rt.remove;const gt={get(e){const t=Ka(e);if(t!==void 0)return JSON.parse(t)},save(e,t,n){let i={};window.location.protocol==="https:"&&(i={secure:!0,sameSite:"none"}),n!=null&&n.daysUntilExpire&&(i.expires=n.daysUntilExpire),n!=null&&n.cookieDomain&&(i.domain=n.cookieDomain),qs(e,JSON.stringify(t),i)},remove(e,t){let n={};t!=null&&t.cookieDomain&&(n.domain=t.cookieDomain),Ys(e,n)}},Ua={get:e=>gt.get(e)||gt.get(`_legacy_${e}`),save(e,t,n){let i={};window.location.protocol==="https:"&&(i={secure:!0}),n!=null&&n.daysUntilExpire&&(i.expires=n.daysUntilExpire),n!=null&&n.cookieDomain&&(i.domain=n.cookieDomain),qs(`_legacy_${e}`,JSON.stringify(t),i),gt.save(e,t,n)},remove(e,t){let n={};t!=null&&t.cookieDomain&&(n.domain=t.cookieDomain),Ys(e,n),gt.remove(e,t),gt.remove(`_legacy_${e}`,t)}},Ha={get(e){if(typeof sessionStorage>"u")return;const t=sessionStorage.getItem(e);return t!=null?JSON.parse(t):void 0},save(e,t){sessionStorage.setItem(e,JSON.stringify(t))},remove(e){sessionStorage.removeItem(e)}};var Zn,$a=function(e){return Zn=Zn||function(t,n,i){var o=n===void 0?null:n,s=function(u,l){var f=atob(u);if(l){for(var h=new Uint8Array(f.length),m=0,y=f.length;m<y;++m)h[m]=f.charCodeAt(m);return String.fromCharCode.apply(null,new Uint16Array(h.buffer))}return f}(t,i!==void 0&&i),r=s.indexOf(`
`,10)+1,a=s.substring(r)+(o?"//# sourceMappingURL="+o:""),c=new Blob([a],{type:"application/javascript"});return URL.createObjectURL(c)}("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",null,!1),new Worker(Zn,e)};const Bn={};class Va{constructor(t,n){this.cache=t,this.clientId=n,this.manifestKey=this.createManifestKeyFrom(this.clientId)}async add(t){var n;const i=new Set(((n=await this.cache.get(this.manifestKey))===null||n===void 0?void 0:n.keys)||[]);i.add(t),await this.cache.set(this.manifestKey,{keys:[...i]})}async remove(t){const n=await this.cache.get(this.manifestKey);if(n){const i=new Set(n.keys);return i.delete(t),i.size>0?await this.cache.set(this.manifestKey,{keys:[...i]}):await this.cache.remove(this.manifestKey)}}get(){return this.cache.get(this.manifestKey)}clear(){return this.cache.remove(this.manifestKey)}createManifestKeyFrom(t){return`@@auth0spajs@@::${t}`}}const Da={memory:()=>new Js().enclosedCache,localstorage:()=>new Ma},Oo=e=>Da[e],Po=e=>{const{openUrl:t,onRedirect:n}=e,i=je(e,["openUrl","onRedirect"]);return Object.assign(Object.assign({},i),{openUrl:t===!1||t?t:n})},Gn=new Ra;class Wa{constructor(t){let n,i;if(this.userCache=new Js().enclosedCache,this.defaultOptions={authorizationParams:{scope:"openid profile email"},useRefreshTokensFallback:!1,useFormData:!0},this._releaseLockOnPageHide=async()=>{await Gn.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)},this.options=Object.assign(Object.assign(Object.assign({},this.defaultOptions),t),{authorizationParams:Object.assign(Object.assign({},this.defaultOptions.authorizationParams),t.authorizationParams)}),typeof window<"u"&&(()=>{if(!mn())throw new Error("For security reasons, `window.crypto` is required to run `auth0-spa-js`.");if(mn().subtle===void 0)throw new Error(`
      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/master/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.
    `)})(),t.cache&&t.cacheLocation&&console.warn("Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`."),t.cache)i=t.cache;else{if(n=t.cacheLocation||"memory",!Oo(n))throw new Error(`Invalid cache location "${n}"`);i=Oo(n)()}this.httpTimeoutMs=t.httpTimeoutInSeconds?1e3*t.httpTimeoutInSeconds:1e4,this.cookieStorage=t.legacySameSiteCookie===!1?gt:Ua,this.orgHintCookieName=`auth0.${this.options.clientId}.organization_hint`,this.isAuthenticatedCookieName=(r=>`auth0.${this.options.clientId}.is.authenticated`)(),this.sessionCheckExpiryDays=t.sessionCheckExpiryDays||1;const o=t.useCookiesForTransactions?this.cookieStorage:Ha;var s;this.scope=ln("openid",this.options.authorizationParams.scope,this.options.useRefreshTokens?"offline_access":""),this.transactionManager=new za(o,this.options.clientId,this.options.cookieDomain),this.nowProvider=this.options.nowProvider||Xs,this.cacheManager=new Na(i,i.allKeys?void 0:new Va(i,this.options.clientId),this.nowProvider),this.domainUrl=(s=this.options.domain,/^https?:\/\//.test(s)?s:`https://${s}`),this.tokenIssuer=((r,a)=>r?r.startsWith("https://")?r:`https://${r}/`:`${a}/`)(this.options.issuer,this.domainUrl),typeof window<"u"&&window.Worker&&this.options.useRefreshTokens&&n==="memory"&&(this.worker=new $a)}_url(t){const n=encodeURIComponent(btoa(JSON.stringify(this.options.auth0Client||Gs)));return`${this.domainUrl}${t}&auth0Client=${n}`}_authorizeUrl(t){return this._url(`/authorize?${ui(t)}`)}async _verifyIdToken(t,n,i){const o=await this.nowProvider();return(r=>{if(!r.id_token)throw new Error("ID token is required but missing");const a=(f=>{const h=f.split("."),[m,y,x]=h;if(h.length!==3||!m||!y||!x)throw new Error("ID token could not be decoded");const R=JSON.parse(Co(y)),S={__raw:f},k={};return Object.keys(R).forEach(j=>{S[j]=R[j],Fa.includes(j)||(k[j]=R[j])}),{encoded:{header:m,payload:y,signature:x},header:JSON.parse(Co(m)),claims:S,user:k}})(r.id_token);if(!a.claims.iss)throw new Error("Issuer (iss) claim must be a string present in the ID token");if(a.claims.iss!==r.iss)throw new Error(`Issuer (iss) claim mismatch in the ID token; expected "${r.iss}", found "${a.claims.iss}"`);if(!a.user.sub)throw new Error("Subject (sub) claim must be a string present in the ID token");if(a.header.alg!=="RS256")throw new Error(`Signature algorithm of "${a.header.alg}" is not supported. Expected the ID token to be signed with "RS256".`);if(!a.claims.aud||typeof a.claims.aud!="string"&&!Array.isArray(a.claims.aud))throw new Error("Audience (aud) claim must be a string or array of strings present in the ID token");if(Array.isArray(a.claims.aud)){if(!a.claims.aud.includes(r.aud))throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${r.aud}" but was not one of "${a.claims.aud.join(", ")}"`);if(a.claims.aud.length>1){if(!a.claims.azp)throw new Error("Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values");if(a.claims.azp!==r.aud)throw new Error(`Authorized Party (azp) claim mismatch in the ID token; expected "${r.aud}", found "${a.claims.azp}"`)}}else if(a.claims.aud!==r.aud)throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${r.aud}" but found "${a.claims.aud}"`);if(r.nonce){if(!a.claims.nonce)throw new Error("Nonce (nonce) claim must be a string present in the ID token");if(a.claims.nonce!==r.nonce)throw new Error(`Nonce (nonce) claim mismatch in the ID token; expected "${r.nonce}", found "${a.claims.nonce}"`)}if(r.max_age&&!Mt(a.claims.auth_time))throw new Error("Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified");if(a.claims.exp==null||!Mt(a.claims.exp))throw new Error("Expiration Time (exp) claim must be a number present in the ID token");if(!Mt(a.claims.iat))throw new Error("Issued At (iat) claim must be a number present in the ID token");const c=r.leeway||60,u=new Date(r.now||Date.now()),l=new Date(0);if(l.setUTCSeconds(a.claims.exp+c),u>l)throw new Error(`Expiration Time (exp) claim error in the ID token; current time (${u}) is after expiration time (${l})`);if(a.claims.nbf!=null&&Mt(a.claims.nbf)){const f=new Date(0);if(f.setUTCSeconds(a.claims.nbf-c),u<f)throw new Error(`Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${u}) is before ${f}`)}if(a.claims.auth_time!=null&&Mt(a.claims.auth_time)){const f=new Date(0);if(f.setUTCSeconds(parseInt(a.claims.auth_time)+r.max_age+c),u>f)throw new Error(`Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${u}) is after last auth at ${f}`)}if(r.organization){const f=r.organization.trim();if(f.startsWith("org_")){const h=f;if(!a.claims.org_id)throw new Error("Organization ID (org_id) claim must be a string present in the ID token");if(h!==a.claims.org_id)throw new Error(`Organization ID (org_id) claim mismatch in the ID token; expected "${h}", found "${a.claims.org_id}"`)}else{const h=f.toLowerCase();if(!a.claims.org_name)throw new Error("Organization Name (org_name) claim must be a string present in the ID token");if(h!==a.claims.org_name)throw new Error(`Organization Name (org_name) claim mismatch in the ID token; expected "${h}", found "${a.claims.org_name}"`)}}return a})({iss:this.tokenIssuer,aud:this.options.clientId,id_token:t,nonce:n,organization:i,leeway:this.options.leeway,max_age:(s=this.options.authorizationParams.max_age,typeof s!="string"?s:parseInt(s,10)||void 0),now:o});var s}_processOrgHint(t){t?this.cookieStorage.save(this.orgHintCookieName,t,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}):this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain})}async _prepareAuthorizeUrl(t,n,i){const o=Io(Wn()),s=Io(Wn()),r=Wn(),a=(l=>{const f=new Uint8Array(l);return(h=>{const m={"+":"-","/":"_","=":""};return h.replace(/[+/=]/g,y=>m[y])})(window.btoa(String.fromCharCode(...Array.from(f))))})(await(async l=>await mn().subtle.digest({name:"SHA-256"},new TextEncoder().encode(l)))(r)),c=((l,f,h,m,y,x,R,S)=>Object.assign(Object.assign(Object.assign({client_id:l.clientId},l.authorizationParams),h),{scope:ln(f,h.scope),response_type:"code",response_mode:S||"query",state:m,nonce:y,redirect_uri:R||l.authorizationParams.redirect_uri,code_challenge:x,code_challenge_method:"S256"}))(this.options,this.scope,t,o,s,a,t.redirect_uri||this.options.authorizationParams.redirect_uri||i,n==null?void 0:n.response_mode),u=this._authorizeUrl(c);return{nonce:s,code_verifier:r,scope:c.scope,audience:c.audience||"default",redirect_uri:c.redirect_uri,state:o,url:u}}async loginWithPopup(t,n){var i;if(t=t||{},!(n=n||{}).popup&&(n.popup=(a=>{const c=window.screenX+(window.innerWidth-400)/2,u=window.screenY+(window.innerHeight-600)/2;return window.open("","auth0:authorize:popup",`left=${c},top=${u},width=400,height=600,resizable,scrollbars=yes,status=1`)})(),!n.popup))throw new Error("Unable to open a popup for loginWithPopup - window.open returned `null`");const o=await this._prepareAuthorizeUrl(t.authorizationParams||{},{response_mode:"web_message"},window.location.origin);n.popup.location.href=o.url;const s=await(a=>new Promise((c,u)=>{let l;const f=setInterval(()=>{a.popup&&a.popup.closed&&(clearInterval(f),clearTimeout(h),window.removeEventListener("message",l,!1),u(new Ui(a.popup)))},1e3),h=setTimeout(()=>{clearInterval(f),u(new Ki(a.popup)),window.removeEventListener("message",l,!1)},1e3*(a.timeoutInSeconds||60));l=function(m){if(m.data&&m.data.type==="authorization_response"){if(clearTimeout(h),clearInterval(f),window.removeEventListener("message",l,!1),a.popup.close(),m.data.response.error)return u(ae.fromPayload(m.data.response));c(m.data.response)}},window.addEventListener("message",l)}))(Object.assign(Object.assign({},n),{timeoutInSeconds:n.timeoutInSeconds||this.options.authorizeTimeoutInSeconds||60}));if(o.state!==s.state)throw new ae("state_mismatch","Invalid state");const r=((i=t.authorizationParams)===null||i===void 0?void 0:i.organization)||this.options.authorizationParams.organization;await this._requestToken({audience:o.audience,scope:o.scope,code_verifier:o.code_verifier,grant_type:"authorization_code",code:s.code,redirect_uri:o.redirect_uri},{nonceIn:o.nonce,organization:r})}async getUser(){var t;const n=await this._getIdTokenFromCache();return(t=n==null?void 0:n.decodedToken)===null||t===void 0?void 0:t.user}async getIdTokenClaims(){var t;const n=await this._getIdTokenFromCache();return(t=n==null?void 0:n.decodedToken)===null||t===void 0?void 0:t.claims}async loginWithRedirect(t={}){var n;const i=Po(t),{openUrl:o,fragment:s,appState:r}=i,a=je(i,["openUrl","fragment","appState"]),c=((n=a.authorizationParams)===null||n===void 0?void 0:n.organization)||this.options.authorizationParams.organization,u=await this._prepareAuthorizeUrl(a.authorizationParams||{}),{url:l}=u,f=je(u,["url"]);this.transactionManager.create(Object.assign(Object.assign(Object.assign({},f),{appState:r}),c&&{organization:c}));const h=s?`${l}#${s}`:l;o?await o(h):window.location.assign(h)}async handleRedirectCallback(t=window.location.href){const n=t.split("?").slice(1);if(n.length===0)throw new Error("There are no query params available for parsing.");const{state:i,code:o,error:s,error_description:r}=(f=>{f.indexOf("#")>-1&&(f=f.substring(0,f.indexOf("#")));const h=new URLSearchParams(f);return{state:h.get("state"),code:h.get("code")||void 0,error:h.get("error")||void 0,error_description:h.get("error_description")||void 0}})(n.join("")),a=this.transactionManager.get();if(!a)throw new ae("missing_transaction","Invalid state");if(this.transactionManager.remove(),s)throw new Fi(s,r||s,i,a.appState);if(!a.code_verifier||a.state&&a.state!==i)throw new ae("state_mismatch","Invalid state");const c=a.organization,u=a.nonce,l=a.redirect_uri;return await this._requestToken(Object.assign({audience:a.audience,scope:a.scope,code_verifier:a.code_verifier,grant_type:"authorization_code",code:o},l?{redirect_uri:l}:{}),{nonceIn:u,organization:c}),{appState:a.appState}}async checkSession(t){if(!this.cookieStorage.get(this.isAuthenticatedCookieName)){if(!this.cookieStorage.get("auth0.is.authenticated"))return;this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove("auth0.is.authenticated")}try{await this.getTokenSilently(t)}catch{}}async getTokenSilently(t={}){var n;const i=Object.assign(Object.assign({cacheMode:"on"},t),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),t.authorizationParams),{scope:ln(this.scope,(n=t.authorizationParams)===null||n===void 0?void 0:n.scope)})}),o=await((s,r)=>{let a=Bn[r];return a||(a=s().finally(()=>{delete Bn[r],a=null}),Bn[r]=a),a})(()=>this._getTokenSilently(i),`${this.options.clientId}::${i.authorizationParams.audience}::${i.authorizationParams.scope}`);return t.detailedResponse?o:o==null?void 0:o.access_token}async _getTokenSilently(t){const{cacheMode:n}=t,i=je(t,["cacheMode"]);if(n!=="off"){const o=await this._getEntryFromCache({scope:i.authorizationParams.scope,audience:i.authorizationParams.audience||"default",clientId:this.options.clientId});if(o)return o}if(n!=="cache-only"){if(!await(async(o,s=3)=>{for(let r=0;r<s;r++)if(await o())return!0;return!1})(()=>Gn.acquireLock("auth0.lock.getTokenSilently",5e3),10))throw new qt;try{if(window.addEventListener("pagehide",this._releaseLockOnPageHide),n!=="off"){const u=await this._getEntryFromCache({scope:i.authorizationParams.scope,audience:i.authorizationParams.audience||"default",clientId:this.options.clientId});if(u)return u}const o=this.options.useRefreshTokens?await this._getTokenUsingRefreshToken(i):await this._getTokenFromIFrame(i),{id_token:s,access_token:r,oauthTokenScope:a,expires_in:c}=o;return Object.assign(Object.assign({id_token:s,access_token:r},a?{scope:a}:null),{expires_in:c})}finally{await Gn.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)}}}async getTokenWithPopup(t={},n={}){var i;const o=Object.assign(Object.assign({},t),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),t.authorizationParams),{scope:ln(this.scope,(i=t.authorizationParams)===null||i===void 0?void 0:i.scope)})});return n=Object.assign(Object.assign({},Ta),n),await this.loginWithPopup(o,n),(await this.cacheManager.get(new Ne({scope:o.authorizationParams.scope,audience:o.authorizationParams.audience||"default",clientId:this.options.clientId}))).access_token}async isAuthenticated(){return!!await this.getUser()}_buildLogoutUrl(t){t.clientId!==null?t.clientId=t.clientId||this.options.clientId:delete t.clientId;const n=t.logoutParams||{},{federated:i}=n,o=je(n,["federated"]),s=i?"&federated":"";return this._url(`/v2/logout?${ui(Object.assign({clientId:t.clientId},o))}`)+s}async logout(t={}){const n=Po(t),{openUrl:i}=n,o=je(n,["openUrl"]);t.clientId===null?await this.cacheManager.clear():await this.cacheManager.clear(t.clientId||this.options.clientId),this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove(this.isAuthenticatedCookieName,{cookieDomain:this.options.cookieDomain}),this.userCache.remove("@@user@@");const s=this._buildLogoutUrl(o);i?await i(s):i!==!1&&window.location.assign(s)}async _getTokenFromIFrame(t){const n=Object.assign(Object.assign({},t.authorizationParams),{prompt:"none"}),i=this.cookieStorage.get(this.orgHintCookieName);i&&!n.organization&&(n.organization=i);const{url:o,state:s,nonce:r,code_verifier:a,redirect_uri:c,scope:u,audience:l}=await this._prepareAuthorizeUrl(n,{response_mode:"web_message"},window.location.origin);try{if(window.crossOriginIsolated)throw new ae("login_required","The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.");const f=t.timeoutInSeconds||this.options.authorizeTimeoutInSeconds,h=await((y,x,R=60)=>new Promise((S,k)=>{const j=window.document.createElement("iframe");j.setAttribute("width","0"),j.setAttribute("height","0"),j.style.display="none";const H=()=>{window.document.body.contains(j)&&(window.document.body.removeChild(j),window.removeEventListener("message",J,!1))};let J;const z=setTimeout(()=>{k(new qt),H()},1e3*R);J=function(q){if(q.origin!=x||!q.data||q.data.type!=="authorization_response")return;const ne=q.source;ne&&ne.close(),q.data.response.error?k(ae.fromPayload(q.data.response)):S(q.data.response),clearTimeout(z),window.removeEventListener("message",J,!1),setTimeout(H,2e3)},window.addEventListener("message",J,!1),window.document.body.appendChild(j),j.setAttribute("src",y)}))(o,this.domainUrl,f);if(s!==h.state)throw new ae("state_mismatch","Invalid state");const m=await this._requestToken(Object.assign(Object.assign({},t.authorizationParams),{code_verifier:a,code:h.code,grant_type:"authorization_code",redirect_uri:c,timeout:t.authorizationParams.timeout||this.httpTimeoutMs}),{nonceIn:r,organization:n.organization});return Object.assign(Object.assign({},m),{scope:u,oauthTokenScope:m.scope,audience:l})}catch(f){throw f.error==="login_required"&&this.logout({openUrl:!1}),f}}async _getTokenUsingRefreshToken(t){const n=await this.cacheManager.get(new Ne({scope:t.authorizationParams.scope,audience:t.authorizationParams.audience||"default",clientId:this.options.clientId}));if(!(n&&n.refresh_token||this.worker)){if(this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(t);throw new jn(t.authorizationParams.audience||"default",t.authorizationParams.scope)}const i=t.authorizationParams.redirect_uri||this.options.authorizationParams.redirect_uri||window.location.origin,o=typeof t.timeoutInSeconds=="number"?1e3*t.timeoutInSeconds:null;try{const s=await this._requestToken(Object.assign(Object.assign(Object.assign({},t.authorizationParams),{grant_type:"refresh_token",refresh_token:n&&n.refresh_token,redirect_uri:i}),o&&{timeout:o}));return Object.assign(Object.assign({},s),{scope:t.authorizationParams.scope,oauthTokenScope:s.scope,audience:t.authorizationParams.audience||"default"})}catch(s){if((s.message.indexOf("Missing Refresh Token")>-1||s.message&&s.message.indexOf("invalid refresh token")>-1)&&this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(t);throw s}}async _saveEntryInCache(t){const{id_token:n,decodedToken:i}=t,o=je(t,["id_token","decodedToken"]);this.userCache.set("@@user@@",{id_token:n,decodedToken:i}),await this.cacheManager.setIdToken(this.options.clientId,t.id_token,t.decodedToken),await this.cacheManager.set(o)}async _getIdTokenFromCache(){const t=this.options.authorizationParams.audience||"default",n=await this.cacheManager.getIdToken(new Ne({clientId:this.options.clientId,audience:t,scope:this.scope})),i=this.userCache.get("@@user@@");return n&&n.id_token===(i==null?void 0:i.id_token)?i:(this.userCache.set("@@user@@",n),n)}async _getEntryFromCache({scope:t,audience:n,clientId:i}){const o=await this.cacheManager.get(new Ne({scope:t,audience:n,clientId:i}),60);if(o&&o.access_token){const{access_token:s,oauthTokenScope:r,expires_in:a}=o,c=await this._getIdTokenFromCache();return c&&Object.assign(Object.assign({id_token:c.id_token,access_token:s},r?{scope:r}:null),{expires_in:a})}}async _requestToken(t,n){const{nonceIn:i,organization:o}=n||{},s=await La(Object.assign({baseUrl:this.domainUrl,client_id:this.options.clientId,auth0Client:this.options.auth0Client,useFormData:this.options.useFormData,timeout:this.httpTimeoutMs},t),this.worker),r=await this._verifyIdToken(s.id_token,i,o);return await this._saveEntryInCache(Object.assign(Object.assign(Object.assign(Object.assign({},s),{decodedToken:r,scope:t.scope,audience:t.audience||"default"}),s.scope?{oauthTokenScope:s.scope}:null),{client_id:this.options.clientId})),this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this._processOrgHint(o||r.claims.org_id),Object.assign(Object.assign({},s),{decodedToken:r})}}function Ft(e){e!=null&&e.redirect_uri&&(console.warn("Using `redirect_uri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `redirectUri` will be no longer supported in a future version"),e.authorizationParams=e.authorizationParams||{},e.authorizationParams.redirect_uri=e.redirect_uri,delete e.redirect_uri)}const it=()=>{console.error("Please ensure Auth0's Vue plugin is correctly installed.")},Za={isLoading:pe(!1),isAuthenticated:pe(!1),user:pe(void 0),idTokenClaims:pe(void 0),error:pe(null),loginWithPopup:it,loginWithRedirect:it,getAccessTokenSilently:it,getAccessTokenWithPopup:it,logout:it,checkSession:it,handleRedirectCallback:it},Ba=pe(Za);class Ga{constructor(t,n){var i,o;this.clientOptions=t,this.pluginOptions=n,this.isLoading=pe(!0),this.isAuthenticated=pe(!1),this.user=pe({}),this.idTokenClaims=pe(),this.error=pe(null),i=this,o=["constructor"],Object.getOwnPropertyNames(Object.getPrototypeOf(i)).filter(s=>!o.includes(s)).forEach(s=>i[s]=i[s].bind(i))}install(t){this._client=new Wa(Object.assign(Object.assign({},this.clientOptions),{auth0Client:{name:"auth0-vue",version:"2.3.1"}})),this.__checkSession(t.config.globalProperties.$router),t.config.globalProperties.$auth0=this,t.provide(Bs,this),Ba.value=this}async loginWithRedirect(t){return Ft(t),this._client.loginWithRedirect(t)}async loginWithPopup(t,n){return Ft(t),this.__proxy(()=>this._client.loginWithPopup(t,n))}async logout(t){return t!=null&&t.openUrl||(t==null?void 0:t.openUrl)===!1?this.__proxy(()=>this._client.logout(t)):this._client.logout(t)}async getAccessTokenSilently(t={}){return Ft(t),this.__proxy(()=>this._client.getTokenSilently(t))}async getAccessTokenWithPopup(t,n){return Ft(t),this.__proxy(()=>this._client.getTokenWithPopup(t,n))}async checkSession(t){return this.__proxy(()=>this._client.checkSession(t))}async handleRedirectCallback(t){return this.__proxy(()=>this._client.handleRedirectCallback(t))}async __checkSession(t){var n,i,o;const s=window.location.search;try{if((s.includes("code=")||s.includes("error="))&&s.includes("state=")&&!(!((n=this.pluginOptions)===null||n===void 0)&&n.skipRedirectCallback)){const r=await this.handleRedirectCallback(),a=r==null?void 0:r.appState,c=(i=a==null?void 0:a.target)!==null&&i!==void 0?i:"/";return window.history.replaceState({},"","/"),t&&t.push(c),r}await this.checkSession()}catch{window.history.replaceState({},"","/"),t&&t.push(((o=this.pluginOptions)===null||o===void 0?void 0:o.errorPath)||"/")}}async __refreshState(){this.isAuthenticated.value=await this._client.isAuthenticated(),this.user.value=await this._client.getUser(),this.idTokenClaims.value=await this._client.getIdTokenClaims(),this.isLoading.value=!1}async __proxy(t,n=!0){let i;try{i=await t(),this.error.value=null}catch(o){throw this.error.value=o,o}finally{n&&await this.__refreshState()}return i}}function Xa(e,t){return Ft(e),new Ga(e,t)}function Qs(){return ve(Bs)}/*!
  * vue-router v4.3.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const yt=typeof document<"u";function Ja(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const B=Object.assign;function Xn(e,t){const n={};for(const i in t){const o=t[i];n[i]=Ie(o)?o.map(e):e(o)}return n}const Dt=()=>{},Ie=Array.isArray,er=/#/g,qa=/&/g,Ya=/\//g,Qa=/=/g,el=/\?/g,tr=/\+/g,tl=/%5B/g,nl=/%5D/g,nr=/%5E/g,il=/%60/g,ir=/%7B/g,ol=/%7C/g,or=/%7D/g,sl=/%20/g;function $i(e){return encodeURI(""+e).replace(ol,"|").replace(tl,"[").replace(nl,"]")}function rl(e){return $i(e).replace(ir,"{").replace(or,"}").replace(nr,"^")}function fi(e){return $i(e).replace(tr,"%2B").replace(sl,"+").replace(er,"%23").replace(qa,"%26").replace(il,"`").replace(ir,"{").replace(or,"}").replace(nr,"^")}function cl(e){return fi(e).replace(Qa,"%3D")}function al(e){return $i(e).replace(er,"%23").replace(el,"%3F")}function ll(e){return e==null?"":al(e).replace(Ya,"%2F")}function Yt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const ul=/\/$/,fl=e=>e.replace(ul,"");function Jn(e,t,n="/"){let i,o={},s="",r="";const a=t.indexOf("#");let c=t.indexOf("?");return a<c&&a>=0&&(c=-1),c>-1&&(i=t.slice(0,c),s=t.slice(c+1,a>-1?a:t.length),o=e(s)),a>-1&&(i=i||t.slice(0,a),r=t.slice(a,t.length)),i=ml(i??t,n),{fullPath:i+(s&&"?")+s+r,path:i,query:o,hash:Yt(r)}}function dl(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Ro(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function hl(e,t,n){const i=t.matched.length-1,o=n.matched.length-1;return i>-1&&i===o&&Ct(t.matched[i],n.matched[o])&&sr(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Ct(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function sr(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!pl(e[n],t[n]))return!1;return!0}function pl(e,t){return Ie(e)?To(e,t):Ie(t)?To(t,e):e===t}function To(e,t){return Ie(t)?e.length===t.length&&e.every((n,i)=>n===t[i]):e.length===1&&e[0]===t}function ml(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),i=e.split("/"),o=i[i.length-1];(o===".."||o===".")&&i.push("");let s=n.length-1,r,a;for(r=0;r<i.length;r++)if(a=i[r],a!==".")if(a==="..")s>1&&s--;else break;return n.slice(0,s).join("/")+"/"+i.slice(r).join("/")}var Qt;(function(e){e.pop="pop",e.push="push"})(Qt||(Qt={}));var Wt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Wt||(Wt={}));function gl(e){if(!e)if(yt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),fl(e)}const yl=/^[^#]+#/;function _l(e,t){return e.replace(yl,"#")+t}function wl(e,t){const n=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-n.left-(t.left||0),top:i.top-n.top-(t.top||0)}}const Ln=()=>({left:window.scrollX,top:window.scrollY});function vl(e){let t;if("el"in e){const n=e.el,i=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?i?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=wl(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ao(e,t){return(history.state?history.state.position-t:-1)+e}const di=new Map;function bl(e,t){di.set(e,t)}function kl(e){const t=di.get(e);return di.delete(e),t}let xl=()=>location.protocol+"//"+location.host;function rr(e,t){const{pathname:n,search:i,hash:o}=t,s=e.indexOf("#");if(s>-1){let a=o.includes(e.slice(s))?e.slice(s).length:1,c=o.slice(a);return c[0]!=="/"&&(c="/"+c),Ro(c,"")}return Ro(n,e)+i+o}function Sl(e,t,n,i){let o=[],s=[],r=null;const a=({state:h})=>{const m=rr(e,location),y=n.value,x=t.value;let R=0;if(h){if(n.value=m,t.value=h,r&&r===y){r=null;return}R=x?h.position-x.position:0}else i(m);o.forEach(S=>{S(n.value,y,{delta:R,type:Qt.pop,direction:R?R>0?Wt.forward:Wt.back:Wt.unknown})})};function c(){r=n.value}function u(h){o.push(h);const m=()=>{const y=o.indexOf(h);y>-1&&o.splice(y,1)};return s.push(m),m}function l(){const{history:h}=window;h.state&&h.replaceState(B({},h.state,{scroll:Ln()}),"")}function f(){for(const h of s)h();s=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:c,listen:u,destroy:f}}function jo(e,t,n,i=!1,o=!1){return{back:e,current:t,forward:n,replaced:i,position:window.history.length,scroll:o?Ln():null}}function El(e){const{history:t,location:n}=window,i={value:rr(e,n)},o={value:t.state};o.value||s(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(c,u,l){const f=e.indexOf("#"),h=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+c:xl()+e+c;try{t[l?"replaceState":"pushState"](u,"",h),o.value=u}catch(m){console.error(m),n[l?"replace":"assign"](h)}}function r(c,u){const l=B({},t.state,jo(o.value.back,c,o.value.forward,!0),u,{position:o.value.position});s(c,l,!0),i.value=c}function a(c,u){const l=B({},o.value,t.state,{forward:c,scroll:Ln()});s(l.current,l,!0);const f=B({},jo(i.value,c,null),{position:l.position+1},u);s(c,f,!1),i.value=c}return{location:i,state:o,push:a,replace:r}}function Il(e){e=gl(e);const t=El(e),n=Sl(e,t.state,t.location,t.replace);function i(s,r=!0){r||n.pauseListeners(),history.go(s)}const o=B({location:"",base:e,go:i,createHref:_l.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Cl(e){return typeof e=="string"||e&&typeof e=="object"}function cr(e){return typeof e=="string"||typeof e=="symbol"}const Ze={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},ar=Symbol("");var Lo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Lo||(Lo={}));function Ot(e,t){return B(new Error,{type:e,[ar]:!0},t)}function Ke(e,t){return e instanceof Error&&ar in e&&(t==null||!!(e.type&t))}const Mo="[^/]+?",Ol={sensitive:!1,strict:!1,start:!0,end:!0},Pl=/[.+*?^${}()[\]/\\]/g;function Rl(e,t){const n=B({},Ol,t),i=[];let o=n.start?"^":"";const s=[];for(const u of e){const l=u.length?[]:[90];n.strict&&!u.length&&(o+="/");for(let f=0;f<u.length;f++){const h=u[f];let m=40+(n.sensitive?.25:0);if(h.type===0)f||(o+="/"),o+=h.value.replace(Pl,"\\$&"),m+=40;else if(h.type===1){const{value:y,repeatable:x,optional:R,regexp:S}=h;s.push({name:y,repeatable:x,optional:R});const k=S||Mo;if(k!==Mo){m+=10;try{new RegExp(`(${k})`)}catch(H){throw new Error(`Invalid custom RegExp for param "${y}" (${k}): `+H.message)}}let j=x?`((?:${k})(?:/(?:${k}))*)`:`(${k})`;f||(j=R&&u.length<2?`(?:/${j})`:"/"+j),R&&(j+="?"),o+=j,m+=20,R&&(m+=-8),x&&(m+=-20),k===".*"&&(m+=-50)}l.push(m)}i.push(l)}if(n.strict&&n.end){const u=i.length-1;i[u][i[u].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const r=new RegExp(o,n.sensitive?"":"i");function a(u){const l=u.match(r),f={};if(!l)return null;for(let h=1;h<l.length;h++){const m=l[h]||"",y=s[h-1];f[y.name]=m&&y.repeatable?m.split("/"):m}return f}function c(u){let l="",f=!1;for(const h of e){(!f||!l.endsWith("/"))&&(l+="/"),f=!1;for(const m of h)if(m.type===0)l+=m.value;else if(m.type===1){const{value:y,repeatable:x,optional:R}=m,S=y in u?u[y]:"";if(Ie(S)&&!x)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const k=Ie(S)?S.join("/"):S;if(!k)if(R)h.length<2&&(l.endsWith("/")?l=l.slice(0,-1):f=!0);else throw new Error(`Missing required param "${y}"`);l+=k}}return l||"/"}return{re:r,score:i,keys:s,parse:a,stringify:c}}function Tl(e,t){let n=0;for(;n<e.length&&n<t.length;){const i=t[n]-e[n];if(i)return i;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Al(e,t){let n=0;const i=e.score,o=t.score;for(;n<i.length&&n<o.length;){const s=Tl(i[n],o[n]);if(s)return s;n++}if(Math.abs(o.length-i.length)===1){if(No(i))return 1;if(No(o))return-1}return o.length-i.length}function No(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const jl={type:0,value:""},Ll=/[a-zA-Z0-9_]/;function Ml(e){if(!e)return[[]];if(e==="/")return[[jl]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${u}": ${m}`)}let n=0,i=n;const o=[];let s;function r(){s&&o.push(s),s=[]}let a=0,c,u="",l="";function f(){u&&(n===0?s.push({type:0,value:u}):n===1||n===2||n===3?(s.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:u,regexp:l,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),u="")}function h(){u+=c}for(;a<e.length;){if(c=e[a++],c==="\\"&&n!==2){i=n,n=4;continue}switch(n){case 0:c==="/"?(u&&f(),r()):c===":"?(f(),n=1):h();break;case 4:h(),n=i;break;case 1:c==="("?n=2:Ll.test(c)?h():(f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--);break;case 2:c===")"?l[l.length-1]=="\\"?l=l.slice(0,-1)+c:n=3:l+=c;break;case 3:f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--,l="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),r(),o}function Nl(e,t,n){const i=Rl(Ml(e.path),n),o=B(i,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function zl(e,t){const n=[],i=new Map;t=Ko({strict:!1,end:!0,sensitive:!1},t);function o(l){return i.get(l)}function s(l,f,h){const m=!h,y=Fl(l);y.aliasOf=h&&h.record;const x=Ko(t,l),R=[y];if("alias"in l){const j=typeof l.alias=="string"?[l.alias]:l.alias;for(const H of j)R.push(B({},y,{components:h?h.record.components:y.components,path:H,aliasOf:h?h.record:y}))}let S,k;for(const j of R){const{path:H}=j;if(f&&H[0]!=="/"){const J=f.record.path,z=J[J.length-1]==="/"?"":"/";j.path=f.record.path+(H&&z+H)}if(S=Nl(j,f,x),h?h.alias.push(S):(k=k||S,k!==S&&k.alias.push(S),m&&l.name&&!Fo(S)&&r(l.name)),y.children){const J=y.children;for(let z=0;z<J.length;z++)s(J[z],S,h&&h.children[z])}h=h||S,(S.record.components&&Object.keys(S.record.components).length||S.record.name||S.record.redirect)&&c(S)}return k?()=>{r(k)}:Dt}function r(l){if(cr(l)){const f=i.get(l);f&&(i.delete(l),n.splice(n.indexOf(f),1),f.children.forEach(r),f.alias.forEach(r))}else{const f=n.indexOf(l);f>-1&&(n.splice(f,1),l.record.name&&i.delete(l.record.name),l.children.forEach(r),l.alias.forEach(r))}}function a(){return n}function c(l){let f=0;for(;f<n.length&&Al(l,n[f])>=0&&(l.record.path!==n[f].record.path||!lr(l,n[f]));)f++;n.splice(f,0,l),l.record.name&&!Fo(l)&&i.set(l.record.name,l)}function u(l,f){let h,m={},y,x;if("name"in l&&l.name){if(h=i.get(l.name),!h)throw Ot(1,{location:l});x=h.record.name,m=B(zo(f.params,h.keys.filter(k=>!k.optional).concat(h.parent?h.parent.keys.filter(k=>k.optional):[]).map(k=>k.name)),l.params&&zo(l.params,h.keys.map(k=>k.name))),y=h.stringify(m)}else if(l.path!=null)y=l.path,h=n.find(k=>k.re.test(y)),h&&(m=h.parse(y),x=h.record.name);else{if(h=f.name?i.get(f.name):n.find(k=>k.re.test(f.path)),!h)throw Ot(1,{location:l,currentLocation:f});x=h.record.name,m=B({},f.params,l.params),y=h.stringify(m)}const R=[];let S=h;for(;S;)R.unshift(S.record),S=S.parent;return{name:x,path:y,params:m,matched:R,meta:Ul(R)}}return e.forEach(l=>s(l)),{addRoute:s,resolve:u,removeRoute:r,getRoutes:a,getRecordMatcher:o}}function zo(e,t){const n={};for(const i of t)i in e&&(n[i]=e[i]);return n}function Fl(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Kl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function Kl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const i in e.components)t[i]=typeof n=="object"?n[i]:n;return t}function Fo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ul(e){return e.reduce((t,n)=>B(t,n.meta),{})}function Ko(e,t){const n={};for(const i in e)n[i]=i in t?t[i]:e[i];return n}function lr(e,t){return t.children.some(n=>n===e||lr(e,n))}function Hl(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<i.length;++o){const s=i[o].replace(tr," "),r=s.indexOf("="),a=Yt(r<0?s:s.slice(0,r)),c=r<0?null:Yt(s.slice(r+1));if(a in t){let u=t[a];Ie(u)||(u=t[a]=[u]),u.push(c)}else t[a]=c}return t}function Uo(e){let t="";for(let n in e){const i=e[n];if(n=cl(n),i==null){i!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ie(i)?i.map(s=>s&&fi(s)):[i&&fi(i)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+n,s!=null&&(t+="="+s))})}return t}function $l(e){const t={};for(const n in e){const i=e[n];i!==void 0&&(t[n]=Ie(i)?i.map(o=>o==null?null:""+o):i==null?i:""+i)}return t}const Vl=Symbol(""),Ho=Symbol(""),Mn=Symbol(""),Vi=Symbol(""),hi=Symbol("");function Nt(){let e=[];function t(i){return e.push(i),()=>{const o=e.indexOf(i);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Je(e,t,n,i,o,s=r=>r()){const r=i&&(i.enterCallbacks[o]=i.enterCallbacks[o]||[]);return()=>new Promise((a,c)=>{const u=h=>{h===!1?c(Ot(4,{from:n,to:t})):h instanceof Error?c(h):Cl(h)?c(Ot(2,{from:t,to:h})):(r&&i.enterCallbacks[o]===r&&typeof h=="function"&&r.push(h),a())},l=s(()=>e.call(i&&i.instances[o],t,n,u));let f=Promise.resolve(l);e.length<3&&(f=f.then(u)),f.catch(h=>c(h))})}function qn(e,t,n,i,o=s=>s()){const s=[];for(const r of e)for(const a in r.components){let c=r.components[a];if(!(t!=="beforeRouteEnter"&&!r.instances[a]))if(Dl(c)){const l=(c.__vccOpts||c)[t];l&&s.push(Je(l,n,i,r,a,o))}else{let u=c();s.push(()=>u.then(l=>{if(!l)return Promise.reject(new Error(`Couldn't resolve component "${a}" at "${r.path}"`));const f=Ja(l)?l.default:l;r.components[a]=f;const m=(f.__vccOpts||f)[t];return m&&Je(m,n,i,r,a,o)()}))}}return s}function Dl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function $o(e){const t=ve(Mn),n=ve(Vi),i=xe(()=>t.resolve(lt(e.to))),o=xe(()=>{const{matched:c}=i.value,{length:u}=c,l=c[u-1],f=n.matched;if(!l||!f.length)return-1;const h=f.findIndex(Ct.bind(null,l));if(h>-1)return h;const m=Vo(c[u-2]);return u>1&&Vo(l)===m&&f[f.length-1].path!==m?f.findIndex(Ct.bind(null,c[u-2])):h}),s=xe(()=>o.value>-1&&Gl(n.params,i.value.params)),r=xe(()=>o.value>-1&&o.value===n.matched.length-1&&sr(n.params,i.value.params));function a(c={}){return Bl(c)?t[lt(e.replace)?"replace":"push"](lt(e.to)).catch(Dt):Promise.resolve()}return{route:i,href:xe(()=>i.value.href),isActive:s,isExactActive:r,navigate:a}}const Wl=Tt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:$o,setup(e,{slots:t}){const n=En($o(e)),{options:i}=ve(Mn),o=xe(()=>({[Do(e.activeClass,i.linkActiveClass,"router-link-active")]:n.isActive,[Do(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const s=t.default&&t.default(n);return e.custom?s:Zs("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},s)}}}),Zl=Wl;function Bl(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Gl(e,t){for(const n in t){const i=t[n],o=e[n];if(typeof i=="string"){if(i!==o)return!1}else if(!Ie(o)||o.length!==i.length||i.some((s,r)=>s!==o[r]))return!1}return!0}function Vo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Do=(e,t,n)=>e??t??n,Xl=Tt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const i=ve(hi),o=xe(()=>e.route||i.value),s=ve(Ho,0),r=xe(()=>{let u=lt(s);const{matched:l}=o.value;let f;for(;(f=l[u])&&!f.components;)u++;return u}),a=xe(()=>o.value.matched[r.value]);dn(Ho,xe(()=>r.value+1)),dn(Vl,a),dn(hi,o);const c=pe();return Ut(()=>[c.value,a.value,e.name],([u,l,f],[h,m,y])=>{l&&(l.instances[f]=u,m&&m!==l&&u&&u===h&&(l.leaveGuards.size||(l.leaveGuards=m.leaveGuards),l.updateGuards.size||(l.updateGuards=m.updateGuards))),u&&l&&(!m||!Ct(l,m)||!h)&&(l.enterCallbacks[f]||[]).forEach(x=>x(u))},{flush:"post"}),()=>{const u=o.value,l=e.name,f=a.value,h=f&&f.components[l];if(!h)return Wo(n.default,{Component:h,route:u});const m=f.props[l],y=m?m===!0?u.params:typeof m=="function"?m(u):m:null,R=Zs(h,B({},y,t,{onVnodeUnmounted:S=>{S.component.isUnmounted&&(f.instances[l]=null)},ref:c}));return Wo(n.default,{Component:R,route:u})||R}}});function Wo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ur=Xl;function Jl(e){const t=zl(e.routes,e),n=e.parseQuery||Hl,i=e.stringifyQuery||Uo,o=e.history,s=Nt(),r=Nt(),a=Nt(),c=Br(Ze);let u=Ze;yt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=Xn.bind(null,w=>""+w),f=Xn.bind(null,ll),h=Xn.bind(null,Yt);function m(w,T){let O,L;return cr(w)?(O=t.getRecordMatcher(w),L=T):L=w,t.addRoute(L,O)}function y(w){const T=t.getRecordMatcher(w);T&&t.removeRoute(T)}function x(){return t.getRoutes().map(w=>w.record)}function R(w){return!!t.getRecordMatcher(w)}function S(w,T){if(T=B({},T||c.value),typeof w=="string"){const p=Jn(n,w,T.path),g=t.resolve({path:p.path},T),v=o.createHref(p.fullPath);return B(p,g,{params:h(g.params),hash:Yt(p.hash),redirectedFrom:void 0,href:v})}let O;if(w.path!=null)O=B({},w,{path:Jn(n,w.path,T.path).path});else{const p=B({},w.params);for(const g in p)p[g]==null&&delete p[g];O=B({},w,{params:f(p)}),T.params=f(T.params)}const L=t.resolve(O,T),Z=w.hash||"";L.params=l(h(L.params));const Y=dl(i,B({},w,{hash:rl(Z),path:L.path})),d=o.createHref(Y);return B({fullPath:Y,hash:Z,query:i===Uo?$l(w.query):w.query||{}},L,{redirectedFrom:void 0,href:d})}function k(w){return typeof w=="string"?Jn(n,w,c.value.path):B({},w)}function j(w,T){if(u!==w)return Ot(8,{from:T,to:w})}function H(w){return q(w)}function J(w){return H(B(k(w),{replace:!0}))}function z(w){const T=w.matched[w.matched.length-1];if(T&&T.redirect){const{redirect:O}=T;let L=typeof O=="function"?O(w):O;return typeof L=="string"&&(L=L.includes("?")||L.includes("#")?L=k(L):{path:L},L.params={}),B({query:w.query,hash:w.hash,params:L.path!=null?{}:w.params},L)}}function q(w,T){const O=u=S(w),L=c.value,Z=w.state,Y=w.force,d=w.replace===!0,p=z(O);if(p)return q(B(k(p),{state:typeof p=="object"?B({},Z,p.state):Z,force:Y,replace:d}),T||O);const g=O;g.redirectedFrom=T;let v;return!Y&&hl(i,L,O)&&(v=Ot(16,{to:g,from:L}),Pe(L,L,!0,!1)),(v?Promise.resolve(v):Ce(g,L)).catch(_=>Ke(_)?Ke(_,2)?_:De(_):W(_,g,L)).then(_=>{if(_){if(Ke(_,2))return q(B({replace:d},k(_.to),{state:typeof _.to=="object"?B({},Z,_.to.state):Z,force:Y}),T||g)}else _=et(g,L,!0,d,Z);return Ve(g,L,_),_})}function ne(w,T){const O=j(w,T);return O?Promise.reject(O):Promise.resolve()}function ze(w){const T=pt.values().next().value;return T&&typeof T.runWithContext=="function"?T.runWithContext(w):w()}function Ce(w,T){let O;const[L,Z,Y]=ql(w,T);O=qn(L.reverse(),"beforeRouteLeave",w,T);for(const p of L)p.leaveGuards.forEach(g=>{O.push(Je(g,w,T))});const d=ne.bind(null,w,T);return O.push(d),ce(O).then(()=>{O=[];for(const p of s.list())O.push(Je(p,w,T));return O.push(d),ce(O)}).then(()=>{O=qn(Z,"beforeRouteUpdate",w,T);for(const p of Z)p.updateGuards.forEach(g=>{O.push(Je(g,w,T))});return O.push(d),ce(O)}).then(()=>{O=[];for(const p of Y)if(p.beforeEnter)if(Ie(p.beforeEnter))for(const g of p.beforeEnter)O.push(Je(g,w,T));else O.push(Je(p.beforeEnter,w,T));return O.push(d),ce(O)}).then(()=>(w.matched.forEach(p=>p.enterCallbacks={}),O=qn(Y,"beforeRouteEnter",w,T,ze),O.push(d),ce(O))).then(()=>{O=[];for(const p of r.list())O.push(Je(p,w,T));return O.push(d),ce(O)}).catch(p=>Ke(p,8)?p:Promise.reject(p))}function Ve(w,T,O){a.list().forEach(L=>ze(()=>L(w,T,O)))}function et(w,T,O,L,Z){const Y=j(w,T);if(Y)return Y;const d=T===Ze,p=yt?history.state:{};O&&(L||d?o.replace(w.fullPath,B({scroll:d&&p&&p.scroll},Z)):o.push(w.fullPath,Z)),c.value=w,Pe(w,T,O,d),De()}let Oe;function At(){Oe||(Oe=o.listen((w,T,O)=>{if(!tn.listening)return;const L=S(w),Z=z(L);if(Z){q(B(Z,{replace:!0}),L).catch(Dt);return}u=L;const Y=c.value;yt&&bl(Ao(Y.fullPath,O.delta),Ln()),Ce(L,Y).catch(d=>Ke(d,12)?d:Ke(d,2)?(q(d.to,L).then(p=>{Ke(p,20)&&!O.delta&&O.type===Qt.pop&&o.go(-1,!1)}).catch(Dt),Promise.reject()):(O.delta&&o.go(-O.delta,!1),W(d,L,Y))).then(d=>{d=d||et(L,Y,!1),d&&(O.delta&&!Ke(d,8)?o.go(-O.delta,!1):O.type===Qt.pop&&Ke(d,20)&&o.go(-1,!1)),Ve(L,Y,d)}).catch(Dt)}))}let dt=Nt(),ie=Nt(),G;function W(w,T,O){De(w);const L=ie.list();return L.length?L.forEach(Z=>Z(w,T,O)):console.error(w),Promise.reject(w)}function Fe(){return G&&c.value!==Ze?Promise.resolve():new Promise((w,T)=>{dt.add([w,T])})}function De(w){return G||(G=!w,At(),dt.list().forEach(([T,O])=>w?O(w):T()),dt.reset()),w}function Pe(w,T,O,L){const{scrollBehavior:Z}=e;if(!yt||!Z)return Promise.resolve();const Y=!O&&kl(Ao(w.fullPath,0))||(L||!O)&&history.state&&history.state.scroll||null;return bs().then(()=>Z(w,T,Y)).then(d=>d&&vl(d)).catch(d=>W(d,w,T))}const de=w=>o.go(w);let ht;const pt=new Set,tn={currentRoute:c,listening:!0,addRoute:m,removeRoute:y,hasRoute:R,getRoutes:x,resolve:S,options:e,push:H,replace:J,go:de,back:()=>de(-1),forward:()=>de(1),beforeEach:s.add,beforeResolve:r.add,afterEach:a.add,onError:ie.add,isReady:Fe,install(w){const T=this;w.component("RouterLink",Zl),w.component("RouterView",ur),w.config.globalProperties.$router=T,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>lt(c)}),yt&&!ht&&c.value===Ze&&(ht=!0,H(o.location).catch(Z=>{}));const O={};for(const Z in Ze)Object.defineProperty(O,Z,{get:()=>c.value[Z],enumerable:!0});w.provide(Mn,T),w.provide(Vi,ds(O)),w.provide(hi,c);const L=w.unmount;pt.add(w),w.unmount=function(){pt.delete(w),pt.size<1&&(u=Ze,Oe&&Oe(),Oe=null,c.value=Ze,ht=!1,G=!1),L()}}};function ce(w){return w.reduce((T,O)=>T.then(()=>ze(O)),Promise.resolve())}return tn}function ql(e,t){const n=[],i=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let r=0;r<s;r++){const a=t.matched[r];a&&(e.matched.find(u=>Ct(u,a))?i.push(a):n.push(a));const c=e.matched[r];c&&(t.matched.find(u=>Ct(u,c))||o.push(c))}return[n,i,o]}function Yl(){return ve(Mn)}function fr(){return ve(Vi)}const Ql=Tt({__name:"App",setup(e){return(t,n)=>(Rn(),Vc(lt(ur)))}}),eu=Tt({__name:"Auth0CallbackView",setup(e){const{isAuthenticated:t}=Qs(),n=Yl(),i=fr(),o=pe("");return Ut(t,s=>{if(!i.query.error&&s){debugger;n.push("/")}}),Ri(()=>{let s=0;if(i.query.error){debugger;window.location.href=`${window.location.origin}/access-denied`}setInterval(()=>{s<4?(o.value+=".",s++):(o.value="",s=0)},500)}),(s,r)=>(Rn(),ji("p",null,"Redirecting "+Qo(o.value),1))}}),tu=Tt({__name:"HomeView",setup(e){const t=fr(),n=navigator.language,i={en:"en","en-US":"en"};let o="";const{pathMatch:s}=t.params,{hash:r}=window.location;Array.isArray(s)?o=s[0]==="en"?s.slice(1).join("/"):s.join("/"):s&&(o=s),Object.keys(i).filter(c=>c.toLowerCase()===n.toLowerCase()).length===0?window.location.href=`${window.location.origin}/en/index.html${r||"#/"+o}`:window.location.href=`${window.location.origin}/${i[n]}/index.html${r||"#/"+o}`;const a=pe("");return Ri(()=>{let c=0;setInterval(()=>{c<4?(a.value+=".",c++):(a.value="",c=0)},500)}),(c,u)=>(Rn(),ji("p",null,"Redirecting "+Qo(a.value),1))}}),nu=Tt({__name:"LoginView",setup(e){const{loginWithRedirect:t}=Qs();return t(),(n,i)=>(Rn(),ji("p",null,"One moment..."))}}),iu=Jl({history:Il(),routes:[{path:"/auth0-callback",name:"auth0-callback",component:eu},{path:"/login",name:"login",component:nu},{path:"/:pathMatch(.*)*",component:tu}],strict:!0}),pi=Ca(Ql);pi.use(iu);async function ou(){const e=location.origin+(location.origin.indexOf(":5173")!==-1?"/src/assets":"/en/assets/data")+"/appConfig.json";return await(await fetch(e)).json()}const su=async()=>{try{const e=await ou();pi.use(Xa({...e.auth0,authorizationParams:{...e.auth0.authorizationParams,redirect_uri:window.location.origin+"/auth0-callback"},cacheLocation:"localstorage"}))}catch(e){console.error(e)}pi.mount("#app")};su().then(()=>console.info("app mounted"));
