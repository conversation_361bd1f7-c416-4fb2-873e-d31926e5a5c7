import { FormControl } from '@angular/forms';

import { MaxItemSelectValidator } from './max-item-select-validator';

describe('MaxItemSelectValidator', () => {
  let validator: MaxItemSelectValidator;

  beforeEach(() => {
    validator = new MaxItemSelectValidator();
  });

  it('should return null if the number of selected items is less than or equal to maxItemSelect', () => {
    validator.maxItemSelect = 5;

    const control = new FormControl([1, 2, 3, 4, 5]);

    const result = validator.validate(control);

    expect(result).toBeNull();
  });

  it('should return an error object if the number of selected items exceeds maxItemSelect', () => {
    validator.maxItemSelect = 5;

    const control = new FormControl([1, 2, 3, 4, 5, 6]);

    const result = validator.validate(control);

    expect(result).toEqual({ maxItem: true });
  });

  it('should return null if control value is null or undefined', () => {
    const control = new FormControl(null);

    const result = validator.validate(control);

    expect(result).toBeNull();
  });
});
