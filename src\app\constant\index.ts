import { Permission } from '../directives/acl/model/acl';

export const DEFAULT_RANGE_IN_DAYS = 13;

export const DEFAULT_MIN_RANGE_IN_YEARS = 2;

export const THIRTY_DAYS = 30;

export const NINETY_DAYS = 90;

export const EMPTY = '';

export const CHART_POINT_RADIUS = 4;

export const FIRST_INDEX = 0;

export const FIVE_SECONDS = 5000;

export const SIXTY_MINS_IN_MILLISECONDS = 1800000;

export const FIVE_MINS_IN_MILLISECONDS = 300000;

export const SIXTY_SECONDS_IN_MILLISECONDS = 60000;

export const ONE_MINUTE_TO_MILISECONDS = 60000;

export const TWO_MINUTE_TO_MILISECONDS = 120000;

export const ONE_SECOND_IN_MILLISECONDS = 1000;

export const THREE_HUNDRED_MILLISECONDS = 300;

export const SNACKBAR_TIMEOUT_IN_MILISECONDS = 3000;

export const ONE_PERCENT = 100;

export const MAX_FREQUENCY = 24;

export const MIN_FREQUENCY = 1;

export const DAYS_IN_WEEK = 7;

export const ONE_YEAR = 1;

export const DEFAULT_INPUT_TEXT_MAX_LENGTH = 0;

export const THREE_MONTHS = 3;

export const FOUR_MONTHS = 4;

export const SIX_MONTHS = 6;

export const GREATER_THAN_SYMBOL = '>';

export const GREATER_THAN_OR_EQUAL_TO_SYMBOL = '>=';

export const LESS_THAN_SYMBOL = '<';

export const LESS_THAN_OR_EQUAL_TO_SYMBOL = '<=';

export const SYSMEX_LOGO_BASE64 =
  // eslint-disable-next-line max-len
  'data:image/png;base64,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';

export const OUT_OF_BOUND_BASE64 =
  // eslint-disable-next-line max-len
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAoCAYAAACIC2hQAAAACXBIWXMAAB2HAAAdhwGP5fFlAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAAs5JREFUWIXtWN9LFFEU/s7M7hZmRSYpKIIEhigF0ovQOjvr9gMf7CX3od4iieghFoIgKOpBoh+o/Rv6J6Shvgj1FEGQ9BTBzGr2EkW0u/f0oJW7e2f23pm7SNj3NDP3nPN9nPtj5htiZvwLsHZbgCr2rtCi4/QWHafXdN2E6YJs4xkABnDJZF0yuZn8XMaF4JcAYFl07tjC0gtTtc1NfT5vQ/Ds71shMAPXNTZjxoT6n9evAzj59wkP+BZPmqpvZOo/ptNHUklrDUB7zdCXipXs61pY2IzLYaSjqSQ9RL1IAGizROm+CY7YHfUcp59svAGQDAgpUwVDHcvLb+PwxO4o2ZhBsEgASPBWTCzEElrMjlwEcF4hdLSYdcbjcEUW+i4/mGLQE9V4Bk9/GBvbF5UvstC2jaMFAH3qGXS89cf3W1H5Im2m9VyuQ4jSGoBDmqlfE2U+0b6y4ulyRnpzCPHzEUD1IhmrIHzavu4GYbgm4mA5gSkAV3U5tTvqjY4MEdNryJYNUb5zcWkeAPzRzASY5yQlBIiHOxdXXunw6q1RIiLGc+28Wk5BsyAivSQNeJn0FYDO6OmSgDDsZUYu66QoC/XGT7cQ0ZS+qgBiwuMN121VjlcO/HbgLoCeSKokYKBLQNxR5lcJ8s6me5hRiC5LDibcVrUtSscTVaxpAC0NAwUX/KwzsaUC3Wi8XfazjadQsC0Nj6ed9qJZULEt4VOfz9ss4n/5NIKKbQkV6m36kwQ+pczIWAUwD2B++1o1ccCzKtfCIgKnPsRehFRTejMFIdS2BHY0mbQeaImMjzaLS/eCBqVCPcfpJ+BG8zTJQYybRdcdlI1JFzDZPA1QmL2Qg3nOzzraaTv1MIlZALnagbqObtkLuhCHLSaktqVKqK69aBZktqVKqL69aBbqbcuf42nbXrwHcHg3pElQZVuM/s1rJvbuH+dm4b9Q0/gFievw3V8b/LMAAAAASUVORK5CYII=';

export const DEFAULT_PERMISSION: Permission = {
  read: true,
  write: false,
};

export const CQC_SERVICE_CODE = 'cqc';

export const HTTP_HEADER_BASE = {
  'content-type': 'application/json',
};

export const COUNTRY_CODE = {
  GERMANY: 'DEU',
};

export const THREE_YEARS = 3;

export const SESSION_LOG_ACTION = {
  login: 'LOGIN',
  logout: 'LOGOUT',
};

export const DIALOG_ACTION_CODES = {
  SAVE: 'save',
  CANCEL: 'cancel',
  RESET: 'reset',
  UPLOAD: 'upload',
  START: 'start',
  END: 'end',
};

export const FILE_EXTENSIONS = {
  PDF: 'pdf',
  TXT: 'txt',
};

export const BROWSER_PATTERNS = {
  Firefox: /Firefox\/[\d.]+/,
  Edg: /Edg\/[\d.]+/,
  Chrome: /Chrome\/[\d.]+/,
  Safari: /Safari\/[\d.]+/,
};

export const BROWSER_NAMES = {
  Firefox: 'Firefox',
  Edg: 'Edge',
  Chrome: 'Chrome',
  Safari: 'Safari',
  Other: 'Other',
};

export const KEY_CODE = {
  KEY_BACKSPACE: 'Backspace',
  KEY_TAB: 'Tab',
  KEY_ENTER: 'Enter',
  KEY_ESCAPE: 'Escape',
  KEY_HOME: 'Home',
  KEY_END: 'End',
  KEY_LEFT: 'ArrowLeft',
  KEY_RIGHT: 'ArrowRight',
  KEY_DELETE: 'Delete',
  KEY_NUM_0: '0',
  KEY_NUM_9: '9',
  KEY_A: 'a',
  KEY_C: 'c',
  KEY_V: 'v',
  KEY_X: 'x',
  KEY_NUMPAD_0: 'Numpad0',
  KEY_NUMPAD_9: 'Numpad9',
};

export const NUMBER_INPUT_PATTERN = {
  ALLOW_NEGATIVE_NO_DECIMALS: /^-?\d+$/,
  ALLOW_NEGATIVE_WITH_DECIMALS: '^-?\\s*((\\d+(\\.\\d*)?)|((\\d*(\\.\\d+))))\\s*$',
  VALIDATE_NO_DECIMALS: /^\d+$/,
  VALIDATE_WITH_DECIMALS: '^\\s*((\\d+(\\.\\d*)?)|((\\d*(\\.\\d+))))\\s*$',
  NON_NUMERIC_PERIOD_HYPHEN: /[^0-9.-]/g,
  DECIMAL: /\./g,
  NEGATIVE: /^-/,
  PRECEDES_DIGITS: /^0+(?=\d)/,
  TRAILING_PERIOD: /\.$/,
};

export const DEFAULT_THUMBNAIL_PATH = 'assets/default-analyzer-thumbnail.png';

export const CUSTOMER_LIMIT_NULL_VALUE = -1;

export const DELAY_TIME_TO_OPEN_NEXT_DIALOG = 300; // milliseconds

export const ONE_MB_IN_BYTES = 1048576;

export const FIRST_VERSION = 'v0.0.1';

export const CHART_LEVELS_COLOR = {
  LEVEL_1: 'var(--red-100)',
  LEVEL_2: 'var(--green-100)',
  LEVEL_3: '#000000',
};

export const GRID_LOCALE_TEXT = {
  selectAll: $localize`:app-app_grid-text:(Select All)`,
  pinColumn: $localize`:app-app_grid-text:Pin Column`,
  noPin: $localize`:app-app_grid-text:No Pin`,
  pinLeft: $localize`:app-app_grid-text:Pin Left`,
  pinRight: $localize`:app-app_grid-text:Pin Right`,
  autosizeThiscolumn: $localize`:app-app_grid-text:Autoresize this column`,
  autosizeAllColumns: $localize`:app-app_grid-text:Autoresize all columns`,
  resetColumns: $localize`:app-app_grid-text:Reset Columns`,
  searchOoo: $localize`:app-app_grid-text:Search...`,
  equals: $localize`:app-app_grid-text:Equals`,
  notEqual: $localize`:app-app_grid-text:Does not equal`,
  lessThan: $localize`:app-app_grid-text:Less than`,
  lessThanOrEqual: $localize`:app-app_grid-text:Less than or equal`,
  greaterThan: $localize`:app-app_grid-text:Greater than`,
  greaterThanOrEqual: $localize`:app-app_grid-text:Greater than or equal`,
  inRange: $localize`:app-app_grid-text:In range`,
  contains: $localize`:app-app_grid-text:Contains`,
  notContains: $localize`:app-app_grid-text:Does not contain`,
  startsWith: $localize`:app-app_grid-text:Starts with`,
  endsWith: $localize`:app-app_grid-text:Ends with`,
  blank: $localize`:app-app_grid-text:Blank`,
  notBlank: $localize`:app-app_grid-text:Not blank`,
  before: $localize`:app-app_grid-text:Before`,
  after: $localize`:app-app_grid-text:After`,
  between: $localize`:app-app_grid-text:Between`,
  filterOoo: $localize`:app-app_grid-text:Filter...`,
  applyFilter: $localize`:app-app_grid-text:Apply`,
  clearFilter: $localize`:app-app_grid-text:Clear`,
  resetFilter: $localize`:app-app_grid-text:Reset`,
  true: $localize`:app-app_grid-text:True`,
  false: $localize`:app-app_grid-text:False`,
};
