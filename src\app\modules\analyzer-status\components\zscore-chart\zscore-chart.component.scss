.-translate-x {
    --tw-translate-x: -2rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
      skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  p.parenthesis::before {
    content: '\0028';
  }
  
  p.parenthesis::after {
    content: '\0029';
  }
  
  .table-fixed {
    .text-orient {
      writing-mode: vertical-rl;
      left: calc(50% - 0.5rem);
    }
  }
  