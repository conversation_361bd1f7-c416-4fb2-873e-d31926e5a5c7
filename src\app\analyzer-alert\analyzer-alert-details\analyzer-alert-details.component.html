<ng-template #analyzerAlertDialog let-parentPageCode="parentCode" let-pageCode="pageCode">
  <ng-container [appAclWrapper]="parentPageCode">
    <div>
      @for (analyzer of analyzers; track analyzer.referenceId) {
        <div class="flex justify-between gap-2">
          <app-analyzer-status-tile
            [analyzerInfo]="analyzer"
            [isAnalyzerAlert]="true"
            (redirectToXbarm)="closeDialog()"
            (troubleshootingEvent)="closeDialog()"
          ></app-analyzer-status-tile>
          <button
            mat-button
            class="!pt-3 text-[var(--blue-200)]"
            (click)="navigateToDetail(analyzer.modelCode, analyzer.serialNumber)"
            i18n
          >
            <mat-icon svgIcon="view_list_primary" class="!mt-[2px] !mr-2"></mat-icon>
            Details
          </button>
        </div>
        <hr />
      }
    </div>
  </ng-container>
</ng-template>
