import { Component, HostBinding } from '@angular/core';
import { SecurityService } from '../services/security.service';

@Component({
  selector: 'app-access-denied',
  templateUrl: './access-denied.component.html',
})
export class AccessDeniedComponent {
  @HostBinding('class') className = 'container mx-auto my-4';

  constructor(private readonly security: SecurityService) {}

  async backToLogin() {
    await this.security.logout();
  }
}
