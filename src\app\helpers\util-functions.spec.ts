import moment from 'moment/moment';
import { of } from 'rxjs';

import { CommonSystem } from '../interfaces/common-system';
import { EMPTY } from '../constant';
import { GlobalVariables } from './constant/global-variable';
import { UserStoreService } from '../services/user-store.service';
import { Utils } from './UtilFunctions';

describe('Utils', () => {
  it('test isNullOrEmptyString', () => {
    expect(Utils.isNullOrEmptyString('')).toBe(true);
    expect(Utils.isNullOrEmptyString('test')).toBe(false);
  });

  it('test isNullishNumber', () => {
    expect(Utils.isNullishNumber(undefined)).toBe(true);
    expect(Utils.isNullishNumber(0)).toBe(false);
  });

  it('test isNumber', () => {
    expect(Utils.isNumber(0)).toBe(true);
  });

  it('test isNullOrEmptyArray', () => {
    expect(Utils.isNullOrEmptyArray([])).toBe(true);
    expect(Utils.isNullOrEmptyArray([1, 2, 3])).toBe(false);
  });

  it('test deepArrayEqual', () => {
    expect(Utils.deepArrayEqual([1, 2, 3], [1, 2, 3])).toBe(true);
    expect(Utils.deepArrayEqual([1, 2, 3], [3, 2, 1])).toBe(true);
    expect(Utils.deepArrayEqual([1, 2, 3], [1, 2])).toBe(false);
  });

  it('test deepObjectEqual', () => {
    expect(
      Utils.deepObjectEqual(
        {
          a: 1,
          b: 2,
        },
        {
          a: 1,
          b: 2,
        },
      ),
    ).toBe(true);
    expect(
      Utils.deepObjectEqual(
        {
          a: 1,
          b: 2,
        },
        {
          b: 2,
          a: 1,
        },
      ),
    ).toBe(true);
    expect(
      Utils.deepObjectEqual(
        {
          a: 1,
          b: 2,
        },
        { a: 1 },
      ),
    ).toBe(false);
  });

  it('test hasNullField', () => {
    expect(
      Utils.hasNullField([
        {
          a: 1,
          b: null,
        },
        {
          c: 3,
          d: 4,
        },
      ]),
    ).toBe(true);
    expect(
      Utils.hasNullField([
        {
          a: 1,
          b: 2,
        },
        {
          c: 3,
          d: 4,
        },
      ]),
    ).toBe(false);
  });

  it('test sort', () => {
    const data = [{ a: 2 }, { a: 1 }, { a: 3 }];
    const sorted = Utils.sort(data, [
      {
        prop: 'a',
        direction: 1,
      },
    ]);

    expect(sorted).toEqual([{ a: 1 }, { a: 2 }, { a: 3 }]);
  });

  it('test truncateDecimalNumber', () => {
    expect(Utils.truncateDecimalNumber(1.2345, 2)).toBe('1.23');
  });

  it('should return correctly when decimal equal 0 ', () => {
    expect(Utils.truncateDecimalNumber(1.2345, 0)).toBe('1');
  });

  it('should return correctly for integer number ', () => {
    expect(Utils.truncateDecimalNumber(1, 2)).toBe('1.00');
  });

  it('should return Empty undefined value ', () => {
    expect(Utils.truncateDecimalNumber(undefined, 2)).toBe(EMPTY);
  });

  it('test getUniqueId', () => {
    const id = Utils.getUniqueId(3);

    expect(id.split('-').length).toBe(3);
  });

  it('test convertNaNValue on undefined number', () => {
    const value = Utils.convertNaNValue(undefined);

    expect(value).toBe(0);
  });

  it('test convertNaNValue on valid number', () => {
    const value = Utils.convertNaNValue(1);

    expect(value).toBe(1);
  });

  it('should split text into chunks without exceeding the limit', () => {
    const text = 'This is a sample text that needs to be split into chunks without exceeding the limit.';

    const limit = 20;

    const expectedChunks = [
      'This is a sample',
      'text that needs to',
      'be split into chunks',
      'without exceeding',
      'the limit.',
    ];

    const result = Utils.splitTextIntoChunks(text, limit);

    expect(result).toEqual(expectedChunks);
  });

  it('should handle text smaller than the limit', () => {
    const text = 'Short text.';

    const limit = 20;

    const expectedChunks = ['Short text.'];

    const result = Utils.splitTextIntoChunks(text, limit);

    expect(result).toEqual(expectedChunks);
  });

  it('should handle exact limit match', () => {
    const text = 'This is exactly twenty.';

    const limit = 20;

    const expectedChunks = ['This is exactly', 'twenty.'];

    const result = Utils.splitTextIntoChunks(text, limit);

    expect(result).toEqual(expectedChunks);
  });

  it('should handle trailing spaces correctly', () => {
    const text = 'This is a test text with trailing spaces at the end.   ';

    const limit = 25;

    const expectedChunks = ['This is a test text with', 'trailing spaces at the', 'end.'];

    const result = Utils.splitTextIntoChunks(text, limit);

    expect(result).toEqual(expectedChunks);
  });

  it('should handle consecutive spaces correctly', () => {
    const text = 'This  text   has multiple   spaces.';

    const limit = 10;

    const expectedChunks = ['This text', 'has', 'multiple', 'spaces.'];

    const result = Utils.splitTextIntoChunks(text, limit);

    expect(result).toEqual(expectedChunks);
  });

  it('should return true for an empty object', () => {
    const obj = {};

    const result = Utils.isEmptyObject(obj);

    expect(result).toBe(true);
  });

  it('should return false for a non-empty object', () => {
    const obj = { key: 'value' };

    const result = Utils.isEmptyObject(obj);

    expect(result).toBe(false);
  });

  it('should generate a unique ID with the specified number of parts', () => {
    const parts = 3;

    const uniqueId = Utils.getUniqueId(parts);

    const uniqueIdParts = uniqueId.split('-');

    expect(uniqueIdParts.length).toBe(parts);
  });

  it('should generate different IDs for consecutive calls', () => {
    const id1 = Utils.getUniqueId(3);

    const id2 = Utils.getUniqueId(3);

    expect(id1).not.toBe(id2);
  });

  it('should return true for a valid number', () => {
    const result = Utils.isNumber(123);

    expect(result).toBe(true);
  });

  it('should deep clone a simple object', () => {
    const obj = {
      a: 1,
      b: 2,
    };

    const clonedObj = Utils.deepClone(obj);

    expect(clonedObj).toEqual(obj);

    expect(clonedObj).not.toBe(obj);
  });

  it('should return true for null', () => {
    expect(Utils.isNull(null)).toBe(true);
  });

  it('should return false for undefined', () => {
    expect(Utils.isNull(undefined)).toBe(false);
  });

  const createMockUserStoreService = () => {
    return {
      loggedIn$: of(false),
      logout: async () => false,
      user: {
        userGuid: 'b699520d-b0e6-4c21-98ae-1d3ee55f49ef',
        firstName: 'Dev 6',
        lastName: 'RHQAD',
        email: '<EMAIL>',
        title: 'Developer',
        phoneNumber: '**********',
        language: {
          languageCode: 'en',
          languageName: 'English',
        },
        paceLicense: '',
        profilePicture: '',
        access: [
          {
            serviceCode: 'CQC',
            serviceName: 'CQC',
            serviceUrl: 'https://cqc.test.caresphereqc.sysmex.com/',
            roleName: 'RHQ Admin',
            sites: [
              {
                siteCode: '**********',
                siteName: 'SYSMEX AMERICA CTR FOR LEARN (other)',
                isDefault: false,
                isServiceL2: false,
              },
              {
                siteCode: 'IL0624',
                siteName: 'SYSMEX AMERICA CENTER FOR LEARNING',
                isDefault: true,
                isServiceL2: true,
              },
            ],
          },
        ],
        defaultSite: {
          siteGuid: '245bde4d-f57f-ea11-a812-000d3a102d7a',
          siteCode: 'IL0624',
          siteName: 'SYSMEX AMERICA CENTER FOR LEARNING',
          nickname: 'SACL',
          address: '1011 Woodlands Parkway',
          city: 'Vernon Hills',
          postalCode: '60061',
          state: 'IL',
          registrationCode: '1494',
        },
        isAcceptedTermsConditions: true,
        role: { roleName: '' },
        avatarUrl: 'blob:http://localhost:4200/82d46d89-10c2-4275-b0f5-8ca982929610',
      },
    };
  };

  describe('UtilFunctions.isServiceLevel2', () => {
    it('should return true when the user has access to a site with isServiceLevel2 set to true', () => {
      const userStoreService = createMockUserStoreService();

      // Mock the getDefaultSite method
      Utils.getDefaultSite = () => 'IL0624';

      // Util function can't use the angular TestBed module, so we need to cast it
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const result = Utils.isServiceLevel2(userStoreService as unknown as UserStoreService);

      expect(result).toBe(true);
    });

    it('should return false when the user has access to a site with isServiceLevel2 set to false', () => {
      const userStoreService = createMockUserStoreService();

      // Mock the getDefaultSite method
      Utils.getDefaultSite = () => '**********';

      // Util function can't use the angular TestBed module, so we need to cast it
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const result = Utils.isServiceLevel2(userStoreService as unknown as UserStoreService);

      expect(result).toBe(false);
    });

    it('should return false when the user does not have access to the site', () => {
      const userStoreService = createMockUserStoreService();

      // Mock the getDefaultSite method
      Utils.getDefaultSite = () => 'unknonwSiteCode';

      // Util function can't use the angular TestBed module, so we need to cast it
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const result = Utils.isServiceLevel2(userStoreService as unknown as UserStoreService);

      expect(result).toBe(false);
    });

    it('should return false if maxDecimalPlaces is undefined', () => {
      const res = Utils.isExceedDecimals(123.4567);

      expect(res).toBe(false);
    });

    it('should return false if value has fewer decimal places than maxDecimalPlaces', () => {
      const res = Utils.isExceedDecimals(123.45, 3);

      expect(res).toBe(false);
    });

    it('should return true if value has more decimal places than maxDecimalPlaces', () => {
      const res = Utils.isExceedDecimals(123.4567, 3);

      expect(res).toBe(true);
    });
  });

  describe('UtilFunctions.replaceText', () => {
    it('should replace text to string with data', () => {
      const textReplace = 'Hello, $(name)!';
      const data = { name: 'John' };

      const result = Utils.replaceText(textReplace, data);

      expect(result).toBe('Hello, John!');
    });
  });

  describe('roundedNumber', () => {
    // Add explicit tests for each statisticType
    it('should apply statisticType "mean"', () => {
      const result = Utils.roundedNumber(1.23, 2, EMPTY, 'mean');

      expect(result).toBe('1.230');
    });

    it('should apply statisticType "sd"', () => {
      const result = Utils.roundedNumber(1.23, 2, EMPTY, 'sd');

      expect(result).toBe('1.2300');
    });

    it('should apply statisticType "cv"', () => {
      const result = Utils.roundedNumber(1.23, 2, EMPTY, 'cv');

      expect(result).toBe('1.2');
    });

    it('should apply statisticType "biasPercent"', () => {
      const result = Utils.roundedNumber(1.23, 2, EMPTY, 'biasPercent');

      expect(result).toBe('1.23');
    });

    it('should apply statisticType "sdi"', () => {
      const result = Utils.roundedNumber(1.23, 2, EMPTY, 'sdi');

      expect(result).toBe('1.230');
    });

    it('should apply statisticType "zscore"', () => {
      const result = Utils.roundedNumber(1.23, 2, EMPTY, 'zscore');

      expect(result).toBe('1.230');
    });

    it('should apply default behavior when statisticType is not recognized', () => {
      // @ts-ignore - Testing with invalid type
      expect(Utils.roundedNumber(1.23, 2, EMPTY, 'unknown')).toBe('1.23');
    });
  });

  it('should return an empty array when input array is empty', () => {
    const result = Utils.getFirstAndBlocksOfFour([]);

    expect(result).toEqual([]);
  });

  it('should return the first element and blocks of four elements', () => {
    const inputArray = [1, 2, 3, 4, 5, 6, 7, 8, 9];
    const expectedOutput = [[1], [2, 3, 4, 5], [6, 7, 8, 9]];
    const result = Utils.getFirstAndBlocksOfFour(inputArray);

    expect(result).toEqual(expectedOutput);
  });

  it('should handle arrays with less than five elements', () => {
    const inputArray = [1, 2, 3];
    const expectedOutput = [[1], [2, 3]];
    const result = Utils.getFirstAndBlocksOfFour(inputArray);

    expect(result).toEqual(expectedOutput);
  });

  it('should handle arrays with exactly five elements', () => {
    const inputArray = [1, 2, 3, 4, 5];
    const expectedOutput = [[1], [2, 3, 4, 5]];
    const result = Utils.getFirstAndBlocksOfFour(inputArray);

    expect(result).toEqual(expectedOutput);
  });

  it('should handle arrays with exactly nine elements', () => {
    const inputArray = [1, 2, 3, 4, 5, 6, 7, 8, 9];
    const expectedOutput = [[1], [2, 3, 4, 5], [6, 7, 8, 9]];
    const result = Utils.getFirstAndBlocksOfFour(inputArray);

    expect(result).toEqual(expectedOutput);
  });

  it('should handle arrays with more than nine elements', () => {
    const inputArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13];
    const expectedOutput = [[1], [2, 3, 4, 5], [6, 7, 8, 9], [10, 11, 12, 13]];
    const result = Utils.getFirstAndBlocksOfFour(inputArray);

    expect(result).toEqual(expectedOutput);
  });

  it('should memoize function results', () => {
    const testFunction = (x: number) => x * 2;
    const memoizedTestFunction = Utils.memoize(testFunction);

    const firstCallResult = memoizedTestFunction(2);
    const secondCallResult = memoizedTestFunction(2);

    expect(firstCallResult).toEqual(4);
    expect(secondCallResult).toEqual(4);
    expect(firstCallResult).toEqual(secondCallResult);
  });

  it('should call the original function only once for the same arguments', () => {
    let callCount = 0;
    const testFunction = (x: number) => {
      callCount++;

      return x * 2;
    };
    const memoizedTestFunction = Utils.memoize(testFunction);

    memoizedTestFunction(3);
    memoizedTestFunction(3);

    expect(callCount).toEqual(1);
  });

  it('should handle multiple arguments correctly', () => {
    const testFunction = (x: number, y: number) => x + y;
    const memoizedTestFunction = Utils.memoize(testFunction);

    const result1 = memoizedTestFunction(2, 3);
    const result2 = memoizedTestFunction(2, 3);
    const result3 = memoizedTestFunction(3, 2);

    expect(result1).toEqual(5);
    expect(result2).toEqual(5);
    expect(result3).toEqual(5);
    expect(result1).toEqual(result2);
    expect(result2).toEqual(result3);
  });

  it('should differentiate between different argument sets', () => {
    const testFunction = (x: number) => x * 2;
    const memoizedTestFunction = Utils.memoize(testFunction);

    const resultFor2 = memoizedTestFunction(2);
    const resultFor3 = memoizedTestFunction(3);

    expect(resultFor2).toEqual(4);
    expect(resultFor3).toEqual(6);
    expect(resultFor2).not.toEqual(resultFor3);
  });

  it('should return correct properties for "analyzer" chart type', () => {
    const properties = Utils.getChartProperties('analyzer', 2, 1);

    expect(properties).toEqual({
      icon: 'square',
      color: '#DBB62E',
    });
  });

  it('should return default properties when index is out of bounds for "analyzer"', () => {
    const properties = Utils.getChartProperties('analyzer', 2, 10);

    expect(properties).toEqual({
      icon: 'square',
      color: '#864B99',
    });
  });

  it('should return correct properties for "level" chart type', () => {
    const properties = Utils.getChartProperties('level', 1, 0);

    expect(properties).toEqual({
      icon: 'circle',
      color: 'var(--red-100)',
    });
  });

  it('should return default properties when level is out of bounds for "level"', () => {
    const properties = Utils.getChartProperties('level', 0, 10);

    expect(properties).toEqual({
      icon: EMPTY,
      color: EMPTY,
    });
  });

  it('should memoize results to avoid recalculating for the same arguments', () => {
    const firstCall = Utils.getChartProperties('analyzer', 1, 2);
    const secondCall = Utils.getChartProperties('analyzer', 1, 2);

    expect(firstCall).toEqual(secondCall);
  });

  it('should return the correct month for getLimitedFromEndDate', () => {
    const endDate = new Date();

    const limitMonthFromEndDate = 6;

    const maximumBackwardYear = 2;

    const result = Utils.getLimitedDateFromEndDate(endDate, limitMonthFromEndDate, maximumBackwardYear);

    const diffInDays = moment(endDate).diff(result, 'months');

    expect(diffInDays).toEqual(limitMonthFromEndDate);
  });

  it('should return the correct backward for getLimitedFromEndDate', () => {
    const endDate = new Date('2021-01-01');

    const limitMonthFromEndDate = 6;

    const maximumBackwardYear = 2;

    const result = Utils.getLimitedDateFromEndDate(endDate, limitMonthFromEndDate, maximumBackwardYear);

    const diffInYears = moment(new Date()).diff(result, 'years');

    expect(diffInYears).toEqual(maximumBackwardYear);
  });

  it('should return the current date for getMaxDateFromStartDate', () => {
    const startDate = new Date();

    const limitMonthFromEndDate = 5;

    const result = Utils.getLimitedDateFromStartDate(startDate, limitMonthFromEndDate);

    const diffInDay = moment(startDate).diff(result, 'days');

    expect(diffInDay).toEqual(0);
  });

  it('should return the correct date for getMaxDateFromStartDate', () => {
    const startDate = new Date('2023-10-21');

    const limitMonthFromEndDate = 5;

    const result = Utils.getLimitedDateFromStartDate(startDate, limitMonthFromEndDate);

    const diffInMonths = moment(result).diff(startDate, 'months');

    expect(diffInMonths).toEqual(limitMonthFromEndDate);
  });

  it('should return the correct date when limitMonthFromEndDate is within maximumBackwardYear', () => {
    // Create a specific date with a fixed timezone representation
    const endDate = new Date('2023-10-21T00:00:00.000Z'); // Use ISO string format
    const limitMonthFromEndDate = 6;
    const maximumBackwardYear = 2;

    // Mock moment's behavior to ensure consistent results
    const mockMoment = {
      subtract: jest.fn().mockReturnThis(),
      startOf: jest.fn().mockReturnThis(),
      toDate: jest.fn().mockReturnValue(new Date('2023-04-21T00:00:00.000Z')),
    };

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
    jest.spyOn(moment, 'max').mockReturnValue(mockMoment as any);
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
    jest.spyOn(moment.prototype, 'subtract').mockReturnValue(mockMoment as any);

    const result = Utils.getLimitedDateFromEndDate(endDate, limitMonthFromEndDate, maximumBackwardYear);

    // Compare by ISO date string to avoid timezone issues
    expect(result.toISOString().split('T')[0]).toBe('2023-04-21');

    // Verify the mocks were called correctly
    expect(moment.max).toHaveBeenCalled();
    expect(mockMoment.startOf).toHaveBeenCalledWith('day');
  });

  it('should return the correct date when limitMonthFromEndDate exceeds maximumBackwardYear', () => {
    const endDate = new Date('2023-10-21');
    const limitMonthFromEndDate = 30; // 2.5 years
    const maximumBackwardYear = 2;

    const result = Utils.getLimitedDateFromEndDate(endDate, limitMonthFromEndDate, maximumBackwardYear);

    const expectedDate = moment().subtract(maximumBackwardYear, 'years').startOf('day').toDate();

    expect(result).toEqual(expectedDate);
  });

  it('should return the correct date when endDate is today', () => {
    const endDate = new Date();
    const limitMonthFromEndDate = 6;
    const maximumBackwardYear = 2;

    const result = Utils.getLimitedDateFromEndDate(endDate, limitMonthFromEndDate, maximumBackwardYear);

    const expectedDate = moment(endDate).subtract(limitMonthFromEndDate, 'months').startOf('day').toDate();

    expect(result).toEqual(expectedDate);
  });

  it('should return the current date if the limit date is in the future', () => {
    const startDate = new Date();
    const limitMonthFromStartDate = 6;

    const result = Utils.getLimitedDateFromStartDate(startDate, limitMonthFromStartDate);

    const currentTime = moment().endOf('day').toDate();

    expect(result).toEqual(currentTime);
  });

  it('should return the limit date if it is in the past', () => {
    const startDate = new Date('2020-01-01');
    const limitMonthFromStartDate = 6;

    const result = Utils.getLimitedDateFromStartDate(startDate, limitMonthFromStartDate);

    const expectedDate = moment(startDate).add(limitMonthFromStartDate, 'months').endOf('day').toDate();

    expect(result).toEqual(expectedDate);
  });

  it('should return the limit date if it is today', () => {
    const startDate = moment().subtract(6, 'months').toDate();
    const limitMonthFromStartDate = 6;

    const result = Utils.getLimitedDateFromStartDate(startDate, limitMonthFromStartDate);

    const expectedDate = moment(startDate).add(limitMonthFromStartDate, 'months').endOf('day').toDate();

    expect(result).toEqual(expectedDate);
  });

  it('should handle leap years correctly', () => {
    const startDate = new Date('2020-02-29');
    const limitMonthFromStartDate = 12;

    const result = Utils.getLimitedDateFromStartDate(startDate, limitMonthFromStartDate);

    const expectedDate = moment(startDate).add(limitMonthFromStartDate, 'months').endOf('day').toDate();

    expect(result).toEqual(expectedDate);
  });

  it('should handle negative limit months correctly', () => {
    const startDate = new Date('2023-10-21');
    const limitMonthFromStartDate = -6;

    const result = Utils.getLimitedDateFromStartDate(startDate, limitMonthFromStartDate);

    const expectedDate = moment(startDate).add(limitMonthFromStartDate, 'months').endOf('day').toDate();

    expect(result).toEqual(expectedDate);
  });

  it('should return an empty array when there are no changes', () => {
    const previousState = {
      a: 1,
      b: 2,
      c: 3,
    };
    const currentState = {
      a: 1,
      b: 2,
      c: 3,
    };

    const result = Utils.getChangedProperties(previousState, currentState);

    expect(result).toEqual([]);
  });

  it('should return the changed properties', () => {
    const previousState = {
      a: 1,
      b: 2,
      c: 3,
    };
    const currentState = {
      a: 1,
      b: 3,
      c: 3,
    };

    const result = Utils.getChangedProperties(previousState, currentState);

    expect(result).toEqual(['b']);
  });

  it('should return all properties if all are changed', () => {
    const previousState = {
      a: 1,
      b: 2,
      c: 3,
    };
    const currentState = {
      a: 4,
      b: 5,
      c: 6,
    };

    const result = Utils.getChangedProperties(previousState, currentState);

    expect(result).toEqual(['a', 'b', 'c']);
  });

  it('should handle empty objects', () => {
    const previousState = {};
    const currentState = {};

    const result = Utils.getChangedProperties(previousState, currentState);

    expect(result).toEqual([]);
  });

  it('should handle added properties in currentState', () => {
    const previousState = { a: 1 };
    const currentState = {
      a: 1,
      b: 2,
    };

    const result = Utils.getChangedProperties(previousState, currentState);

    expect(result).toEqual(['b']);
  });

  it('should handle removed properties in currentState', () => {
    const previousState = {
      a: 1,
      b: 2,
    };
    const currentState = { a: 1 };

    const result = Utils.getChangedProperties(previousState, currentState);

    expect(result).toEqual([]);
  });

  it('should handle nested objects', () => {
    const previousState = {
      a: { x: 1 },
      b: 2,
    };
    const currentState = {
      a: { x: 2 },
      b: 2,
    };

    const result = Utils.getChangedProperties(previousState, currentState);

    expect(result).toEqual(['a']);
  });

  describe('getPagingData', () => {
    const items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

    it('should return the correct data for the first page', () => {
      const pageIndex = 1;
      const numberOfItems = 3;

      const result = Utils.getPagingData(items, pageIndex, numberOfItems);

      expect(result).toEqual([1, 2, 3]);
    });

    it('should return the remaining items when data is less than page size', () => {
      const pageIndex = 4;
      const numberOfItems = 3;

      const result = Utils.getPagingData(items, pageIndex, numberOfItems);

      expect(result).toEqual([10]);
    });

    it('should return an empty array when pageIndex is out of range', () => {
      const pageIndex = 5;
      const numberOfItems = 3;

      const result = Utils.getPagingData(items, pageIndex, numberOfItems);

      expect(result).toEqual([]);
    });
  });

  it('should return the default value if the key is not found in storedCommonSystem', () => {
    jest.spyOn(GlobalVariables, 'getCommonSystems').mockReturnValue(undefined);

    const result = Utils.getCommonSystemValue('someKey', 'defaultValue');

    expect(result).toBe('defaultValue');
  });

  it('should return the number value if the key is found and the type is number', () => {
    const mockStoredCommonSystem: CommonSystem = {
      name: 'someKey',
      value: '42',
      type: 'number',
    };

    jest.spyOn(GlobalVariables, 'getCommonSystems').mockReturnValue(mockStoredCommonSystem);

    const result = Utils.getCommonSystemValue('someKey', 0);

    expect(result).toBe(42);
  });

  it('should return the boolean value if the key is found and the type is boolean', () => {
    const mockStoredCommonSystem: CommonSystem = {
      name: 'someKey',
      value: 'true',
      type: 'boolean',
    };

    jest.spyOn(GlobalVariables, 'getCommonSystems').mockReturnValue(mockStoredCommonSystem);

    const result = Utils.getCommonSystemValue('someKey', false);

    expect(result).toBe(true);
  });

  it('should return the string value if the key is found and the type is string', () => {
    const mockStoredCommonSystem: CommonSystem = {
      name: 'someKey',
      value: 'someValue',
      type: 'string',
    };

    jest.spyOn(GlobalVariables, 'getCommonSystems').mockReturnValue(mockStoredCommonSystem);

    const result = Utils.getCommonSystemValue('someKey', '');

    expect(result).toBe('someValue');
  });

  describe('addTimeZoneCharacter', () => {
    it('should add "Z" to the end of the date string if not present', () => {
      const date = '2023-10-01T12:00:00';
      const result = Utils.addTimeZoneCharacter(date);

      expect(result).toBe('2023-10-01T12:00:00Z');
    });

    it('should not add "Z" if it is already present at the end of the date string', () => {
      const date = '2023-10-01T12:00:00Z';
      const result = Utils.addTimeZoneCharacter(date);

      expect(result).toBe('2023-10-01T12:00:00Z');
    });

    it('should handle empty date string', () => {
      const result = Utils.addTimeZoneCharacter(EMPTY);

      expect(result).toBe(EMPTY);
    });
  });

  describe('isObjectsEqualOnKeys', () => {
    it('should return true for objects with equal values on specified keys', () => {
      const obj1 = {
        id: 1,
        name: 'John',
        age: 25,
      };
      const obj2 = {
        id: 1,
        name: 'John',
        city: 'New York',
      };

      const result = Utils.isObjectsEqualOnKeys(obj1, obj2, ['id', 'name']);

      expect(result).toBe(true);
    });

    it('should return false for objects with different values on specified keys', () => {
      const obj1 = {
        id: 1,
        name: 'John',
        age: 25,
      };
      const obj2 = {
        id: 2,
        name: 'Doe',
        city: 'New York',
      };

      const result = Utils.isObjectsEqualOnKeys(obj1, obj2, ['id', 'name']);

      expect(result).toBe(false);
    });

    it('should handle missing keys in one object and return false', () => {
      const obj1 = {
        id: 1,
        name: 'John',
      };
      const obj2 = { id: 1 };

      const result = Utils.isObjectsEqualOnKeys(obj1, obj2, ['id', 'name']);

      expect(result).toBe(false);
    });

    it('should handle undefined objects and return false', () => {
      const obj1 = {
        id: 1,
        name: 'John',
      };
      const obj2 = undefined;

      const result = Utils.isObjectsEqualOnKeys(obj1, obj2, ['id', 'name']);

      expect(result).toBe(false);
    });
  });

  describe('isArraysEqualOnKeys', () => {
    it('should return true for arrays with equal objects on specified keys', () => {
      const array1 = [
        {
          id: 1,
          name: 'John',
          age: 25,
        },
        {
          id: 2,
          name: 'Doe',
          age: 30,
        },
      ];
      const array2 = [
        {
          id: 2,
          name: 'Doe',
          city: 'New York',
        },
        {
          id: 1,
          name: 'John',
          city: 'New York',
        },
      ];

      jest.spyOn(Utils, 'sort');

      const result = Utils.isArraysEqualOnKeys(array1, array2, ['id', 'name'], 'id');

      expect(result).toBe(true);
      expect(Utils.sort).toHaveBeenCalledTimes(2);
    });

    it('should return false for arrays with different objects on specified keys', () => {
      const array1 = [
        {
          id: 1,
          name: 'John',
          age: 25,
        },
        {
          id: 2,
          name: 'Doe',
          age: 30,
        },
      ];
      const array2 = [
        {
          id: 1,
          name: 'Jane',
          city: 'New York',
        },
        {
          id: 2,
          name: 'Doe',
          city: 'Los Angeles',
        },
      ];

      const result = Utils.isArraysEqualOnKeys(array1, array2, ['id', 'name']);

      expect(result).toBe(false);
    });

    it('should return false for arrays of different lengths', () => {
      const array1 = [
        {
          id: 1,
          name: 'John',
          age: 25,
        },
        {
          id: 2,
          name: 'Doe',
          age: 30,
        },
      ];
      const array2 = [
        {
          id: 1,
          name: 'John',
          city: 'New York',
        },
      ];

      const result = Utils.isArraysEqualOnKeys(array1, array2, ['id', 'name']);

      expect(result).toBe(false);
    });
  });

  describe('simplifyLanguageCode', () => {
    it('should return "en" when langCode is null', () => {
      const result = Utils.simplifyLanguageCode(null);

      expect(result).toBe('en');
    });

    it('should return the language code without region', () => {
      const result = Utils.simplifyLanguageCode('zh-CN');

      expect(result).toBe('zh');
    });

    it('should return the language code as is if there is no region', () => {
      const result = Utils.simplifyLanguageCode('fr');

      expect(result).toBe('fr');
    });
  });
});
