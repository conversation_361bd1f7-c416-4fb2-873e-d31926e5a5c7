export interface MultiplierAndClinicalTEAs {
  parameterCode: string;

  clinicalTEA: number | string;

  multiplier: number | string;
}

export interface BalanceTEAs {
  parameterCode: string;

  balanceTEA: number | string;
}

export interface UpperAndLowerLimits {
  parameterCode: string;

  upperLimit: number | string;

  lowerLimit: number | string;
}

export interface Rule12To13ParamSetting {
  multiplierAndClinicalTEAs: MultiplierAndClinicalTEAs[];

  balanceTEAs: BalanceTEAs[];

  upperAndLowerLimits: UpperAndLowerLimits[];
}
