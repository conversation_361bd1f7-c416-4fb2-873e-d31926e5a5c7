import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { debounceTime, timer } from 'rxjs';

import { EMPTY, THREE_HUNDRED_MILLISECONDS } from '../constant';
import { MatTooltip } from '@angular/material/tooltip';

@Directive({
  selector: '[appTruncateText]',
  standalone: true,
  providers: [MatTooltip],
})
export class TruncateTextDirective {
  @Input() set disabled(disabled: boolean | null) {
    if (disabled) {
      this.tooltip.message = EMPTY;

      return;
    }

    this.checkTextOverflow();
  }

  elementTruncate!: HTMLElement;

  element: HTMLElement;

  constructor(
    private readonly el: ElementRef<HTMLElement>,
    private readonly tooltip: MatTooltip,
  ) {
    this.element = this.el.nativeElement;

    timer(200).subscribe(() => {
      this.tooltip.position = 'above';

      this.getElementTruncate();
      this.checkTextOverflow();
    });
  }

  // Listen to events or add custom logic to trigger the tooltip
  @HostListener('mouseenter') onMouseEnter() {
    this.tooltip.show(); // Show tooltip on mouse enter
  }

  @HostListener('mouseleave') onMouseLeave() {
    this.tooltip.hide(); // Hide tooltip on mouse leave
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    timer(0)
      .pipe(debounceTime(THREE_HUNDRED_MILLISECONDS))
      .subscribe(() => this.checkTextOverflow());
  }

  getElementTruncate(): void {
    if (!this.element) {
      return;
    }

    const getElementTruncate: Record<string, string> = {
      'mat-slide-toggle': '.mdc-label',
      button: '.mdc-button__label',
    };

    const classElement = getElementTruncate[this.element.tagName.toLowerCase()];
    const elementTruncate = this.element.querySelector<HTMLElement>(classElement);

    if (!elementTruncate) {
      return;
    }

    this.elementTruncate = elementTruncate;
  }

  checkTextOverflow(): void {
    if (!this.elementTruncate) {
      return;
    }

    const isTruncate = this.element.scrollWidth > this.element.clientWidth;

    this.tooltip.message = isTruncate ? this.element.innerText : EMPTY;
  }

  getLineHeightInPixels(element: HTMLElement) {
    const computedStyle = window.getComputedStyle(element);
    const lineHeight = computedStyle.lineHeight;

    if (lineHeight === 'normal') {
      const fontSize = parseFloat(computedStyle.fontSize);

      return fontSize * 1.2;
    }

    if (lineHeight.includes('px')) {
      return parseFloat(lineHeight);
    }

    const fontSize = parseFloat(computedStyle.fontSize);

    return parseFloat(lineHeight) * fontSize;
  }
}
