import { WeekDayValue } from '../interfaces/user';

export function readDaysOfWeek(selectedDays: number): WeekDayValue[] {
  const resDays: WeekDayValue[] = [];
  const intDays: Record<string, number> = {
    MONDAY: 0b0000001, //  1
    TUESDAY: 0b0000010, //  2
    WEDNESDAY: 0b0000100, //  4
    THURSDAY: 0b0001000, //  8
    FRIDAY: 0b0010000, // 16
    SATURDAY: 0b0100000, // 32
    SUNDAY: 0b1000000, // 64
  };
  const weekDays = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'];

  weekDays.forEach((day, index) => {
    resDays.push({
      weekday: weekDays[index],
      value: intDays[day],
      isSelected: (selectedDays & intDays[day]) > 0,
    });
  });

  return resDays;
}

export function writeDaysOfWeek(selectedDays: WeekDayValue[]): number {
  let resDays = 0;

  selectedDays.forEach((weekDays: WeekDayValue) => {
    if (weekDays.isSelected) {
      resDays += weekDays.value;
    }
  });

  return resDays;
}
