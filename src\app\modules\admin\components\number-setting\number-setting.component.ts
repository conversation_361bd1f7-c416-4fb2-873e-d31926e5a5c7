import { Component, Input, OnInit, QueryList, TemplateRef, ViewChild, ViewChildren } from '@angular/core';

import { CommonModule } from '@angular/common';
import { DesignSystemModule } from '@sysmex/design-system';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabsModule } from '@angular/material/tabs';
import { NgModel } from '@angular/forms';
import { Subject } from 'rxjs';

import { EventFocusRuleComponent, EventUpdateNumberSetting } from 'src/app/models/event-params';
import { MAX_DECIMAL_PLACES, NUMBER_SETTINGS, NUMBER_SETTINGS_ID, NUMBER_SETTING_CONSTRAINTS } from '../../constants';
import { NumberSetting, NumberSettingInput } from '../../interfaces/number-setting';

import { AclModule } from 'src/app/directives/acl/acl.module';
import { DialogService } from 'src/app/services/dialog.service';
import { EMPTY } from 'src/app/constant';
import { ERROR_CODE } from 'src/app/enums/errors';
import { ErrorMessage } from 'src/app/helpers/ErrorMessage';
import { EventAbsComponent } from 'src/app/services/event-communicate/component-evt-base';
import { EventParam } from 'src/app/models/event-items';
import { GlobalVariables } from 'src/app/helpers/constant/global-variable';
import { InputDirectivesModule } from 'src/app/directives/input/input-directives.module';
import { NumberInputConstraints } from '../../interfaces/ag-grid-editable-cell';
import { NumberSettingAuditTrailComponent }
  from './components/number-setting-audit-trail/number-setting-audit-trail.component';
import { RuleConfigGridInputValidator } from '../../custom-validator/rule-config-grid-input-validator';
import { RuleConfigurationAuditTrailDialogParams } from '../../interfaces/rule-configuration-audit-trail';
import { RuleConfigurationService } from '../../services/rule-configuration.service';
import { SnackBarService } from 'src/app/services/snackbar.service';
import { Utils } from 'src/app/helpers/UtilFunctions';

@Component({
  selector: 'app-number-setting',
  templateUrl: './number-setting.component.html',
  standalone: true,
  imports: [
    CommonModule,
    AclModule,
    DesignSystemModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatSlideToggleModule,
    MatTabsModule,
    MatButtonModule,
    RuleConfigGridInputValidator,
    InputDirectivesModule,
    NumberSettingAuditTrailComponent,
  ],
})
export class NumberSettingComponent extends EventAbsComponent implements OnInit {
  @Input() ruleCode: string = EMPTY;

  @ViewChild('saveConfirmDialogContent') saveConfirmDialogContent: TemplateRef<unknown> | null = null;

  @ViewChildren(NgModel) inputModels!: QueryList<NgModel>;

  isEditMode = false;

  inputValues: NumberSettingInput[] = [];

  inputValuesBackup: NumberSettingInput[] = [];

  hasRecentRuns = false;

  generalErrorMessages = {
    invalidFormat:
      ErrorMessage.getMessage(ERROR_CODE.EM005) +
      ErrorMessage.getMessage(ERROR_CODE.EM012, MAX_DECIMAL_PLACES.toString()),
    required: ErrorMessage.getMessage(ERROR_CODE.EM002),
    rule7CrossFieldError: ErrorMessage.getMessage(
      ERROR_CODE.EM018,
      NUMBER_SETTINGS.find((setting) => setting.id == NUMBER_SETTINGS_ID.troubleshootingEvents)?.label ?? EMPTY,
      NUMBER_SETTINGS.find((setting) => setting.id == NUMBER_SETTINGS_ID.consecutiveControlRunSets)?.label ?? EMPTY,
    ),
  };

  numberErrorMessages!: Record<string, string>;

  percentErrorMessages!: Record<string, string>;

  generalInputConstraints: NumberInputConstraints = {
    allowDecimals: false,
    minIncluded: true,
    maxIncluded: true,
  };

  numberInputConstraints!: NumberInputConstraints;

  percentInputConstraints!: NumberInputConstraints;

  openAuditTrailDialog$ = new Subject<RuleConfigurationAuditTrailDialogParams>();

  constructor(
    private readonly matDialog: MatDialog,
    private readonly dialogService: DialogService,
    private readonly snackBarService: SnackBarService,
    private readonly ruleConfigService: RuleConfigurationService,
  ) {
    super();
  }

  onInit() {
    this.subscribeEvent(EventFocusRuleComponent.CLASS_NAME);

    this.fetchData();

    this.initProps();
  }

  onDestroy(): void {
    this.subscription.unsubscribe();
  }

  onEvent(params: EventParam): void {
    params.objs.forEach(async (obj) => {
      if (!obj) {
        return;
      }

      if (obj instanceof EventFocusRuleComponent) {
        if (obj.focusIdentifierType === 'view') {
          this.isEditMode = false;
        }
      }
    });
  }

  initProps() {
    this.numberErrorMessages = Utils.deepClone(this.generalErrorMessages);

    this.percentErrorMessages = Utils.deepClone(this.generalErrorMessages);

    this.numberInputConstraints = Utils.deepClone(this.generalInputConstraints);

    this.numberInputConstraints.min = NUMBER_SETTING_CONSTRAINTS.NUMBER_MIN;

    this.numberInputConstraints.max = NUMBER_SETTING_CONSTRAINTS.NUMBER_MAX;

    this.percentInputConstraints = Utils.deepClone(this.generalInputConstraints);

    this.percentInputConstraints.allowDecimals = true;

    this.percentInputConstraints.maxDecimals = NUMBER_SETTING_CONSTRAINTS.MAX_DECIMALS;

    this.percentInputConstraints.min = NUMBER_SETTING_CONSTRAINTS.PERCENT_MIN;

    this.percentInputConstraints.max = NUMBER_SETTING_CONSTRAINTS.PERCENT_MAX;

    this.numberErrorMessages['rangeError'] = ErrorMessage.getMessage(
      ERROR_CODE.EM001,
      this.numberInputConstraints.min.toString(),
      this.numberInputConstraints.max.toString(),
    );

    const maximumDecimalsMessage = ErrorMessage.getMessage(
      ERROR_CODE.EM012,
      this.percentInputConstraints.maxDecimals.toString(),
    );

    this.percentErrorMessages['rangeError'] =
      ErrorMessage.getMessage(
        ERROR_CODE.EM001,
        this.percentInputConstraints.min.toString(),
        this.percentInputConstraints.max.toString(),
      ) + maximumDecimalsMessage;
  }

  fetchData() {
    this.ruleConfigService.getNumberSettings(this.ruleCode).subscribe((data) => {
      this.formatResponse(data);
    });
  }

  createSettingInputNumber(
    inputNumSetting: Partial<NumberSettingInput>,
    numSetting: NumberSetting,
  ): NumberSettingInput {
    return {
      id: inputNumSetting.id!,
      label: inputNumSetting.label!,
      type: inputNumSetting.type!,
      value: numSetting.settingValue!,
      order: inputNumSetting.order!,
      min:
        inputNumSetting.type === 'number'
          ? (this.numberInputConstraints.min ?? null)
          : (this.percentInputConstraints.min ?? null),
      max:
        inputNumSetting.type === 'number'
          ? (this.numberInputConstraints.max ?? null)
          : (this.percentInputConstraints.max ?? null),
    };
  }

  formatResponse(response: NumberSetting[]): void {
    if (response) {
      this.inputValues = response
        .reduce((settings: NumberSettingInput[], numSetting: NumberSetting) => {
          const inputNumSetting = NUMBER_SETTINGS.find(
            (setting) => setting.id === numSetting.settingKey && this.ruleCode === setting.ruleCode,
          );

          if (inputNumSetting) {
            settings.push(this.createSettingInputNumber(inputNumSetting, numSetting));
          }

          return settings;
        }, [])
        .sort((input1: NumberSettingInput, input2: NumberSettingInput) => input1.order - input2.order);

      this.inputValues = this.inputValues.map((inputValue) => this.addDecimalZero(inputValue));

      this.inputValuesBackup = Utils.deepClone(this.inputValues);

      this.hasRecentRuns = this.inputValues.some(({ id }) => id === NUMBER_SETTINGS_ID.recentRuns);
    }
  }

  addDecimalZero(numberSetting: NumberSettingInput): NumberSettingInput {
    return {
      ...numberSetting,
      ...(numberSetting.type !== 'number' && {
        value: Utils.roundedNumber(Number(numberSetting.value), MAX_DECIMAL_PLACES),
      }),
    };
  }

  openSaveConfirmDialog() {
    const dialogConfig = {
      data: {
        title: $localize`:app-app_dialog-title:Save Changes`,
        content: this.saveConfirmDialogContent,
        actions: [
          {
            label: $localize`:@@app-app_button-cancel:Cancel`,
            color: 'ghost',
            close: true,
            onClick: () => {
              this.matDialog.closeAll();
            },
          },
          {
            label: $localize`:@@app-app_button-save:Save`,
            color: 'primary',
            close: true,
            onClick: () => {
              this.saveData();

              this.matDialog.closeAll();
            },
          },
        ],
        showCloseIcon: true,
      },
    };

    this.dialogService.openDialog$.next(dialogConfig);
  }

  toggleEditMode(isEnable: boolean) {
    this.isEditMode = isEnable;

    GlobalVariables.fireEvent(
      new EventParam(new EventFocusRuleComponent(this.isEditMode ? 'numberSetting' : 'view', this.ruleCode)),
    );
  }

  onCancel() {
    this.toggleEditMode(false);

    this.inputValues = Utils.deepClone(this.inputValuesBackup);
  }

  onSave() {
    const dataUnChanged = Utils.isArraysEqualOnKeys(this.inputValues, this.inputValuesBackup, ['value']);

    if (this.isAllInputValid() && !dataUnChanged) {
      this.openSaveConfirmDialog();
    } else {
      if (dataUnChanged) {
        this.snackBarService.displayMsg(ErrorMessage.getMessage(ERROR_CODE.CUS003), {
          type: 'error',
        });

        return;
      }

      this.snackBarService.displayMsg(
        $localize`:app-app_message-text:The system cannot perform this action. Please try again.`,
        {
          type: 'error',
        },
      );
    }
  }

  saveData() {
    const dataToSave = this.getDataToSave();

    this.ruleConfigService.updateNumberSettings(this.ruleCode, dataToSave).subscribe({
      next: () => {
        ErrorMessage.getMessage(ERROR_CODE.SM001);
        this.snackBarService.displayMsg(ErrorMessage.getMessage(ERROR_CODE.SM001), {
          type: 'success',
        });

        this.toggleEditMode(false);

        this.inputValues = this.inputValues.map((inputValue) => this.addDecimalZero(inputValue));
        this.inputValuesBackup = Utils.deepClone(this.inputValues);

        GlobalVariables.fireEvent(new EventParam(new EventUpdateNumberSetting(this.ruleCode)));
      },
      error: () => {
        this.snackBarService.displayMsg(
          $localize`:app-app_message-text:The system cannot perform this action. Please try again.`,
          {
            type: 'error',
          },
        );
      },
    });
  }

  getDataToSave() {
    const values = [];

    for (const inputValue of this.inputValues) {
      values.push({
        settingKey: inputValue.id,
        settingValue: Number(inputValue.value),
      });
    }

    return values;
  }

  isAllInputValid(): boolean {
    return !this.inputModels.some((inputModel) => inputModel.invalid ?? false);
  }

  markAllInputForCheck() {
    this.inputModels.forEach((inputModel) => {
      inputModel.control.markAsTouched();

      inputModel.control.markAsDirty();

      inputModel.control.updateValueAndValidity();
    });
  }

  onOpenAuditTrail(): void {
    if (this.ruleCode) {
      this.openAuditTrailDialog$.next({
        logKey: this.ruleCode,
      });
    }
  }
}
