import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AuthService } from '@auth0/auth0-angular';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Router } from '@angular/router';
import { of } from 'rxjs';

import { MOCK_ANALYZER_IN, MOCK_ANALYZER_OUT } from '../../modules/analyzer-status/mocks/analyzer';
import { AUTH0_CLIENT } from '../analyzer-alert.component.spec';
import { AnalyzerAlertDetailsComponent } from './analyzer-alert-details.component';
import { AnalyzerService } from '../../modules/analyzer-status/services/analyzer.service';
import { DialogService } from '../../services/dialog.service';
import { EMPTY } from 'src/app/constant';
import { MOCK_ALERT_DIALOG_CONFIG } from '../../modules/analyzer-status/mocks/qc-data';
import { UIEventType } from 'src/app/interfaces/receiveMessage';

describe('AnalyzerAlertDialogComponent', () => {
  let component: AnalyzerAlertDetailsComponent;
  let fixture: ComponentFixture<AnalyzerAlertDetailsComponent>;

  const routerMock = {
    navigate: jest.fn().mockReturnValue(Promise.resolve(true)),
    url: '/analyzer-status/analyzer-alert',
  };
  const dialogServiceMock = {
    openDialog$: of(MOCK_ALERT_DIALOG_CONFIG),
    closeDialog$: {
      next: jest.fn(),
    },
  };
  const analyzerServiceMock = {
    getAnalyzers: jest.fn().mockReturnValue(of(MOCK_ANALYZER_OUT)),
    analyzerWithTileConf: jest.fn(),
  };

  const mockAuthService = {
    isAuthenticated$: of(true), // Mocking isAuthenticated$ as an observable
    idTokenClaims$: of({}), // Mocking idTokenClaims$ as an observable
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        {
          provide: Router,
          useValue: routerMock,
        },
        {
          provide: DialogService,
          useValue: dialogServiceMock,
        },
        {
          provide: AnalyzerService,
          useValue: analyzerServiceMock,
        },
        {
          provide: AUTH0_CLIENT,
          useValue: {},
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AnalyzerAlertDetailsComponent);
    component = fixture.componentInstance;
    component.openDialog = {
      type: 2,
      data: {
        siteCode: '123',
        alertPreferences: {
          isGreen: true,
          isYellow: true,
          isRed: true,
          isGray: true,
        },
      },
    };

    fixture.detectChanges();
  });

  // Helper function to create changes object for testing ngOnChanges
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const createMockDialogChange = (mockDialogData: any) => ({
    openDialog: {
      currentValue: mockDialogData,
      previousValue: undefined,
      firstChange: false,
      isFirstChange: () => false,
    },
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize analyzerAlertStatus array', () => {
    expect(component.analyzerAlertStatus).toBeDefined();
  });

  it('should open dialog if response is valid', () => {
    jest.spyOn(component, 'getAnalyzerStatus');

    const mockDialogData = {
      type: 1,
      data: {
        siteCode: '123',
        alertPreferences: {
          isGreen: true,
          isYellow: true,
          isRed: true,
          isGray: true,
        },
      },
    };

    component.openDialog = mockDialogData;

    const change = createMockDialogChange(mockDialogData);

    fixture.detectChanges();
    component.ngOnChanges(change);

    expect(component.getAnalyzerStatus).toHaveBeenCalled();
    expect(component.currentSiteCode).toEqual('123');
  });

  it('should call updateIotStatus if analyzerAlertStatus changes and analyzers are not empty', () => {
    // Arrange
    const mockAnalyzerAlertStatus = [
      {
        content: {
          analyzerModel: 'model1',
          analyzerSerial: 'serial1',
          isIotConnected: true,
        },
      },
    ];

    const changes = {
      analyzerAlertStatus: {
        currentValue: mockAnalyzerAlertStatus,
        previousValue: null,
        firstChange: false,
        isFirstChange: () => false,
      },
    };

    component.analyzers = MOCK_ANALYZER_IN;

    const updateIotStatusSpy = jest.spyOn(component, 'updateAnalyzersStatus');

    // Act
    component.ngOnChanges(changes);

    // Assert
    expect(updateIotStatusSpy).toHaveBeenCalled();
  });

  it('should not call updateIotStatus if analyzers are empty', () => {
    // Arrange
    const mockAnalyzerAlertStatus = [
      {
        content: {
          analyzerModel: 'model1',
          analyzerSerial: 'serial1',
          isIotConnected: true,
        },
      },
    ];

    const changes = {
      analyzerAlertStatus: {
        currentValue: mockAnalyzerAlertStatus,
        previousValue: null,
        firstChange: false,
        isFirstChange: () => false,
      },
    };

    component.analyzers = [];

    const updateIotStatusSpy = jest.spyOn(component, 'updateAnalyzersStatus');

    // Act
    component.ngOnChanges(changes);

    // Assert
    expect(updateIotStatusSpy).not.toHaveBeenCalled();
  });

  it('should not call updateIotStatus if analyzerAlertStatus does not change', () => {
    // Arrange
    const changes = {
      someOtherInput: {
        currentValue: 'new value',
        previousValue: 'old value',
        firstChange: false,
        isFirstChange: () => false,
      },
    };

    component.analyzers = MOCK_ANALYZER_IN;

    const updateIotStatusSpy = jest.spyOn(component, 'updateAnalyzersStatus');

    // Act
    component.ngOnChanges(changes);

    // Assert
    expect(updateIotStatusSpy).not.toHaveBeenCalled();
  });

  it('should set analyzerAlertStatus when getAnalyzerStatus is called', () => {
    component.getAnalyzerStatus('123');

    expect(component.analyzerAlertStatus).toBeDefined();
    expect(analyzerServiceMock.getAnalyzers).toHaveBeenCalled();
  });

  it('should sort analyzerAlertStatus array in the correct order', () => {
    const sortedAnalyzer = component.sortAlertAnalyzers(MOCK_ANALYZER_OUT.aloneAnalyzers);

    expect(sortedAnalyzer[0].tileConfig?.color).toEqual('yellow-20');
  });

  it('should navigate to detail page with the correct modelCode and serialNumber', () => {
    const modelCode = 'XN10';
    const serialNumber = '7759';

    component.navigateToDetail(modelCode, serialNumber);

    expect(routerMock.navigate).toHaveBeenCalledWith(['/analyzer-status/analyzer-detail', 'XN10', '7759']);
  });

  it('should close the dialog', () => {
    jest.spyOn(dialogServiceMock.closeDialog$, 'next');

    component.closeDialog();

    expect(dialogServiceMock.closeDialog$.next).toHaveBeenCalled();
  });

  it('should return the original array if all preferences are true', () => {
    component.alertPreferences = {
      isGreen: true,
      isYellow: true,
      isRed: true,
      isGray: true,
    };

    fixture.detectChanges();

    expect(component.filterAnalyzerAlertByStatusPreference(MOCK_ANALYZER_OUT.aloneAnalyzers)).toHaveLength(9);
  });

  it('should filter out analyzers based on preferences', () => {
    component.alertPreferences = {
      isGreen: false,
      isYellow: true,
      isRed: false,
      isGray: true,
    };

    fixture.detectChanges();

    expect(component.filterAnalyzerAlertByStatusPreference(MOCK_ANALYZER_OUT.aloneAnalyzers)).toHaveLength(7);
  });

  it('should filter out all analyzers if all preferences are false', () => {
    component.alertPreferences = {
      isGreen: false,
      isYellow: false,
      isRed: false,
      isGray: false,
    };

    fixture.detectChanges();

    expect(component.filterAnalyzerAlertByStatusPreference(MOCK_ANALYZER_OUT.aloneAnalyzers)).toHaveLength(0);
  });

  it('should change analyzers status correctly after updateAnalyzersStatus', () => {
    component.analyzers = [MOCK_ANALYZER_OUT.aloneAnalyzers[0]];

    component.analyzerAlertStatus = [
      {
        uiEventType: UIEventType.ANALYZER_STATUS,
        siteCode: '123',
        eventId: EMPTY,
        sentTime: EMPTY,
        content: {
          ...component.analyzers[0].analyzerStatus,
          enableTroubleshootingTime: EMPTY,
          isIotConnected: false,
          statusCode: 0,
          isStart: false,
          isBfMode: false,
          isRequiredWbMode: false,
          analyzerModel: 'XN2000V',
          analyzerSerial: '()43520',
          qcDueBf: '2024/12/31 23:59:59',
        },
      },
    ];

    jest.spyOn(analyzerServiceMock, 'analyzerWithTileConf').mockReturnValue(component.analyzers[0]);

    component.updateAnalyzersStatus();

    expect(component.analyzers[0].analyzerStatus.isIotConnected).toEqual(
      component.analyzerAlertStatus[0].content.isIotConnected,
    );
    expect(component.analyzers[0].analyzerStatus.qcDueBf).toEqual(component.analyzerAlertStatus[0].content.qcDueBf);
    expect(analyzerServiceMock.analyzerWithTileConf).toHaveBeenCalled();
  });
});
