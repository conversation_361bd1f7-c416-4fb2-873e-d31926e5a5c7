import { AfterViewInit, Component, HostBinding, OnInit } from '@angular/core';
import { MatTabChangeEvent } from '@angular/material/tabs';

import { EventFocusRuleComponent } from 'src/app/models/event-params';
import { EventParam } from 'src/app/models/event-items';
import { GlobalVariables } from 'src/app/helpers/constant/global-variable';
import { RULES } from '../../constants';
import { RULE_STANDARD_SETTING_OPTION } from 'src/app/constant/localize-constant';
import { RuleConfigurationService } from '../../services/rule-configuration.service';
import { RuleStandardSettings } from '../../interfaces/admin-parameters';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-rules-configuration',
  templateUrl: './rules-configuration.component.html',
  styleUrls: ['./rules-configuration.component.scss'],
})
export class RulesConfigurationComponent implements OnInit, AfterViewInit {
  @HostBinding('class') className = 'container mx-auto my-4 flex flex-col rules-configuration';

  rules = Object.values(RULES);

  ruleStandardSettings: RuleStandardSettings | undefined;

  hasAccessibleSite = false;
  firstLoad = true;
  activatedTab = 'rule1';

  constructor(private readonly ruleConfigService: RuleConfigurationService) {}

  ngOnInit(): void {
    this.getStandardSettings(this.rules[0]);
  }

  ngAfterViewInit(): void {
    GlobalVariables.fireEvent(new EventParam(new EventFocusRuleComponent('view', 'rule1')));
  }

  getStandardSettings(ruleCode: string) {
    this.activatedTab = ruleCode;

    this.ruleConfigService
      .getStandardSettings(ruleCode)
      .pipe(finalize(() => (this.firstLoad = false)))
      .subscribe((result) => {
        if (!result) {
          this.hasAccessibleSite = false;
          this.ruleStandardSettings = undefined;

          return;
        }

        this.hasAccessibleSite = true;
        this.ruleStandardSettings = {
          ...result,
          description: RULE_STANDARD_SETTING_OPTION[result.keyName],
        };

        if (this.ruleStandardSettings.ruleCode === RULES.rule5) {
          this.ruleStandardSettings.description = $localize`:app-app_description:Rule 5 - Check Precision Recent Runs`;
        }

        if (this.ruleStandardSettings.ruleCode == RULES.rule10) {
          this.ruleStandardSettings.description = $localize`:app-app_description:Rule 10 - COC Limits`;
        }
      });
  }

  onTabChange(ev: MatTabChangeEvent) {
    this.getStandardSettings(this.rules[ev.index]);

    GlobalVariables.fireEvent(new EventParam(new EventFocusRuleComponent('view', `rule${ev.index + 1}`)));
  }
}
