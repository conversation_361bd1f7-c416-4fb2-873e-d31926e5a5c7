import { EMPTY } from '../constant';
import { ERROR_CODE } from '../enums/errors';
import { ErrorMessage } from './ErrorMessage';

describe('ErrorMessage', () => {
  it('should return the correct message for SM001', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.SM001);

    expect(message).toBe('The change was updated successfully.');
  });

  it('should return the correct message for SM002', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.SM002);

    expect(message).toBe('QC files were successfully uploaded.');
  });

  it('should return the correct message for SM003', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.SM003);

    expect(message).toBe('The request for Service Upgrade has been sent.');
  });

  it('should return the correct message for SM005', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.SM005, 'Lot123');

    expect(message).toBe('Assay Targets and Limits for Lot Lot123 were updated successfully');
  });

  it('should return the correct message for EM001', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM001, '1', '10');

    expect(message).toBe('Please enter a value between 1 and 10. ');
  });

  it('should return the correct message for EM002', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM002);

    expect(message).toBe('This field is required');
  });

  it('should return the correct message for EM003', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM003);

    expect(message).toBe('The system cannot perform this action. Please try again.');
  });

  it('should return the correct message for EM004', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM004);

    expect(message).toBe('Value is out of range.');
  });

  it('should return the correct message for EM005', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM005);

    expect(message).toBe('Invalid format. ');
  });

  it('should return the correct message for EM006', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM006);

    expect(message).toBe('No Results Found');
  });

  it('should return the correct message for EM007', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM007);

    expect(message).toBe('You are not allowed to access any sites');
  });

  it('should return the correct message for EM008', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM008);

    expect(message).toBe('You are not given permission to access this function');
  });

  it('should return the correct message for EM009', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM009);

    expect(message).toBe('Default site is not available, please choose a site');
  });

  it('should return the correct message for EM010', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM010);

    expect(message).toBe('No Data Found');
  });

  it('should return the correct message for EM011', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM011, '5', '10');

    expect(message).toBe('5 must be smaller than 10. ');
  });

  it('should return the correct message for EM012', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM012, '2');

    expect(message).toBe('Maximum 2 decimals allowed.');
  });

  it('should return the correct message for EM013', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM013, 'value');

    expect(message).toBe('Duplicated value');
  });

  it('should return the correct message for EM014', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM014, '5');

    expect(message).toBe('Not enough data points(<5 points)');
  });

  it('should return the correct message for EM015', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM015, '30');

    expect(message).toBe('Not enough pre-evaluation time(<30 days)');
  });

  it('should return the correct message for EM016', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM016, '1', '10');

    expect(message).toBe('Value shall be within 1 and 10');
  });

  it('should return the correct message for EM018', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM018, '5', '10');

    expect(message).toBe('5 must be smaller than 10. ');
  });

  it('should return the correct message for EM019', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.EM019, '1', '10');

    expect(message).toBe('Value must be between 1 and 10. ');
  });

  it('should return the correct message for UQ001', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ001);

    expect(message).toBe(`Upload Failed. You upload a file in the wrong format.
        Only .txt files are allowed. Please try again.`);
  });

  it('should return the correct message for UQ002', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ002);

    expect(message).toBe('Upload Failed. The filename should start with "OnlineQC".');
  });

  it('should return the correct message for UQ003', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ003, EMPTY);

    expect(message).toBe(`The following mandatory field(s) are missing.
          Please double check and try again:
          ${EMPTY}`);
  });

  it('should return the correct message for ERROR_CODE.UQ004', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ004);

    expect(message).toBe('N files were successfully processed into the system.');
  });

  it('should return the correct message for ERROR_CODE.UQ005', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ005);

    expect(message).toBe(`Upload Failed. The file you are trying to upload exceeds the maximum file size limit (1 MB).
        Please reduce the file size and try again.`);
  });

  it('should return the correct message for ERROR_CODE.UQ006', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ006);

    expect(message).toBe('Please select maximum 6  files.');
  });

  it('should return the correct message for ERROR_CODE.UQ007', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ007);

    expect(message).toBe(`Upload Failed.
          The file you are trying to upload exceeds the maximum file size limit (10MB).
          Please reduce the file size and try again.`);
  });

  it('should return the correct message for ERROR_CODE.UQ008', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ008);

    expect(message).toBe(`Upload Failed.
          You upload a file in the wrong format. Only .xls, .xlsx, .xlsm, .xml or .csv files are allowed.
          Please try again.`);
  });

  it('should return the correct message for ERROR_CODE.UQ009', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ009, EMPTY);

    expect(message).toBe(
      `Invalid Model Group.
        The Model Group cannot be identified in the system.` +
        ' Please make sure the model group being used is one of the following:' +
        `\n${EMPTY}`,
    );
  });

  it('should return the correct message for ERROR_CODE.UQ010', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ010, EMPTY);

    expect(message).toBe(
      `Invalid Material.
        The Material cannot be identified in the system.` +
        ' Please make sure the material being used is one of the following:' +
        `\n${EMPTY}`,
    );
  });

  it('should return the correct message for ERROR_CODE.UQ011', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ011);

    expect(message).toBe(`Invalid Expiration Date.
        Please correct the format of Expired Date based on your file types:
        - For .xls, .xlsx, .xlsm file(s): YYYY-MM-DD
        - For csv: YYYY/MM/DD
        - For xml: DD.MM.YYYY`);
  });

  it('should return the correct message for ERROR_CODE.UQ012', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ012);

    expect(message).toBe('Invalid Lot. Valid Lot must contain 4 or 8 digits.Please try again.');
  });

  it('should return the correct message for ERROR_CODE.UQ013', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ013);

    expect(message).toBe(`QC Level Information is incorrect.
        Please upload data for only 2 or 3 levels depending your materials:
        - For Body Flood: only Level 1 and Level 2
        - For Whole Blood: Level 1, Level 2 and Level 3`);
  });

  it('should return the correct message for ERROR_CODE.UQ014', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ014, EMPTY);

    expect(message).toBe(`The unit of measurement of parameter(s) in your file
        cannot be identified in the system. CQC allows to upload info for
        ${EMPTY} Please double check and try again`);
  });

  it('should return the correct message for ERROR_CODE.UQ015', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ015);

    expect(message).toBe(`Target value is incorrect.
        This value must be in range Lower Limit and Upper Limit,
        OR Target must be numeric value. Please double check and try again.`);
  });

  it('should return the correct message for ERROR_CODE.UQ016', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ016);

    expect(message).toBe(`Lower Limit is incorrect.
        This value must be less then Upper Limit, OR Lower Limit must be numeric value.
        Please double check and try again.`);
  });

  it('should return the correct message for ERROR_CODE.UQ017', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ017);

    expect(message).toBe(`Upper Limit is incorrect.
        This value must be greater then Lower Limit, OR Upper Limit must be numeric value.
        Please double check and try again.`);
  });

  it('should return the correct message for ERROR_CODE.UQ018', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ018);

    expect(message).toBe(`Invalid Start Date.
        Please correct the format of Start Date based on your file types:
        - For .xls, .xlsx, .xlsm file(s): YYYY-MM-DD
        - For csv: YYYY/MM/DD
        - For xml: DD.MM.YYYY`);
  });

  it('should return the correct message for ERROR_CODE.UQ019', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.UQ019);

    expect(message).toBe(`Invalid Limit is incorrect.
        Limit must be numeric value. Please double check and try again.`);
  });

  it('should return the correct message for ERROR_CODE.DI001', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI001, '-Parameter Name');

    expect(message).toBe(`The following mandatory field(s) are missing.
        Please double check and try again:
        -Parameter Name`);
  });

  it('should return the correct message for ERROR_CODE.DI002', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI002);

    expect(message).toBe('The same file already exists.');
  });

  it('should return the correct message for ERROR_CODE.DI003', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI003, 'HGB');

    expect(message).toBe('The file contains duplicated entries for the parameter HGB.');
  });

  it('should return the correct message for ERROR_CODE.DI004', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI004, '++--');

    expect(message).toBe(`The following parameter include special characters.
        Please remove special characters and try again:
        ++--`);
  });

  it('should return the correct message for ERROR_CODE.DI005', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI005, '1123');

    expect(message).toBe('Lot 1123 is already inactive. Please use an active lot.');
  });

  it('should return the correct message for ERROR_CODE.DI006', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI006, '1123');

    expect(message).toBe('Lot 1123 does not exist in the system. Please double check and try again.');
  });

  it('should return the correct message for ERROR_CODE.DI007', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI007, 'HGB\n');

    expect(message).toBe(`The unit of measurement of parameter(s) in your file
        cannot be identified in the system.
        CQC allows to upload info for
        HGB\n Please double check and try again.`);
  });

  it('should return the correct message for ERROR_CODE.DI008', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI008, 'HGB');

    expect(message).toBe(`The parameter HGB has not existed in the system.
          Please double check and try again.`);
  });

  it('should return the correct message for ERROR_CODE.DI009', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI009, 'Analyzer123');

    expect(message).toBe(`The analyzer Analyzer123 is not supported because:
        - Not active OR
        - Not enrolled to CQC`);
  });

  it('should return the correct message for ERROR_CODE.DI010', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI010);

    expect(message).toBe('The associated analyzer does not belong to the user’s accessible sites');
  });

  it('should return the correct message for ERROR_CODE.DI011', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI011, 'HGB');

    expect(message).toBe(`The file contains special parameter(s)
        HGB`);
  });

  it('should return the correct message for ERROR_CODE.DI012', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.DI012, 'Analyzer123');

    expect(message).toBe(`The analyzer Analyzer123 does not belong to the selected site.
          Please select another file and try again.`);
  });

  it('should return the correct message for ERROR_CODE.SS004', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.SS004);

    expect(message).toBe('Please select at least one alert option');
  });

  it('should return the correct message for ERROR_CODE.TS001', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.TS001);

    expect(message).toBe('Upload Failed. Files must be in <strong>.pdf</strong> or <strong>.mp4</strong> formats.');
  });

  it('should return the correct message for ERROR_CODE.TS002', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.TS002);

    expect(message).toBe('Upload Failed. Mp4 file size is larger than 100MB.');
  });

  it('should return the correct message for ERROR_CODE.TS003', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.TS003);

    expect(message).toBe('Upload Failed. PDF file size is larger than 20MB.');
  });

  it('should return the correct message for ERROR_CODE.TS004', () => {
    const workflowName = 'Test Workflow';
    const message = ErrorMessage.getMessage(ERROR_CODE.TS004, workflowName);

    expect(message).toBe(
      // eslint-disable-next-line max-len
      `This Instruction Step is associated with Channel Workflow: ${workflowName}. Please remove it from the workflow first in order to change the Model Group and Channel information`,
    );
  });

  it('should return the correct message for ERROR_CODE.TS005', () => {
    const workflowName = 'Test Workflow';
    const message = ErrorMessage.getMessage(ERROR_CODE.TS005, workflowName);

    expect(message).toBe(
      'This Instruction Step cannot be deleted as it is associated with ' + `Channel Workflow: ${workflowName}`,
    );
  });

  it('should return the correct message for ERROR_CODE.TS006', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.TS006);

    expect(message).toBe('Instruction Step does not exist');
  });

  it('should return the correct message for ERROR_CODE.TS007', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.TS007);

    expect(message).toBe(
      'This Instruction Step is for [Channel 1] and [Model Group 1], not [Channel 2] and [Model Group 2]',
    );
  });

  it('should return the correct message for ERROR_CODE.TS008', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.TS008);

    expect(message).toBe("There's no step yet, please add at least 1 step");
  });

  it('should return the correct message for ERROR_CODE.TS009', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.TS009);

    expect(message).toBe('A workflow can only have maximum of 12 steps');
  });

  it('should return the correct message for ERROR_CODE.AG001', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.AG001);

    expect(message).toBe('You do not have access to one or more sites within this group');
  });

  it('should return the correct message for ERROR_CODE.RC001', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.RC001);

    expect(message).toBe('Maximum 5 lots allowed');
  });

  it('should return the correct message for ERROR_CODE.CUS001', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.CUS001);

    expect(message).toBe('Upper Limit must be greater than Mean');
  });

  it('should return the correct message for ERROR_CODE.CUS002', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.CUS002);

    expect(message).toBe('Upper Limit must be greater than Lower Limit');
  });

  it('should return the correct message for ERROR_CODE.CUS003', () => {
    const message = ErrorMessage.getMessage(ERROR_CODE.CUS003);

    expect(message).toBe('The updated change was identical to previous setting.');
  });

  it('should return the correct message for unknown error code', () => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const message = ErrorMessage.getMessage('UNKNOWN_CODE' as ERROR_CODE);

    expect(message).toBe('Unknown error code');
  });
});
