<div appAclWrapper>
  <div class="flex items-center" appAcl #acl="AppAcl">
    <mat-slide-toggle
      [(ngModel)]="isEnabled"
      color="primary"
      [disabled]="ruleCode === 'rule1' || !(acl.permission$ | async)?.write"
      (change)="onChangeEnable($event)"
    >
      {{ enableToggleLabel.getValue() }}
    </mat-slide-toggle>

    <mat-checkbox
      class="p-4"
      [(ngModel)]="isCreateServiceTicket"
      color="primary"
      (change)="onChangeCreateServiceTicket($event)"
      [disabled]="!(acl.permission$ | async)?.write"
    >
      <span i18n>Create service ticket</span>
    </mat-checkbox>

    @if ((acl.permission$ | async)?.write) {
      <button mat-stroked-button color="primary" (click)="onOpenAuditTrail()">
        <span i18n>Audit Trail</span>
      </button>
    }
  </div>
</div>

<ng-template #dialogContent>
  <div class="text-base font-semibold">
    <span i18n="@@app-app_button-confirm_save">Are you sure you want to save these changes?</span>
  </div>
</ng-template>

<app-create-service-ticket-audit-trail
  [dialogParams]="openAuditTrailDialog$ | async"
></app-create-service-ticket-audit-trail>
