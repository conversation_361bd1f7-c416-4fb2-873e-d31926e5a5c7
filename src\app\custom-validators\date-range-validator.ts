import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';
import { Directive, Input, OnChanges } from '@angular/core';

@Directive({
  selector: '[appDateRangeValidator]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: DateRangeValidatorDirective,
      multi: true,
    },
  ],
  standalone: true,
})
export class DateRangeValidatorDirective implements Validator, OnChanges {
  @Input() minDate: Date | undefined;
  @Input() maxDate: Date | undefined;
  private control: AbstractControl | undefined;

  validate(control: AbstractControl): ValidationErrors | null {
    this.control = control;

    if (control.value) {
      const { start, end } = control.value;

      if (!start && !end) {
        return { required: true };
      }

      if (
        (!start && end) ||
        (start && !end) ||
        start > end ||
        (this.minDate && start < this.minDate) ||
        (this.maxDate && end > this.maxDate)
      ) {
        return {
          dateInvalid: true,
        };
      }

      return null;
    }

    return { required: true };
  }

  ngOnChanges(): void {
    this.control?.updateValueAndValidity();
  }
}
