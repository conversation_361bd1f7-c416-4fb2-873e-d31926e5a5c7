import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';
import { Directive, Input } from '@angular/core';

import moment, { unitOfTime } from 'moment/moment';

@Directive({
  selector: '[appInnerDateRangeValidator]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: InnerDateRangeValidatorDirective,
      multi: true,
    },
  ],
  standalone: true,
})
export class InnerDateRangeValidatorDirective implements Validator {
  @Input() startToEndRange: number | undefined;
  @Input() startToEndUnit: unitOfTime.Base = 'months';

  validate(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return { required: true }
    }

    if (!this.startToEndRange) {
      return null;
    }

    const { start, end } = control.value;

    const startDate = moment(start);

    const endDate = moment(end);

    const diffInMonths = endDate.diff(startDate, this.startToEndUnit, true);

    const isValid = diffInMonths <= this.startToEndRange;

    if (!isValid) {
      return { invalidInnerRange: true }
    }

    return null;
  }

}
