import { RowClassParams } from 'ag-grid-community';
import { TableUtilities } from './table-utilities'; // Adjust the path as necessary

describe('TableUtilities', () => {
  describe('getRowStripedStyle', () => {
    it('should return striped style for odd row index', () => {
      // casting 3rd party library type
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const params = {
        node: { rowIndex: 1 },
      } as RowClassParams;

      const result = TableUtilities.getRowStripedStyle(params);

      expect(result).toEqual({
        background: 'var(--grey-40)',
        borderBottom: '1px solid var(--grey-60)',
        borderTop: '1px solid var(--grey-60)',
      });
    });

    it('should return default style for even row index', () => {
      // casting 3rd party library type
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const params = {
        node: { rowIndex: 0 },
      } as RowClassParams;

      const result = TableUtilities.getRowStripedStyle(params);

      expect(result).toEqual({
        background: 'white',
        borderBottom: 'none',
        borderTop: 'none',
      });
    });
  });
});
