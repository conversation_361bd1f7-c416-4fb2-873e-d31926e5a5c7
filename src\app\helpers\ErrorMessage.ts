/* eslint-disable max-len */
import { ERROR_CODE } from '../enums/errors';

export class ErrorMessage {
  static getMessage(code: ERROR_CODE, value1?: string, value2?: string): string {
    switch (code) {
      case ERROR_CODE.SM001:
        return $localize`:@@app-app_message-message_code-SM001:The change was updated successfully.`;

      case ERROR_CODE.SM002:
        return $localize`:@@app-app_message-message_code-SM002:QC files were successfully uploaded.`;

      case ERROR_CODE.SM003:
        return $localize`:@@app-app_message-message_code-SM003:The request for Service Upgrade has been sent.`;

      case ERROR_CODE.SM005:
        return $localize`:@@app-app_message-message_code-SM005:Assay Targets and Limits for Lot ${value1} were updated successfully`;

      case ERROR_CODE.EM001:
        return $localize`:@@app-app_message-message_code-EM001:Please enter a value between ${value1} and ${value2}. `;

      case ERROR_CODE.EM002:
        return $localize`:@@app-app_message-message_code-EM002:This field is required`;

      case ERROR_CODE.EM003:
        return $localize`:@@app-app_message-message_code-EM003:The system cannot perform this action. Please try again.`;

      case ERROR_CODE.EM004:
        return $localize`:@@app-app_message-message_code-EM004:Value is out of range.`;

      case ERROR_CODE.EM005:
        return $localize`:@@app-app_message-message_code-EM005:Invalid format. `;

      case ERROR_CODE.EM006:
        return $localize`:@@app-app_message-message_code-EM006:No Results Found`;

      case ERROR_CODE.EM007:
        return $localize`:@@app-app_message-message_code-EM007:You are not allowed to access any sites`;

      case ERROR_CODE.EM008:
        return $localize`:@@app-app_message-message_code-EM008:You are not given permission to access this function`;

      case ERROR_CODE.EM009:
        return $localize`:@@app-app_message-message_code-EM009:Default site is not available, please choose a site`;

      case ERROR_CODE.EM010:
        return $localize`:@@app-app_message-message_code-EM010:No Data Found`;

      case ERROR_CODE.EM011:
        return $localize`:@@app-app_message-message_code-EM011:${value1} must be smaller than ${value2}. `;

      case ERROR_CODE.EM012:
        return $localize`:@@app-app_message-message_code-EM012:Maximum ${value1} decimals allowed.`;

      case ERROR_CODE.EM013:
        return $localize`:@@app-app_message-message_code-EM013:Duplicated ${value1}`;

      case ERROR_CODE.EM014:
        return $localize`:@@app-app_message-message_code-EM014:Not enough data points(<${value1} points)`;

      case ERROR_CODE.EM015:
        return $localize`:@@app-app_message-message_code-EM015:Not enough pre-evaluation time(<${value1} days)`;

      case ERROR_CODE.EM016:
        return $localize`:@@app-app_message-message_code-EM016:Value shall be within ${value1} and ${value2}`;

      case ERROR_CODE.EM018:
        return $localize`:@@app-app_message-message_code-EM018:${value1} must be smaller than ${value2}. `;

      case ERROR_CODE.EM019:
        return $localize`:@@app-app_message-message_code-EM019:Value must be between ${value1} and ${value2}. `;

      case ERROR_CODE.EM023:
        return $localize`:@@app-app_message-message_code-EM023:We couldn’t find any results matching your search. Try adjusting your criteria to see more options. `;

      case ERROR_CODE.UQ001:
        return $localize`:@@app-app_message-message_code-UQ001:Upload Failed. You upload a file in the wrong format.
        Only .txt files are allowed. Please try again.`;

      case ERROR_CODE.UQ002:
        return $localize`:@@app-app_message-message_code-UQ002:Upload Failed. The filename should start with "OnlineQC".`;

      case ERROR_CODE.UQ003:
        return $localize`:@@app-app_message-message_code-UQ003:The following mandatory field(s) are missing.
          Please double check and try again:
          ${value1}`;

      case ERROR_CODE.UQ004:
        return $localize`:@@app-app_message-message_code-UQ004:N files were successfully processed into the system.`;

      case ERROR_CODE.UQ005:
        return $localize`:@@app-app_message-message_code-UQ005:Upload Failed. The file you are trying to upload exceeds the maximum file size limit (1 MB).
        Please reduce the file size and try again.`;

      case ERROR_CODE.UQ006:
        return $localize`:@@app-app_message-message_code-UQ006:Please select maximum 6  files.`;

      case ERROR_CODE.UQ007:
        return $localize`:@@app-app_message-message_code-UQ007:Upload Failed.
          The file you are trying to upload exceeds the maximum file size limit (10MB).
          Please reduce the file size and try again.`;

      case ERROR_CODE.UQ008:
        return $localize`:@@app-app_message-message_code-UQ008:Upload Failed.
          You upload a file in the wrong format. Only .xls, .xlsx, .xlsm, .xml or .csv files are allowed.
          Please try again.`;

      case ERROR_CODE.UQ009:
        return (
          $localize`:@@app-app_message-message_code-UQ009:Invalid Model Group.
        The Model Group cannot be identified in the system. Please make sure the model group being used is one of the following:` +
          `\n${value1}`
        );

      case ERROR_CODE.UQ010:
        return (
          $localize`:@@app-app_message-message_code-UQ010:Invalid Material.
        The Material cannot be identified in the system. Please make sure the material being used is one of the following:` +
          `\n${value1}`
        );

      case ERROR_CODE.UQ011:
        return $localize`:@@app-app_message-message_code-UQ011:Invalid Expiration Date.
        Please correct the format of Expired Date based on your file types:
        - For .xls, .xlsx, .xlsm file(s): YYYY-MM-DD
        - For csv: YYYY/MM/DD
        - For xml: DD.MM.YYYY`;

      case ERROR_CODE.UQ012:
        return $localize`:@@app-app_message-message_code-UQ012:Invalid Lot. Valid Lot must contain 4 or 8 digits.Please try again.`;

      case ERROR_CODE.UQ013:
        return $localize`:@@app-app_message-message_code-UQ013:QC Level Information is incorrect.
        Please upload data for only 2 or 3 levels depending your materials:
        - For Body Flood: only Level 1 and Level 2
        - For Whole Blood: Level 1, Level 2 and Level 3`;

      case ERROR_CODE.UQ014:
        return $localize`:@@app-app_message-message_code-UQ014:The unit of measurement of parameter(s) in your file
        cannot be identified in the system. CQC allows to upload info for
        ${value1} Please double check and try again`;

      case ERROR_CODE.UQ015:
        return $localize`:@@app-app_message-message_code-UQ015:Target value is incorrect.
        This value must be in range Lower Limit and Upper Limit,
        OR Target must be numeric value. Please double check and try again.`;

      case ERROR_CODE.UQ016:
        return $localize`:@@app-app_message-message_code-UQ016:Lower Limit is incorrect.
        This value must be less then Upper Limit, OR Lower Limit must be numeric value.
        Please double check and try again.`;

      case ERROR_CODE.UQ017:
        return $localize`:@@app-app_message-message_code-UQ017:Upper Limit is incorrect.
        This value must be greater then Lower Limit, OR Upper Limit must be numeric value.
        Please double check and try again.`;

      case ERROR_CODE.UQ018:
        return $localize`:@@app-app_message-message_code-UQ018:Invalid Start Date.
        Please correct the format of Start Date based on your file types:
        - For .xls, .xlsx, .xlsm file(s): YYYY-MM-DD
        - For csv: YYYY/MM/DD
        - For xml: DD.MM.YYYY`;

      case ERROR_CODE.UQ019:
        return $localize`:@@app-app_message-message_code-UQ019:Invalid Limit is incorrect.
        Limit must be numeric value. Please double check and try again.`;

      case ERROR_CODE.UQ020:
        return $localize`:@@app-app_message-message_code-UQ020:No data found. Please go to “Assay Upload”
          Tab to upload your targets and limits`;

      case ERROR_CODE.UQ021:
        return $localize`:@@app-app_message-message_code-UQ021:	The parameter ${value1} has not existed in the system.
          Please double check and try again.`;

      case ERROR_CODE.DI001:
        return $localize`:@@app-app_message-message_code-DI001:The following mandatory field(s) are missing.
        Please double check and try again:
        ${value1}`;

      case ERROR_CODE.DI002:
        return $localize`:@@app-app_message-message_code-DI002:The same file already exists.`;

      case ERROR_CODE.DI003:
        return $localize`:@@app-app_message-message_code-DI003:The file contains duplicated entries for the parameter ${value1}.`;

      case ERROR_CODE.DI004:
        return $localize`:@@app-app_message-message_code-DI004:The following parameter include special characters.
        Please remove special characters and try again:
        ${value1}`;

      case ERROR_CODE.DI005:
        return $localize`:@@app-app_message-message_code-DI005:Lot ${value1} is already inactive. Please use an active lot.`;

      case ERROR_CODE.DI006:
        return $localize`:@@app-app_message-message_code-DI006:Lot ${value1} does not exist in the system. Please double check and try again.`;

      case ERROR_CODE.DI007:
        return $localize`:@@app-app_message-message_code-DI007:The unit of measurement of parameter(s) in your file
        cannot be identified in the system.
        CQC allows to upload info for
        ${value1} Please double check and try again.`;

      case ERROR_CODE.DI008:
        return $localize`:@@app-app_message-message_code-DI008:The parameter ${value1} has not existed in the system.
          Please double check and try again.`;

      case ERROR_CODE.DI009:
        return $localize`:@@app-app_message-message_code-DI009:The analyzer ${value1} is not supported because:
        - Not active OR
        - Not enrolled to CQC`;

      case ERROR_CODE.DI010:
        return $localize`:@@app-app_message-message_code-DI010:The associated analyzer does not belong to the user’s accessible sites`;

      case ERROR_CODE.DI011:
        return $localize`:@@app-app_message-message_code-DI011:The file contains special parameter(s)
        ${value1}`;

      case ERROR_CODE.DI012:
        return $localize`:@@app-app_message-message_code-DI012:The analyzer ${value1} does not belong to the selected site.
          Please select another file and try again.`;

      case ERROR_CODE.SS004:
        return $localize`:@@app-SS004:Please select at least one alert option`;

      case ERROR_CODE.TS001:
        return $localize`:@@app-app_message-message_code-TS001:Upload Failed. Files must be in <strong>.pdf</strong> or <strong>.mp4</strong> formats.`;

      case ERROR_CODE.TS002:
        return $localize`:@@app-app_message-message_code-TS002:Upload Failed. Mp4 file size is larger than 100MB.`;

      case ERROR_CODE.TS003:
        return $localize`:@@app-app_message-message_code-TS003:Upload Failed. PDF file size is larger than 20MB.`;

      case ERROR_CODE.TS004:
        return $localize`:@@app-app_message-message_code-TS004:This Instruction Step is associated with Channel Workflow: ${value1}. Please remove it from the workflow first in order to change the Model Group and Channel information`;

      case ERROR_CODE.RC001:
        return $localize`:@@app-RC001:Maximum 5 lots allowed`;

      case ERROR_CODE.RC002:
        return $localize`:@@app-RC002:Please select at least one report type option.`;

      case ERROR_CODE.TS005:
        return $localize`:@@app-app_message-message_code-TS005:This Instruction Step cannot be deleted as it is associated with Channel Workflow: ${value1}`;

      case ERROR_CODE.TS006:
        return $localize`:@@app-app_message-message_code-TS006:Instruction Step does not exist`;

      case ERROR_CODE.TS007:
        return $localize`:@@app-app_message-message_code-TS007:This Instruction Step is for [Channel 1] and [Model Group 1], not [Channel 2] and [Model Group 2]`;

      case ERROR_CODE.TS008:
        return $localize`:@@app-app_message-message_code-TS008:There\'s no step yet, please add at least 1 step`;

      case ERROR_CODE.TS009:
        return $localize`:@@app-app_message-message_code-TS009:A workflow can only have maximum of 12 steps`;

      case ERROR_CODE.AG001:
        return $localize`:@@app-app_message_code-AG001:You do not have access to one or more sites within this group`;

      case ERROR_CODE.CUS001:
        return $localize`:@@app-app_message_code-CUS001:Upper Limit must be greater than Mean`;

      case ERROR_CODE.CUS002:
        return $localize`:@@app-app_message_code-CUS002:Upper Limit must be greater than Lower Limit`;

      case ERROR_CODE.CUS003:
        return $localize`:@@app-app_message_code-CUS003:The updated change was identical to previous setting.`;

      case ERROR_CODE.SL001:
        return $localize`:@@app-app_message_code-SL001:Successfully started Service Mode for Analyzer ${value1}`;

      case ERROR_CODE.SL002:
        return $localize`:@@app-app_message_code-SL002:Successfully ended Service Mode for Analyzer ${value1}`;

      default:
        return $localize`:@@app-app_message-message_code-unknown_error_code:Unknown error code`;
    }
  }
}
