import { AbstractControl, FormControl, ValidationErrors } from '@angular/forms';

import { CellValueChangedEvent } from 'ag-grid-community';

import { NUMBER_SETTINGS_ID, RULES } from '../constants';

import { RuleConfigGridInputValidator } from './rule-config-grid-input-validator';

class EditableInputCellParams {
  api = {
    addEventListener: jest.fn(),
  };
}

class MockAbstractControl extends AbstractControl {
  override reset(): void {
    // This is intentional
  }

  override value: string | number = '50';

  constructor() {
    super(null, null);
  }

  override markAsTouched() {
    // This is intentional
  }

  override markAsDirty() {
    // This is intentional
  }

  override updateValueAndValidity() {
    // This is intentional
  }

  setValue() {
    // This is intentional
  }

  patchValue() {
    // This is intentional
  }

  override get(path: string | string[]): AbstractControl | null {
    if (typeof path === 'string' && path === 'expectedPath') {
      return new MockAbstractControl();
    }

    if (Array.isArray(path) && path.includes('expectedPath')) {
      return new MockAbstractControl();
    }

    return null;
  }
}

describe('RuleConfigGridInputValidator', () => {
  let directive: RuleConfigGridInputValidator;

  let control: FormControl;

  let mockControl: MockAbstractControl;

  let mockParams: EditableInputCellParams;

  beforeEach(() => {
    directive = new RuleConfigGridInputValidator();

    mockControl = new MockAbstractControl();

    mockParams = {
      api: {
        addEventListener: jest.fn(),
      },
    };
  });

  beforeEach(() => {
    control = new FormControl(null);
  });

  it('should create an instance', () => {
    expect(directive).toBeTruthy();
  });

  it('should return { required: true } if the value is empty', () => {
    directive.constraints = {
      min: 0,
      max: 10,
      allowDecimals: true,
    };

    directive.required = true;

    expect(directive.checkInvalid('')).toEqual({ required: true });
  });

  it('should return null if the required is false', () => {
    directive.constraints = {
      min: 0,
      max: 10,
      allowDecimals: true,
    };

    directive.required = false;

    expect(directive.checkInvalid('')).toBeNull();
  });

  it('should return true if value is below the minimum', () => {
    directive.constraints = {
      min: 5,
      max: 10,
      minIncluded: false,
      maxIncluded: true,
      allowDecimals: true,
    };

    expect(directive.isRangeError(4)).toBe(true);
  });

  it('should return true if value is above the maximum', () => {
    directive.constraints = {
      min: 5,
      max: 10,
      minIncluded: true,
      maxIncluded: false,
      allowDecimals: true,
    };

    expect(directive.isRangeError(11)).toBe(true);
  });

  it('should return false for values within the range', () => {
    directive.constraints = {
      min: 5,
      max: 10,
      minIncluded: true,
      maxIncluded: true,
      allowDecimals: true,
    };

    expect(directive.isRangeError(5)).toBe(false);
    expect(directive.isRangeError(10)).toBe(false);
    expect(directive.isRangeError(7)).toBe(false);
  });

  it('should return null if the value is valid', () => {
    directive.constraints = { allowDecimals: false };
    directive.rowIndex = 0;
    directive.rowIndexValue = 0;
    directive.lowerLimitValue = 0;
    directive.upperLimitValue = 100;

    mockControl.value = '50';

    const result: ValidationErrors | null = directive.validate(mockControl);

    expect(result).toBeNull();
  });

  it('should return lowerUpperError if lowerLimit is not less than upperLimit', () => {
    directive.lowerLimitValue = 10;
    directive.upperLimitValue = 10;
    directive.rowIndex = 1;
    directive.rowIndexValue = 1;

    const result: ValidationErrors | null = directive.checkInvalid('15');

    expect(result).toEqual({ lowerUpperError: true });
  });

  it('should return lowerUpperCocError if cocParameterCode is present and limits are reversed', () => {
    directive.lowerLimitValue = 20;
    directive.upperLimitValue = 10;
    directive.cocParameterCode = 'code';
    directive.rowIndex = 1;
    directive.rowIndexValue = 1;

    const result: ValidationErrors | null = directive.checkInvalid('15');

    expect(result).toEqual({ lowerUpperCocError: true });
  });

  it('should update internal properties and call control methods', () => {
    // 3rd party library class, must using mock object by casting type
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const mockEvent = {
      node: { rowIndex: 1 },
    } as unknown as CellValueChangedEvent;

    directive.absControl = mockControl;

    directive.onCellValueChanged(mockEvent);

    expect(directive.rowIndexValue).toBe(1);
  });

  it('should validate with rule7 and troubleshootingEvents correctly', () => {
    directive.ruleCode = RULES.rule7;

    directive.inputValues = [
      {
        id: NUMBER_SETTINGS_ID.troubleshootingEvents,
        label: 'Number of Consecutive Troubleshooting Events',
        type: 'number',
        value: 5,
        order: 1,
        min: 0,
        max: 10,
      },
      {
        id: NUMBER_SETTINGS_ID.consecutiveControlRunSets,
        label: 'Number of Consecutive Control Runs',
        type: 'number',
        value: 4,
        order: 2,
        min: 0,
        max: 10,
      },
    ];

    //3rd party library class, must using mock object by casting type
    //eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const control = {
      value: '15',
      dirty: true,
      touched: true,
    } as AbstractControl;

    expect(directive.validate(control)).toEqual({ rule7CrossFieldError: true });
  });
});
