import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NavigationEnd, Router } from '@angular/router';
import { Subject, of, switchMap } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

import { AlertAnalyzerStatusContent, AnalyzerAlertComponent } from './analyzer-alert.component';
import { EventAlertPreferencesChanged, EventDisableNotify, EventSiteChange } from '../models/event-params';
import { MOCK_ANALYZER_IN, MOCK_ANALYZER_OUT } from '../modules/analyzer-status/mocks/analyzer';
import { ReceiveMessage, UIEventType } from '../interfaces/receiveMessage';
import { AnalyzerService } from '../modules/analyzer-status/services/analyzer.service';
import { AuthService } from '@auth0/auth0-angular';
import { DatePipe } from '@angular/common';
import { EventParam } from '../models/event-items';
import { InjectionToken } from '@angular/core';
import { MOCK_ANALYZER_ALERT_MSG } from '../modules/analyzer-status/mocks/analyzer-alert';
import { MOCK_SITES_SETTINGS } from '../modules/settings/mocks/site';
import { MessageManagementService } from '../services/message-management.service';
import { SiteSettingsService } from '../services/site.service';
import { SysmexService } from '../services/sysmex.service';
import { UserStoreService } from '../services/user-store.service';

// Create the InjectionToken
export const AUTH0_CLIENT = new InjectionToken('auth0.client');

describe('AnalyzerAlertComponent', () => {
  let component: AnalyzerAlertComponent;
  let fixture: ComponentFixture<AnalyzerAlertComponent>;
  let analyzerService: AnalyzerService;

  const msgManagementServiceMock = {
    setInitMsgQueue: jest.fn(),
    getMsgQueue: jest.fn(),
    onReceiveMsg: new Subject(),
  };

  const mockAuthService = {
    isAuthenticated$: of(true), // Mocking isAuthenticated$ as an observable
    idTokenClaims$: of({}), // Mocking idTokenClaims$ as an observable
  };

  const mockUserStoreService = {
    loggedIn$: mockAuthService.isAuthenticated$.pipe(
      switchMap((value) => {
        return value ? mockAuthService.idTokenClaims$ : of(null);
      }),
    ),
    user: {
      access: [
        {
          sites: [{ isServiceL2: true }, { isServiceL2: false }],
        },
      ],
    },
    getDefaultSite: jest.fn(),
  };

  const eventsSubject = new Subject();

  const mockRouter = {
    events: eventsSubject.asObservable(),
    url: '/report',
  };

  const mockFormatCollection = {
    DATE_MONTH_YEAR: 'MM/dd/YYYY',
    DATE_MONTH_YEAR_REPORT: 'MMDDYYYY',
    FORMAT_1: 'HH:mm:ss',
    FORMAT_4: 'MM/dd/YYYY HH:mm:ss',
    API_FORMAT: 'YYYY-MM-dd',
  };

  const mockSysmex = {
    formatDate: jest.fn().mockImplementation((format, date) => {
      const datePipe = new DatePipe('en-US');

      return datePipe.transform(date, format);
    }),
    DATES: mockFormatCollection,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MatCardModule, MatIconModule, HttpClientTestingModule],
      providers: [
        {
          provide: AnalyzerService,
          useValue: {
            getAnalyzers: jest.fn().mockReturnValue(of(MOCK_ANALYZER_OUT)),
            mapQcDueStatus: jest.fn().mockReturnValue('QcNotDue'),
          },
        },
        {
          provide: MessageManagementService,
          useValue: msgManagementServiceMock,
        },
        {
          provide: SiteSettingsService,
          useValue: {
            getSiteSettings: jest.fn().mockReturnValue(of(MOCK_SITES_SETTINGS)),
          },
        },
        {
          provide: AUTH0_CLIENT,
          useValue: {},
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: UserStoreService,
          useValue: mockUserStoreService,
        },
        {
          provide: Router,
          useValue: mockRouter,
        },
        {
          provide: SysmexService,
          useValue: mockSysmex,
        },
      ],
    }).compileComponents();

    analyzerService = TestBed.inject(AnalyzerService);

    fixture = TestBed.createComponent(AnalyzerAlertComponent);

    component = fixture.componentInstance;

    component.canNotify = false;

    component.mapAlertAnalyzerStatuses = [];
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('getAnalyzers should fetch analyzers onInit', async () => {
    const event = new NavigationEnd(1, '/report', '/report');

    eventsSubject.next(event);

    jest.spyOn(component, 'handleRouterEvent');

    component.onInit();

    expect(component.currentSiteCode).toBeDefined();

    expect(component.handleRouterEvent).toBeDefined();
  });

  it('getAnalyzers should fetch analyzers and update mapAlertAnalyzerStatuses', async () => {
    const alertPreferences = MOCK_SITES_SETTINGS.alertPreferences;

    await component.setInitialAnalyzerAlert('123', alertPreferences);

    fixture.detectChanges();

    expect(component.mapAlertAnalyzerStatuses).toBeDefined();
  });

  it('should update alert banner visibility based on notification permission', () => {
    component.canNotify = true;

    component.mapAlertAnalyzerStatuses = [
      {
        color: 'green',
        numberOfAnalyzers: 1,
      },
    ];

    component.isShowingAlertBanner();

    expect(component.isDisplayingAlertBanner).toBeTruthy();
  });

  it('should correctly map analyzer statuses to HTML props', () => {
    jest.spyOn(analyzerService, 'mapQcDueStatus').mockReturnValue({
      qcDueStatus: 'QcPastDue',
      qcDueWbStatus: 'QcPastDue',
      qcDueBfStatus: '',
    });

    const result = component.mapAnalyzerStatusWithHTMLProps(MOCK_ANALYZER_ALERT_MSG);

    expect(result).toBeDefined();
  });

  it('should not display alert banner when canNotify is false', () => {
    component.canNotify = false;

    component.mapAlertAnalyzerStatuses = [
      {
        color: 'green',
        numberOfAnalyzers: 1,
      },
    ];

    component.isShowingAlertBanner();

    expect(component.isDisplayingAlertBanner).toBe(false);
  });

  it('should display alert banner when canNotify is true and there are analyzers', () => {
    component.canNotify = true;

    component.mapAlertAnalyzerStatuses = [
      {
        color: 'green',
        numberOfAnalyzers: 1,
      },
    ];

    component.isShowingAlertBanner();

    expect(component.isDisplayingAlertBanner).toBe(true);
  });

  it('should not display alert banner when there are no analyzers', () => {
    component.canNotify = true;

    component.mapAlertAnalyzerStatuses = [];

    component.isShowingAlertBanner();

    expect(component.isDisplayingAlertBanner).toBe(false);
  });

  it('should correctly map analyzer responses to message queue', () => {
    const result = component.mapMsgQueue(MOCK_ANALYZER_IN);

    expect(result).toBeDefined();
  });

  it('should emit openDialog$ with correct dialog type and site code', () => {
    jest.spyOn(component.openDialog$, 'next');

    component.currentSiteCode = '';
    component.alertPreferences = MOCK_SITES_SETTINGS.alertPreferences;

    fixture.detectChanges();

    component.openAnalyzerAlertDialog();

    expect(component.openDialog$.next).toHaveBeenCalledWith({
      type: 2,
      data: {
        siteCode: '',
        alertPreferences: {
          isGreen: true,
          isRed: true,
          isYellow: true,
          isGray: true,
        },
      },
    });
  });

  describe('onEvent', () => {
    it('should handle EventDisableNotify event', () => {
      const params: EventParam = new EventParam(new EventDisableNotify('EventDisableNotify'));

      component.onEvent(params);

      expect(component.canNotify).toBe(true);

      expect(component.isDisplayingAlertBanner).toBe(false);
    });

    it('should handle EventSiteChange event', () => {
      const params: EventParam = new EventParam(new EventSiteChange('newSiteCode'));
      const event = new NavigationEnd(1, '/report', '/report');

      eventsSubject.next(event);

      jest.spyOn(component, 'handleRouterEvent');

      component.onEvent(params);

      expect(component.currentSiteCode).toBe('newSiteCode');

      expect(component.handleRouterEvent).toBeDefined();
    });

    it('should handle EventSiteChange event when alertPreferences is undefined', () => {
      const params: EventParam = new EventParam(new EventSiteChange('newSiteCode'));

      component.alertPreferences = undefined;

      jest.spyOn(component, 'setInitialAnalyzerAlert');

      jest.spyOn(component, 'getAlertPreferences').mockReturnValue(of(MOCK_SITES_SETTINGS.alertPreferences));

      component.onEvent(params);

      component.getAlertPreferences().subscribe(() => {
        expect(component.setInitialAnalyzerAlert).toHaveBeenCalled();
      });

      expect(component.getAlertPreferences).toHaveBeenCalled();
    });

    it('should handle EventSiteChange event when alertPreferences is defined', () => {
      const params: EventParam = new EventParam(new EventSiteChange('newSiteCode'));
      const event = new NavigationEnd(1, '/report', '/report');

      eventsSubject.next(event);

      component.alertPreferences = MOCK_SITES_SETTINGS.alertPreferences;

      jest.spyOn(component, 'handleRouterEvent');

      component.onEvent(params);

      expect(component.handleRouterEvent).toBeDefined();
    });

    it('should handle EventAlertPreferencesChanged event', () => {
      const newPreferences = MOCK_SITES_SETTINGS.alertPreferences;

      const params: EventParam = new EventParam(new EventAlertPreferencesChanged(newPreferences));

      jest.spyOn(component, 'isShowingAlertBanner');

      component.onEvent(params);

      expect(component.alertPreferences).toEqual(newPreferences);

      expect(component.isShowingAlertBanner).toHaveBeenCalled();
    });
  });

  it('should correctly map analyzer statuses to HTML props', () => {
    jest.spyOn(analyzerService, 'mapQcDueStatus').mockReturnValue({
      qcDueStatus: 'QcPastDue',
      qcDueWbStatus: 'QcPastDue',
      qcDueBfStatus: '',
    });

    component.alertPreferences = MOCK_SITES_SETTINGS.alertPreferences;

    const result = component.mapAnalyzerStatusWithHTMLProps(MOCK_ANALYZER_ALERT_MSG);

    expect(result).toBeDefined();
  });

  it('should return allowed analyzer status colors', () => {
    const alertPreferences = {
      isGreen: true,
      isRed: true,
      isYellow: true,
      isGray: true,
    };

    const result = component.getAllowAnalyzerStatusColor(alertPreferences);

    expect(result).toEqual(['green', 'red', 'yellow', 'grey']);
  });

  it('should update alertAnalyzerStatusContent and mapAlertAnalyzerStatuses on received new alert', () => {
    const receiveMessageMock: ReceiveMessage<AlertAnalyzerStatusContent>[] = [
      {
        uiEventType: UIEventType.ANALYZER_STATUS,
        siteCode: '123',
        eventId: '',
        sentTime: '',
        content: {
          analyzerModel: 'model',
          analyzerSerial: 'serial',
          statusCode: 1,
          isStart: false,
          isBfMode: false,
          isBfWarning: false,
          isRequiredWbMode: false,
          enableTroubleshootingTime: '',
          approachingTime: '',
          qcDue: '',
          isCheckXbarmData: false,
        },
      },
    ];

    component.currentSiteCode = '123';

    component.onReceivedNewAlert();

    msgManagementServiceMock.onReceiveMsg.next({
      queueMsg: receiveMessageMock,
    });

    msgManagementServiceMock.onReceiveMsg.subscribe(() => {
      expect(component.alertAnalyzerStatusContent).toEqual(receiveMessageMock);
    });
  });

  it('should return "Green - Ready for Samples" for "green"', () => {
    expect(component.analyzerAlertStatusLabel('green')).toBe('Green - Ready for Samples');
  });

  it('should return "Yellow - Action Required" for "yellow"', () => {
    expect(component.analyzerAlertStatusLabel('yellow')).toBe('Yellow - Action Required');
  });

  it('should return "Red - Issue Identified" for "red"', () => {
    expect(component.analyzerAlertStatusLabel('red')).toBe('Red - Issue Identified');
  });

  it('should return "Gray - Offline" for "grey"', () => {
    expect(component.analyzerAlertStatusLabel('grey')).toBe('Gray - Offline');
  });

  it('should return an empty string for an unknown color', () => {
    expect(component.analyzerAlertStatusLabel('unknown')).toBe('');
  });

  it('should return early if event URL is /analyzer-status/summary', () => {
    jest.spyOn(component, 'setInitialAnalyzerAlert');
    jest.spyOn(component, 'getAlertPreferences').mockReturnValue(of(MOCK_SITES_SETTINGS.alertPreferences));

    const event = new NavigationEnd(1, '/analyzer-status/summary', '/analyzer-status/summary');

    component.handleRouterEvent(event);

    expect(component.getAlertPreferences).not.toHaveBeenCalled();
    expect(component.setInitialAnalyzerAlert).not.toHaveBeenCalled();
  });

  it('should return early if event URL is /analyzer-status/summary', () => {
    jest.spyOn(component, 'setInitialAnalyzerAlert');
    jest.spyOn(component, 'getAlertPreferences').mockReturnValue(of(MOCK_SITES_SETTINGS.alertPreferences));

    const event = new NavigationEnd(1, '/report', '/report');

    component.handleRouterEvent(event);

    expect(component.getAlertPreferences).toHaveBeenCalled();
    expect(component.setInitialAnalyzerAlert).toHaveBeenCalled();
  });

  it('should check analyzer status can notify if url is not  /analyzer-status/summary', () => {
    component.mapAlertAnalyzerStatuses = [
      {
        color: 'green',
        numberOfAnalyzers: 1,
      },
      {
        color: 'red',
        numberOfAnalyzers: 2,
      },
    ];
    component.isServiceL2 = false;

    jest.spyOn(component, 'loadAlertPreferencesAndSetInitialAlert');

    component.checkCanNotifyAnalyzerStatus();

    expect(component.canNotify).toBe(true);
    expect(component.loadAlertPreferencesAndSetInitialAlert).toHaveBeenCalled();
  });

  it('should check analyzer status can not notify if url is /analyzer-status/summary', () => {
    mockRouter.url = '/analyzer-status/summary';

    jest.spyOn(component, 'loadAlertPreferencesAndSetInitialAlert');

    component.checkCanNotifyAnalyzerStatus();

    expect(component.canNotify).toBe(false);
    expect(component.loadAlertPreferencesAndSetInitialAlert).not.toHaveBeenCalled();
  });
});
