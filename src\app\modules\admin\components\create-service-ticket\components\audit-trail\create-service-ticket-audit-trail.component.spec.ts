import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CreateServiceTicketAuditTrailComponent } from './create-service-ticket-audit-trail.component';
import { DialogService } from 'src/app/services/dialog.service';
import { RuleConfigurationService } from 'src/app/modules/admin/services/rule-configuration.service';

describe('CreateServiceTicketAuditTrailComponent', () => {
  let component: CreateServiceTicketAuditTrailComponent;
  let fixture: ComponentFixture<CreateServiceTicketAuditTrailComponent>;

  const mockRuleConfigurationService = {
    getRuleConfigurationAuditTrailLog: jest.fn(),
  };

  const mockDialogService = {
    openDialog$: { next: jest.fn() },
    dialogRef: { afterClosed: jest.fn() },
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        {
          provide: RuleConfigurationService,
          useValue: mockRuleConfigurationService,
        },
        {
          provide: DialogService,
          useValue: mockDialogService,
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CreateServiceTicketAuditTrailComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
