import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ActivatedRoute } from '@angular/router';
import { AgGridAngular } from 'ag-grid-angular';
import { MatDialog } from '@angular/material/dialog';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';

import { DialogService } from 'src/app/services/dialog.service';
import { EditableTableComponent } from 'src/app/modules/common/editable-table/components/editable-table.component';
import { PEER_GROUP_SIZE_SETTINGS } from '../../mocks/admin-parameters';
import { PeerGroupSizeComponent } from './peer-group-size.component';
import { RuleConfigurationService } from '../../services/rule-configuration.service';
import { SnackBarService } from 'src/app/services/snackbar.service';
import { UserStoreService } from 'src/app/services/user-store.service';

interface SnackbarServiceMock {
  displayMsg: typeof jest.fn;
}

interface AgGridAngularMock {
  api: {
    sizeColumnsToFit: typeof jest.fn;
    getColumn: typeof jest.fn;
    refreshCells: typeof jest.fn;
    forEachNode: typeof jest.fn;
    getCellRendererInstances: typeof jest.fn;
  };
}

describe('PeerGroupSizeComponent', () => {
  let component: PeerGroupSizeComponent;

  let fixture: ComponentFixture<PeerGroupSizeComponent>;

  let dialog: MatDialog;

  const mockUserStoreService = {};

  const mockRuleConfigService = {
    getPeerGroupSizeSettings: jest.fn(),
    updatePeerGroupSizeSettings: jest.fn(),
    adjustRuleValueIntegerToDecimalZero: jest.fn(),
  };

  let dialogService: DialogService;

  let agGridAngularMock: AgGridAngularMock;

  let snackbarServiceMock: jest.Mocked<SnackbarServiceMock>;

  const mockDialogService = {
    openDialog$: { next: jest.fn() },
    dialogRef: { afterClosed: jest.fn() },
  };

  beforeEach(async () => {
    const activatedRouteMock = {
      params: of({ id: '123' }),
      queryParams: of({ search: 'test' }),
      data: of({ pagecode: '123' }),
    };

    snackbarServiceMock = {
      displayMsg: jest.fn(),
    };

    agGridAngularMock = {
      api: {
        sizeColumnsToFit: jest.fn(),
        getColumn: jest.fn(),
        refreshCells: jest.fn(),
        forEachNode: jest.fn(),
        getCellRendererInstances: jest.fn(),
      },
    };

    await TestBed.configureTestingModule({
      providers: [
        {
          provide: RuleConfigurationService,
          useValue: mockRuleConfigService,
        },
        {
          provide: AgGridAngular,
          useValue: agGridAngularMock,
        },
        {
          provide: SnackBarService,
          useValue: snackbarServiceMock,
        },
        {
          provide: MatDialog,
          useValue: {
            open: jest.fn(),
            closeAll: jest.fn(),
          },
        },
        {
          provide: UserStoreService,
          useValue: mockUserStoreService,
        },
        {
          provide: ActivatedRoute,
          useValue: activatedRouteMock,
        },
        {
          provide: DialogService,
          useValue: mockDialogService,
        },
      ],
      imports: [EditableTableComponent],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PeerGroupSizeComponent);

    component = fixture.componentInstance;

    mockRuleConfigService.getPeerGroupSizeSettings.mockReturnValue(
      of([
        {
          peerGroupSizeId: '1',
          modelGroup: 'RBC/PLT&HGB',
          materialControlName: 'RBC/PLT&HGB',
          size: 1000,
        },
      ]),
    );

    mockRuleConfigService.updatePeerGroupSizeSettings.mockReturnValue(of({ status: 200 }));

    dialog = TestBed.inject(MatDialog);
    mockRuleConfigService.getPeerGroupSizeSettings.mockReturnValue(of(PEER_GROUP_SIZE_SETTINGS));

    dialogService = TestBed.inject(DialogService);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
