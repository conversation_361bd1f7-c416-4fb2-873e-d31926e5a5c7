import { FormControl } from '@angular/forms';

import { NumberValidatorDirective } from './number-validator';

describe('NumberValidatorDirective', () => {
  let directive: NumberValidatorDirective;

  beforeEach(() => {
    directive = new NumberValidatorDirective();
  });

  it('should return null if value is nullish or not a number', () => {
    expect(directive.validate(new FormControl(null))).toStrictEqual({ required: true });

    expect(directive.validate(new FormControl(undefined))).toStrictEqual({ required: true });

    expect(directive.validate(new FormControl('string'))).toBeNull();
  });

  it('should return maxError if value exceeds max', () => {
    directive.maxValue = 10;

    const control = new FormControl(15);

    expect(directive.validate(control)).toEqual({ maxError: true });
  });

  it('should return minError if value is below min', () => {
    directive.minValue = 5;

    const control = new FormControl(3);

    expect(directive.validate(control)).toEqual({ minError: true });
  });

  it('should return null if value is within min and max range', () => {
    directive.minValue = 5;

    directive.maxValue = 10;

    const control = new FormControl(7);

    expect(directive.validate(control)).toBeNull();
  });

  it('should return exceedDecimalsError if value exceeds maxDecimalPlaces', () => {
    directive.maxDecimals = 2;

    const control = new FormControl(12.345);

    expect(directive.validate(control)).toEqual({ exceedDecimalsError: true });
  });

  it('should return null if value has fewer or equal decimal places than maxDecimalPlaces', () => {
    directive.maxDecimals = 2;

    const control1 = new FormControl(12.34);

    const control2 = new FormControl(12.3);

    expect(directive.validate(control1)).toBeNull();

    expect(directive.validate(control2)).toBeNull();
  });

  it('should return null if maxDecimalPlaces is undefined', () => {
    const control = new FormControl(12.345);

    expect(directive.validate(control)).toBeNull();
  });

  it('should handle negative numbers correctly', () => {
    directive.minValue = -10;

    directive.maxValue = 0;

    const control = new FormControl(-5);

    expect(directive.validate(control)).toBeNull();
  });

  it('should return both maxError and exceedDecimalsError if value exceeds both', () => {
    directive.maxValue = 10;

    directive.maxDecimals = 2;

    const control = new FormControl(15.678);

    expect(directive.validate(control)).toEqual({
      maxError: true,
    });
  });

  it('should return minError for negative numbers if they are below the min', () => {
    directive.minValue = -5;

    const control = new FormControl(-10);

    expect(directive.validate(control)).toEqual({ minError: true });
  });
});
