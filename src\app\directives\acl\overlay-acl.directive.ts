import { Directive, ElementRef, Host, Input, On<PERSON><PERSON>roy, OnInit, Renderer2 } from '@angular/core';
import { AclWrapperDirective } from './acl-wrapper.directive';
import { EMPTY } from 'src/app/constant';
import { StorageService } from 'src/app/services/storage.service';

@Directive({
  exportAs: 'AppFunctionAcl',
  selector: 'div[appFunctionAcl], mat-card-content[appFunctionAcl]',
})
export class OverlayAclDirective implements OnInit, OnDestroy {
  @Input('appFunctionAcl') aclFunctionCode: string = EMPTY;

  private overlayContainer: HTMLElement | null = null;

  constructor(
    @Host() private readonly aclWrapper: AclWrapperDirective,
    private readonly renderer: Renderer2,
    private readonly el: ElementRef,
    private readonly storageService: StorageService,
  ) {}

  ngOnInit(): void {
    this.storageService.storageChange$.subscribe((change) => {
      if (change.storageArea === 'sessionStorage' && change.key === 'defaultSelectionSite') {
        this.updateOverlay();
      }
    });
  }

  ngOnDestroy(): void {
    this.removeOverlay();
  }

  private updateOverlay(): void {
    const isAllowed = this.aclWrapper.getFunctionPermission(this.aclFunctionCode).isAllowed;

    if (!isAllowed) {
      this.createOverlay();
    } else {
      this.removeOverlay();
    }
  }

  private createOverlay(): void {
    if (!this.overlayContainer) {
      this.overlayContainer = this.renderer.createElement('div');
      this.renderer.setStyle(this.overlayContainer, 'position', 'absolute');
      this.renderer.setStyle(this.overlayContainer, 'top', '0');
      this.renderer.setStyle(this.overlayContainer, 'left', '0');
      this.renderer.setStyle(this.overlayContainer, 'width', '100%');
      this.renderer.setStyle(this.overlayContainer, 'height', '100%');
      this.renderer.setStyle(this.overlayContainer, 'backgroundColor', 'white');
      this.renderer.setStyle(this.overlayContainer, 'display', 'flex');
      this.renderer.setStyle(this.overlayContainer, 'alignItems', 'center');
      this.renderer.setStyle(this.overlayContainer, 'justifyContent', 'center');
      this.renderer.setStyle(this.overlayContainer, 'zIndex', '1000');

      const text = this.renderer.createElement('h2');

      this.renderer.setStyle(text, 'color', 'var(--grey-400)');
      this.renderer.setStyle(text, 'fontStyle', 'italic');
      this.renderer.setStyle(text, 'fontsize', '16px');

      const textContent = this.renderer.createText(
        $localize`:app-app_message-text:Please upgrade your package to use this setting.`,
      );

      this.renderer.appendChild(text, textContent);
      this.renderer.appendChild(this.overlayContainer, text);
      this.renderer.appendChild(this.el.nativeElement, this.overlayContainer);
    }
  }

  private removeOverlay(): void {
    if (this.overlayContainer) {
      this.renderer.removeChild(this.el.nativeElement, this.overlayContainer);

      this.overlayContainer = null;
    }
  }
}
