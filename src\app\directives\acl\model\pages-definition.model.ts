import { Acl } from './acl';

export const ALL_PAGES: Acl<string> = {
  '/dashboard': 'RealtimeDashboard',

  '/analyzer-status': 'Analyzers',
  '/analyzer-status/summary': 'AnalyzerSummary',
  '/analyzer-status/file-upload': 'FileUpload',
  '/analyzer-status/analyzer-detail/:modelCode/:serialNumber': 'AnalyzerDetail',

  '/settings': 'Settings',

  '/settings/sites': 'Sites',

  '/settings/qc-parameters': 'QCParameters',

  '/settings/qc-targets-and-limits': 'QCTargetsAndLimits',
  '/settings/qc-targets-and-limits/assay-limits': 'AssayLimits',
  '/settings/qc-targets-and-limits/assay-upload': 'AssayUpload',
  '/settings/qc-targets-and-limits/customer-limits': 'CustomerLimits',

  '/settings/level3-rules': 'SysmexRules',

  '/settings/level2-rules': 'L2Rules',
  '/settings/level2-rules/multi-rules-configuration': 'MultiRulesConfiguration',
  '/settings/level2-rules/limit-type-selection': 'LimitTypeSelection',

  '/settings/troubleshooting': 'Troubleshooting',
  '/settings/troubleshooting/instruction-step': 'InstructionStep',
  '/settings/troubleshooting/channel-workflow': 'ChannelWorkflow',

  '/settings/group': 'Group',
  '/settings/group/affiliated-peer-group': 'AffiliatedPeerGroup',
  '/settings/group/error-judgment': 'ErrorJudgment',

  '/xbarm': 'XbarM',
  '/xbarm/xbarm-targets-and-limits': 'XbarMTargetsAndLimits',
  '/xbarm/xbarm-targets-and-limits/xbarm-sysmex-limit': 'XbarMSysmexLimit',
  '/xbarm/xbarm-targets-and-limits/xbarm-customer-limits': 'XbarMCustomerLimits',

  '/xbarm/xbarm-parameters': 'XbarMParameters',

  '/reports': 'Reports',
  '/reports/background-counts': 'BackgroundCount',
  '/reports/reagent-change-history-report': 'ReagentChangeHistory',
  '/reports/reagent-comparison-between-lots-report': 'ReagentLotComparision',
  '/reports/uncertainty-of-measurement-report': 'UncertaintyOfMeasurementReport',
  '/reports/periodical-report': 'PeriodicalReport',
  '/reports/peer-group-report': 'PeerGroupReport',
  '/reports/monthly-rilibak-report': 'MonthlyRilibakReport',
  '/reports/periodical-iqc-error-report': 'PeriodicalIqcErrorReport',
  '/reports/:reportID/preview': 'ReportPreview',
  '/reports/traceability-report': 'TraceabilityReport',
  '/reports/historical-report': 'HistoricalReport',
  '/reports/lot-to-lot-report': 'LotToLotReport',
  '/reports/calibration-report': 'CalibrationReport',
  '/reports/continuous-performance-verification-report': 'ContinuousPerformanceVerificationReport',
  '/reports/group-chart-report': 'GroupChartReport',

  '/user-activity': 'UserActivity',

  '/service': 'Service',
  '/service/service-mode': 'ServiceMode',
  '/service/service-reset': 'ServiceReset',
  '/service/logic-reset': 'LogicReset',

  '/resources': 'Resources',
};

export const PAGENAME_MAPPING: Acl<string> = {
  RealtimeDashboard: $localize`:app-app_page-name:Real-time Dashboard`,

  Analyzers: $localize`:app-app_page-name:Analyzers`,
  AnalyzerSummary: $localize`:app-app_page-name:Analyzer Summary`,
  FileUpload: $localize`:app-app_page-name:File Upload`,
  AnalyzerDetail: $localize`:app-app_page-name:Analyzer Detail`,

  XbarM: $localize`:app-app_page-name:XbarM`,
  XbarMParameters: $localize`:app-app_page-name:XbarM Parameters`,
  XbarMTargetsAndLimits: $localize`:app-app_page-name:XbarM Targets and Limits`,
  XbarMSysmexLimit: $localize`:app-app_page-name:XbarM Sysmex Limits`,
  XbarMCustomerLimits: $localize`:app-app_page-name:XbarM Customer Limits`,

  Reports: $localize`:app-app_page-name:Reports`,
  PeriodicalReport: $localize`:app-app_page-name:Periodical Report`,
  HistoricalReport: $localize`:app-app_page-name:Historical Report`,
  TraceabilityReport: $localize`:app-app_page-name:Traceability Report`,
  BackgroundCount: $localize`:app-app_page-name:Background Count`,
  CalibrationReport: $localize`:app-app_page-name:Calibration Report`,
  ContinuousPerformanceVerificationReport: $localize`:app-app_page-name:Continuous Performance Verification`,
  Admin: $localize`:app-app_page-name:Admin`,
  RulesConfiguration1: $localize`:app-app_page-name:Rules Configuration`,
  UncertaintyOfMeasurementReport: $localize`:app-app_page-name:Uncertainty of Measurement Report`,
  ReagentChangeHistory: $localize`:app-app_page-name:Reagent Change History Report`,
  ReagentLotComparision: $localize`:app-app_page-name:Reagent Comparison Between Lots Report`,
  PeerGroupReport: $localize`:app-app_page-name:Peer Group Report`,
  MonthlyRilibakReport: $localize`:app-app_page-name:Monthly RiliBÄK Report`,
  PeriodicalIqcErrorReport: $localize`:app-app_page-name:Periodical IQC Error Report`,
  ReportPreview: $localize`:app-app_page-name:Report Preview`,
  LotToLotReport: $localize`:app-app_page-name:Lot to Lot Report`,
  GroupChartReport: $localize`:app-app_page-name:Group Chart Report`,

  UserActivity: $localize`:app-app_page-name:User Activity`,

  Settings: $localize`:app-app_page-name:Settings`,
  Sites: $localize`:app-app_page-name:Sites`,
  QCTargetsAndLimits: $localize`:app-app_page-name:QC Targets and Limits`,
  AssayLimits: $localize`:app-app_page-name:Assay Limits`,
  CustomerLimits: $localize`:app-app_page-name:Customer Limits`,
  SysmexRules: $localize`:app-app_page-name:Level 3 Rules`,
  L2Rules: $localize`:app-app_page-name:Level 2 Rules`,
  LimitTypeSelection: $localize`:app-app_page-name:Limit Type Selection`,
  MultiRulesConfiguration: $localize`:app-app_page-name:Multi Rules Configuration`,
  Group: $localize`:app-app_page-name:Group`,
  AffiliatedPeerGroup: $localize`:app-app_page-name:Affiliated Peer Group`,
  ErrorJudgment: $localize`:app-app_page-name:Error Judgment`,
  QCParameters: $localize`:app-app_page-name:QC Parameters`,

  Troubleshooting: $localize`:app-app_page-name:Troubleshooting`,
  InstructionStep: $localize`:app-app_page-name:Instruction Step`,
  ChannelWorkflow: $localize`:app-app_page-name:Channel Workflow`,

  Service: $localize`:app-app_page-name:Service`,
  ServiceMode: $localize`:app-app_page-name:Service Mode`,
  ServiceReset: $localize`:app-app_page-name:Service Reset`,
  LogicReset: $localize`:app-app_page-name:Logic Reset`,

  Resources: $localize`:app-app_page-name:Resources`,
};

// only L3 can access these functions
export const RESTRICTED_FUNCTIONS_LOOKUP = {
  Sites: ['NotificationSetting', 'QcFrequencySetting'],
};
