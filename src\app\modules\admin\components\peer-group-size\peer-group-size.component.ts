import { Component, OnInit, ViewChild } from '@angular/core';
import { ColDef } from 'ag-grid-community';
import { CommonModule } from '@angular/common';

import { CELL_STYLE_DEFAULT, RULES } from '../../constants';
import { AclService } from 'src/app/services/acl.service';
import { EditableTableComponent } from 'src/app/modules/common/editable-table/components/editable-table.component';
import { NumberInputCellComponent } from '../cell-template/number-input-cell/number-input-cell.component';
import { PeerGroupSizeAuditTrailComponent } from './components/audit-trail/peer-group-size-audit-trail.component';
import { PeerGroupSizeSettings } from '../../interfaces/admin-parameters';
import { PmsService } from 'src/app/services/app-permission.service';
import { RuleConfigurationAuditTrailDialogParams } from '../../interfaces/rule-configuration-audit-trail';
import { RuleConfigurationService } from '../../services/rule-configuration.service';
import { RuleDataMappingFunc } from '../../interfaces/rule';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-peer-group-size',
  templateUrl: './peer-group-size.component.html',
  standalone: true,
  imports: [EditableTableComponent, CommonModule, PeerGroupSizeAuditTrailComponent],
  providers: [],
})
export class PeerGroupSizeComponent implements OnInit {
  @ViewChild('ruleConfigTable', { static: true }) ruleConfigTable!: EditableTableComponent;

  tableDataBackup: PeerGroupSizeSettings[] = [];

  ruleCode = RULES.rule1;

  columnDefs: ColDef[] = [
    {
      field: 'modelGroupDisplay',
      headerName: $localize`:app-app_grid-header_label:Model Group`,
      cellStyle: CELL_STYLE_DEFAULT,
    },
    {
      field: 'materialControlName',
      headerName: $localize`:app-app_grid-header_label:Material Control`,
      cellStyle: CELL_STYLE_DEFAULT,
    },
    {
      field: 'size',
      headerName: $localize`:app-app_grid-header_label:Number of Analyzers`,
      cellClass: 'text-left',
      headerClass: 'text-left',
      editable: false,
      autoHeight: true,
      type: 'number',
      cellRenderer: NumberInputCellComponent,
      cellRendererParams: {
        constraints: {
          allowDecimals: false,
          min: 1,
          max: 100000,
          minIncluded: true,
          maxIncluded: true,
        },
      },
      cellStyle: CELL_STYLE_DEFAULT,
    },
  ];

  editableColumns = ['size'];

  isShowAuditTrail = false;

  openAuditTrailDialog$ = new Subject<RuleConfigurationAuditTrailDialogParams>();

  dataMappingFunc: RuleDataMappingFunc = (data: PeerGroupSizeSettings) => ({
    peerGroupSizeId: data.peerGroupSizeId,
    modelGroup: data.modelGroup,
    materialControlName: data.materialControlName,
    size: data.size,
  });

  tableTitle = $localize`:app-app_table-name:Peer Group Size`;

  constructor(
    private readonly ruleConfigService: RuleConfigurationService,
    private readonly pmsService: PmsService,
    private readonly aclService: AclService,
  ) {}

  ngOnInit(): void {
    this.isShowAuditTrail = this.aclService.getPageWritePms(
      this.pmsService.page,
      this.aclService.pageCode,
      this.aclService.parentCode,
    );

    this.getPeerGroupSizeSettings();
  }

  onSave(data: PeerGroupSizeSettings[]): void {
    this.ruleConfigTable.saveData(() => this.ruleConfigService.updatePeerGroupSizeSettings('rule1', data));
  }

  getPeerGroupSizeSettings() {
    this.ruleConfigService
      .getPeerGroupSizeSettings('rule1')
      .subscribe((resp: PeerGroupSizeSettings[]) => (this.tableDataBackup = resp));
  }

  onOpenAuditTrail({ peerGroupSizeId }: PeerGroupSizeSettings): void {
    if (peerGroupSizeId) {
      this.openAuditTrailDialog$.next({
        logKey: peerGroupSizeId,
      });
    }
  }
}
