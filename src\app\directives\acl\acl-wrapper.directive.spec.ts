import { ActivatedRoute } from '@angular/router';
import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';

import { AclWrapperDirective } from './acl-wrapper.directive';
import { DEFAULT_PERMISSION } from 'src/app/constant';
import { MOCK_PMS_RESULTS } from './mock/permission-response-mock';
import { PmsService } from 'src/app/services/app-permission.service';
import { UserStoreService } from 'src/app/services/user-store.service';

describe('AclWrapperDirective', () => {
  let directive: AclWrapperDirective;
  let mockPermissionService: Partial<PmsService>;
  let mockUserStoreService: Partial<UserStoreService>;

  beforeEach(() => {
    mockPermissionService = {
      page: MOCK_PMS_RESULTS,
    };

    const activatedRouteMock = {
      params: of({ id: '123' }),
      queryParams: of({ search: 'test' }),
      data: of({ pagecode: '123' }),
    };

    mockUserStoreService = {};

    TestBed.configureTestingModule({
      providers: [
        AclWrapperDirective,
        {
          provide: PmsService,
          useValue: mockPermissionService,
        },
        {
          provide: UserStoreService,
          useValue: mockUserStoreService,
        },
        {
          provide: ActivatedRoute,
          useValue: activatedRouteMock,
        },
      ],
    });

    directive = TestBed.inject(AclWrapperDirective);
  });

  describe('getPermissionsByPageCode', () => {
    it('should return permissions for a found page', () => {
      const pageCode = 'AnalyzerStatus';
      const permissions = directive.getPermissionsByPageCode(pageCode);

      expect(permissions).toEqual({
        read: true,
        write: false,
      });
    });

    it('should return default permissions for a not found page', () => {
      const pageCode = 'nonexistentPage';
      const permissions = directive.getPermissionsByPageCode(pageCode);

      expect(permissions).toEqual({
        read: DEFAULT_PERMISSION.read,
        write: DEFAULT_PERMISSION.write,
      });
    });
  });
});
