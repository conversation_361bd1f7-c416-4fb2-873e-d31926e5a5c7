import { BehaviorSubject, Subject, finalize } from 'rxjs';
import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';

import { AclModule } from 'src/app/directives/acl/acl.module';
import { CreateServiceTicketAuditTrailComponent }
  from './components/audit-trail/create-service-ticket-audit-trail.component';
import { CreateServiceTicketService } from '../../services/create-service-ticket.service';
import { DialogComponent } from 'src/app/dialog/dialog.component';
import { DialogService } from 'src/app/services/dialog.service';
import { EMPTY } from '../../../../constant';
import { ERROR_CODE } from 'src/app/enums/errors';
import { ErrorMessage } from 'src/app/helpers/ErrorMessage';
import { RuleConfigurationAuditTrailDialogParams } from '../../interfaces/rule-configuration-audit-trail';
import { RuleConfigurationService } from '../../services/rule-configuration.service';
import { SETTING_KEY } from '../../constants';
import { SnackBarService } from 'src/app/services/snackbar.service';

@Component({
  selector: 'app-create-service-ticket',
  templateUrl: './create-service-ticket.component.html',
  standalone: true,
  imports: [
    MatSlideToggleModule,
    MatCheckboxModule,
    MatButtonModule,
    FormsModule,
    CommonModule,
    AclModule,
    CreateServiceTicketAuditTrailComponent,
  ],
  providers: [CreateServiceTicketService],
})
export class CreateServiceTicketComponent implements OnInit {
  @Input() ruleCode: string = EMPTY;

  @Input() isEnabled = true;

  @Input() isCreateServiceTicket = true;

  @ViewChild('dialogContent') dialogContent: TemplateRef<unknown> | null = null;

  dialogRef: MatDialogRef<DialogComponent> | undefined;

  isEnabledBackup = true;

  isCreateServiceTicketBackup = true;

  enableToggleLabel = new BehaviorSubject<string>($localize`:app-app_label:Enabled`);

  openAuditTrailDialog$ = new Subject<RuleConfigurationAuditTrailDialogParams>();

  constructor(
    private readonly ruleConfigService: RuleConfigurationService,
    private readonly snackBarService: SnackBarService,
    private readonly dialog: MatDialog,
    private readonly dialogService: DialogService,
    private readonly createServiceTicketService: CreateServiceTicketService,
  ) {}

  ngOnInit(): void {
    this.fetchData();
  }

  fetchData() {
    this.createServiceTicketService.getStandardRuleSettings(this.ruleCode).subscribe((resp) => {
      this.isEnabled = resp.isEnabled ?? true;
      this.isCreateServiceTicket = resp.isCreateServiceTicket ?? true;
      this.isEnabledBackup = this.isEnabled;
      this.isCreateServiceTicketBackup = this.isCreateServiceTicket;
      this.changeEnableLabel();
    });
  }

  onChangeEnable(event: MatSlideToggleChange) {
    this.openDialog('enable', event.checked);
  }

  onChangeCreateServiceTicket(event: MatCheckboxChange) {
    this.openDialog('ticket', event.checked);
  }

  openDialog(settingKey: SETTING_KEY, settingValue: boolean) {
    let isSaved = false;

    const dialogData = {
      data: {
        title: $localize`:app-app_dialog-title:Save changes`,
        content: this.dialogContent,
        actions: [
          {
            label: $localize`:@@app-app_button-cancel:Cancel`,
            close: true,
            color: 'ghost',
            skipPermission: true,
            disable: false,
            onClick: () => {
              this.dialog.closeAll();
            },
          },
          {
            label: $localize`:@@app-app_button-save:Save`,
            color: 'primary',
            skipPermission: true,
            disable: false,
            onClick: () => {
              isSaved = true;

              this.updateStandardSettings(settingKey, settingValue);

              this.dialog.closeAll();
            },
          },
        ],
        showCloseIcon: true,
      },
    };

    this.dialogService.openDialog$.next(dialogData);

    this.dialogService.dialogRef?.afterClosed().subscribe(() => {
      if (!isSaved) {
        this.revertBackupData();
      }
    });
  }

  updateStandardSettings(settingKey: SETTING_KEY, settingValue: boolean) {
    this.ruleConfigService
      .updateStandardSettings(this.ruleCode, settingKey, settingValue)
      .pipe(
        finalize(() => {
          this.changeEnableLabel();
        }),
      )
      .subscribe({
        next: () => {
          this.snackBarService.displayMsg(ErrorMessage.getMessage(ERROR_CODE.SM001), {
            type: 'success',
          });

          this.backupData();
        },
        error: () => {
          this.snackBarService.displayMsg(ErrorMessage.getMessage(ERROR_CODE.EM003), {
            type: 'error',
          });

          this.revertBackupData();
        },
      });
  }

  backupData() {
    this.isEnabledBackup = this.isEnabled;

    this.isCreateServiceTicketBackup = this.isCreateServiceTicket;

    this.changeEnableLabel();
  }

  revertBackupData() {
    this.isEnabled = this.isEnabledBackup;

    this.isCreateServiceTicket = this.isCreateServiceTicketBackup;

    this.changeEnableLabel();
  }

  changeEnableLabel() {
    this.enableToggleLabel.next(
      this.isEnabled ? $localize`:app-app_label:Enabled` : $localize`:app-app_label:Disabled`,
    );
  }

  onOpenAuditTrail(): void {
    if (this.ruleCode) {
      this.openAuditTrailDialog$.next({ logKey: this.ruleCode });
    }
  }
}
