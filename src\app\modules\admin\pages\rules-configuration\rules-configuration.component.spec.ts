import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTabChangeEvent } from '@angular/material/tabs';

import { RULES } from '../../constants';
import { RuleConfigurationService } from '../../services/rule-configuration.service';
import { RulesConfigurationComponent } from './rules-configuration.component';
import { of } from 'rxjs';

describe('RulesConfigurationComponent', () => {
  let component: RulesConfigurationComponent;

  let fixture: ComponentFixture<RulesConfigurationComponent>;

  const ruleConfigServiceMock = {
    getStandardSettings: jest.fn(),
  };

  const rules = Object.values(RULES);

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [RulesConfigurationComponent],
      providers: [
        {
          provide: RuleConfigurationService,
          useValue: ruleConfigServiceMock,
        },
      ],
      imports: [MatCardModule, CommonModule],
    }).compileComponents();
  });

  beforeEach(() => {
    ruleConfigServiceMock.getStandardSettings.mockReturnValue(of(null));

    fixture = TestBed.createComponent(RulesConfigurationComponent);

    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update hasAccesibleSite and ruleStandardSettings on getStandardSettings success', () => {
    const mockResult = {
      ruleId: 1,
      ruleCode: 'rule1',
      keyName: 'rule1',
      description: 'Rule 1 - Single Run Failure',
      isEnabled: false,
      isCreateServiceTicket: true,
      requiredQcDataPoint: 150,
    };

    ruleConfigServiceMock.getStandardSettings.mockReturnValue(of(mockResult));

    component.getStandardSettings(rules[0]);

    fixture.detectChanges();

    expect(ruleConfigServiceMock.getStandardSettings).toHaveBeenCalledWith(rules[0]);

    expect(component.hasAccessibleSite).toEqual(true);

    expect(component.ruleStandardSettings).toEqual(mockResult);
  });

  it('should handle getStandardSettings with no result', () => {
    ruleConfigServiceMock.getStandardSettings.mockReturnValue(of(null));

    component.getStandardSettings(rules[10]);
    fixture.detectChanges();

    expect(component.hasAccessibleSite).toEqual(false);
    expect(component.ruleStandardSettings).toBeUndefined();
  });

  it('should set localized description for RULE5', () => {
    const mockResult = {
      ruleId: 5,
      ruleCode: 'rule5',
      description: 'Rule 5 - Check Precision Recent Runs',
      isEnabled: false,
      isCreateServiceTicket: true,
      requiredQcDataPoint: 150,
    };

    ruleConfigServiceMock.getStandardSettings.mockReturnValue(of(mockResult));

    component.getStandardSettings(rules[4]);

    expect(component.hasAccessibleSite).toBe(true);
    expect(component.ruleStandardSettings).toEqual(mockResult);
    expect(component.ruleStandardSettings?.description).toBe('Rule 5 - Check Precision Recent Runs');
  });

  it('should call getStandardSettings with correct ruleCode on tab change', () => {
    const mockIndex = 0;

    // 3rd party library class, must using mock object by casting type
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const mockEvent = { index: mockIndex } as MatTabChangeEvent;

    jest.spyOn(component, 'getStandardSettings');

    component.rules = ['rule1', 'rule2', 'rule3', 'rule4'];

    component.onTabChange(mockEvent);

    expect(component.getStandardSettings).toHaveBeenCalledWith(component.rules[mockIndex]);
  });
});
