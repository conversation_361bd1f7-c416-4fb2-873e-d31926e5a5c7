(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))i(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const r of s.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&i(r)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function i(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function pi(e,t){const n=Object.create(null),i=e.split(",");for(let o=0;o<i.length;o++)n[i[o]]=!0;return t?o=>!!n[o.toLowerCase()]:o=>!!n[o]}const Y={},_t=[],Ee=()=>{},sr=()=>!1,rr=/^on[^a-z]/,vn=e=>rr.test(e),mi=e=>e.startsWith("onUpdate:"),oe=Object.assign,gi=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},cr=Object.prototype.hasOwnProperty,H=(e,t)=>cr.call(e,t),K=Array.isArray,bt=e=>kn(e)==="[object Map]",Zo=e=>kn(e)==="[object Set]",U=e=>typeof e=="function",se=e=>typeof e=="string",yi=e=>typeof e=="symbol",G=e=>e!==null&&typeof e=="object",Bo=e=>G(e)&&U(e.then)&&U(e.catch),Vo=Object.prototype.toString,kn=e=>Vo.call(e),ar=e=>kn(e).slice(8,-1),Xo=e=>kn(e)==="[object Object]",wi=e=>se(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,un=pi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),xn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},lr=/-(\w)/g,xt=xn(e=>e.replace(lr,(t,n)=>n?n.toUpperCase():"")),ur=/\B([A-Z])/g,St=xn(e=>e.replace(ur,"-$1").toLowerCase()),Jo=xn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Nn=xn(e=>e?`on${Jo(e)}`:""),Bt=(e,t)=>!Object.is(e,t),zn=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},mn=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},fr=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Vi;const Yn=()=>Vi||(Vi=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function _i(e){if(K(e)){const t={};for(let n=0;n<e.length;n++){const i=e[n],o=se(i)?mr(i):_i(i);if(o)for(const s in o)t[s]=o[s]}return t}else{if(se(e))return e;if(G(e))return e}}const dr=/;(?![^(]*\))/g,hr=/:([^]+)/,pr=/\/\*[^]*?\*\//g;function mr(e){const t={};return e.replace(pr,"").split(dr).forEach(n=>{if(n){const i=n.split(hr);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function bi(e){let t="";if(se(e))t=e;else if(K(e))for(let n=0;n<e.length;n++){const i=bi(e[n]);i&&(t+=i+" ")}else if(G(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const gr="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",yr=pi(gr);function Yo(e){return!!e||e===""}const wr=e=>se(e)?e:e==null?"":K(e)||G(e)&&(e.toString===Vo||!U(e.toString))?JSON.stringify(e,qo,2):String(e),qo=(e,t)=>t&&t.__v_isRef?qo(e,t.value):bt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[i,o])=>(n[`${i} =>`]=o,n),{})}:Zo(t)?{[`Set(${t.size})`]:[...t.values()]}:G(t)&&!K(t)&&!Xo(t)?String(t):t;let be;class _r{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=be,!t&&be&&(this.index=(be.scopes||(be.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=be;try{return be=this,t()}finally{be=n}}}on(){be=this}off(){be=this.parent}stop(t){if(this._active){let n,i;for(n=0,i=this.effects.length;n<i;n++)this.effects[n].stop();for(n=0,i=this.cleanups.length;n<i;n++)this.cleanups[n]();if(this.scopes)for(n=0,i=this.scopes.length;n<i;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0,this._active=!1}}}function br(e,t=be){t&&t.active&&t.effects.push(e)}function vr(){return be}const vi=e=>{const t=new Set(e);return t.w=0,t.n=0,t},Go=e=>(e.w&Qe)>0,Qo=e=>(e.n&Qe)>0,kr=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=Qe},xr=e=>{const{deps:t}=e;if(t.length){let n=0;for(let i=0;i<t.length;i++){const o=t[i];Go(o)&&!Qo(o)?o.delete(e):t[n++]=o,o.w&=~Qe,o.n&=~Qe}t.length=n}},qn=new WeakMap;let Ft=0,Qe=1;const Gn=30;let ve;const at=Symbol(""),Qn=Symbol("");class ki{constructor(t,n=null,i){this.fn=t,this.scheduler=n,this.active=!0,this.deps=[],this.parent=void 0,br(this,i)}run(){if(!this.active)return this.fn();let t=ve,n=Ye;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=ve,ve=this,Ye=!0,Qe=1<<++Ft,Ft<=Gn?kr(this):Xi(this),this.fn()}finally{Ft<=Gn&&xr(this),Qe=1<<--Ft,ve=this.parent,Ye=n,this.parent=void 0,this.deferStop&&this.stop()}}stop(){ve===this?this.deferStop=!0:this.active&&(Xi(this),this.onStop&&this.onStop(),this.active=!1)}}function Xi(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Ye=!0;const es=[];function Tt(){es.push(Ye),Ye=!1}function Rt(){const e=es.pop();Ye=e===void 0?!0:e}function pe(e,t,n){if(Ye&&ve){let i=qn.get(e);i||qn.set(e,i=new Map);let o=i.get(n);o||i.set(n,o=vi()),ts(o)}}function ts(e,t){let n=!1;Ft<=Gn?Qo(e)||(e.n|=Qe,n=!Go(e)):n=!e.has(ve),n&&(e.add(ve),ve.deps.push(e))}function De(e,t,n,i,o,s){const r=qn.get(e);if(!r)return;let a=[];if(t==="clear")a=[...r.values()];else if(n==="length"&&K(e)){const c=Number(i);r.forEach((u,l)=>{(l==="length"||l>=c)&&a.push(u)})}else switch(n!==void 0&&a.push(r.get(n)),t){case"add":K(e)?wi(n)&&a.push(r.get("length")):(a.push(r.get(at)),bt(e)&&a.push(r.get(Qn)));break;case"delete":K(e)||(a.push(r.get(at)),bt(e)&&a.push(r.get(Qn)));break;case"set":bt(e)&&a.push(r.get(at));break}if(a.length===1)a[0]&&ei(a[0]);else{const c=[];for(const u of a)u&&c.push(...u);ei(vi(c))}}function ei(e,t){const n=K(e)?e:[...e];for(const i of n)i.computed&&Ji(i);for(const i of n)i.computed||Ji(i)}function Ji(e,t){(e!==ve||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const Er=pi("__proto__,__v_isRef,__isVue"),ns=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(yi)),Ir=xi(),Or=xi(!1,!0),Pr=xi(!0),Yi=Cr();function Cr(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const i=W(this);for(let s=0,r=this.length;s<r;s++)pe(i,"get",s+"");const o=i[t](...n);return o===-1||o===!1?i[t](...n.map(W)):o}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){Tt();const i=W(this)[t].apply(this,n);return Rt(),i}}),e}function Sr(e){const t=W(this);return pe(t,"has",e),t.hasOwnProperty(e)}function xi(e=!1,t=!1){return function(i,o,s){if(o==="__v_isReactive")return!e;if(o==="__v_isReadonly")return e;if(o==="__v_isShallow")return t;if(o==="__v_raw"&&s===(e?t?Zr:cs:t?rs:ss).get(i))return i;const r=K(i);if(!e){if(r&&H(Yi,o))return Reflect.get(Yi,o,s);if(o==="hasOwnProperty")return Sr}const a=Reflect.get(i,o,s);return(yi(o)?ns.has(o):Er(o))||(e||pe(i,"get",o),t)?a:ue(a)?r&&wi(o)?a:a.value:G(a)?e?ls(a):In(a):a}}const Tr=is(),Rr=is(!0);function is(e=!1){return function(n,i,o,s){let r=n[i];if(Et(r)&&ue(r)&&!ue(o))return!1;if(!e&&(!gn(o)&&!Et(o)&&(r=W(r),o=W(o)),!K(n)&&ue(r)&&!ue(o)))return r.value=o,!0;const a=K(n)&&wi(i)?Number(i)<n.length:H(n,i),c=Reflect.set(n,i,o,s);return n===W(s)&&(a?Bt(o,r)&&De(n,"set",i,o):De(n,"add",i,o)),c}}function jr(e,t){const n=H(e,t);e[t];const i=Reflect.deleteProperty(e,t);return i&&n&&De(e,"delete",t,void 0),i}function Ar(e,t){const n=Reflect.has(e,t);return(!yi(t)||!ns.has(t))&&pe(e,"has",t),n}function Mr(e){return pe(e,"iterate",K(e)?"length":at),Reflect.ownKeys(e)}const os={get:Ir,set:Tr,deleteProperty:jr,has:Ar,ownKeys:Mr},Lr={get:Pr,set(e,t){return!0},deleteProperty(e,t){return!0}},Nr=oe({},os,{get:Or,set:Rr}),Ei=e=>e,En=e=>Reflect.getPrototypeOf(e);function nn(e,t,n=!1,i=!1){e=e.__v_raw;const o=W(e),s=W(t);n||(t!==s&&pe(o,"get",t),pe(o,"get",s));const{has:r}=En(o),a=i?Ei:n?Pi:Vt;if(r.call(o,t))return a(e.get(t));if(r.call(o,s))return a(e.get(s));e!==o&&e.get(t)}function on(e,t=!1){const n=this.__v_raw,i=W(n),o=W(e);return t||(e!==o&&pe(i,"has",e),pe(i,"has",o)),e===o?n.has(e):n.has(e)||n.has(o)}function sn(e,t=!1){return e=e.__v_raw,!t&&pe(W(e),"iterate",at),Reflect.get(e,"size",e)}function qi(e){e=W(e);const t=W(this);return En(t).has.call(t,e)||(t.add(e),De(t,"add",e,e)),this}function Gi(e,t){t=W(t);const n=W(this),{has:i,get:o}=En(n);let s=i.call(n,e);s||(e=W(e),s=i.call(n,e));const r=o.call(n,e);return n.set(e,t),s?Bt(t,r)&&De(n,"set",e,t):De(n,"add",e,t),this}function Qi(e){const t=W(this),{has:n,get:i}=En(t);let o=n.call(t,e);o||(e=W(e),o=n.call(t,e)),i&&i.call(t,e);const s=t.delete(e);return o&&De(t,"delete",e,void 0),s}function eo(){const e=W(this),t=e.size!==0,n=e.clear();return t&&De(e,"clear",void 0,void 0),n}function rn(e,t){return function(i,o){const s=this,r=s.__v_raw,a=W(r),c=t?Ei:e?Pi:Vt;return!e&&pe(a,"iterate",at),r.forEach((u,l)=>i.call(o,c(u),c(l),s))}}function cn(e,t,n){return function(...i){const o=this.__v_raw,s=W(o),r=bt(s),a=e==="entries"||e===Symbol.iterator&&r,c=e==="keys"&&r,u=o[e](...i),l=n?Ei:t?Pi:Vt;return!t&&pe(s,"iterate",c?Qn:at),{next(){const{value:f,done:h}=u.next();return h?{value:f,done:h}:{value:a?[l(f[0]),l(f[1])]:l(f),done:h}},[Symbol.iterator](){return this}}}}function Be(e){return function(...t){return e==="delete"?!1:this}}function zr(){const e={get(s){return nn(this,s)},get size(){return sn(this)},has:on,add:qi,set:Gi,delete:Qi,clear:eo,forEach:rn(!1,!1)},t={get(s){return nn(this,s,!1,!0)},get size(){return sn(this)},has:on,add:qi,set:Gi,delete:Qi,clear:eo,forEach:rn(!1,!0)},n={get(s){return nn(this,s,!0)},get size(){return sn(this,!0)},has(s){return on.call(this,s,!0)},add:Be("add"),set:Be("set"),delete:Be("delete"),clear:Be("clear"),forEach:rn(!0,!1)},i={get(s){return nn(this,s,!0,!0)},get size(){return sn(this,!0)},has(s){return on.call(this,s,!0)},add:Be("add"),set:Be("set"),delete:Be("delete"),clear:Be("clear"),forEach:rn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=cn(s,!1,!1),n[s]=cn(s,!0,!1),t[s]=cn(s,!1,!0),i[s]=cn(s,!0,!0)}),[e,n,t,i]}const[Fr,Kr,Ur,Dr]=zr();function Ii(e,t){const n=t?e?Dr:Ur:e?Kr:Fr;return(i,o,s)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?i:Reflect.get(H(n,o)&&o in i?n:i,o,s)}const Hr={get:Ii(!1,!1)},Wr={get:Ii(!1,!0)},$r={get:Ii(!0,!1)},ss=new WeakMap,rs=new WeakMap,cs=new WeakMap,Zr=new WeakMap;function Br(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Vr(e){return e.__v_skip||!Object.isExtensible(e)?0:Br(ar(e))}function In(e){return Et(e)?e:Oi(e,!1,os,Hr,ss)}function as(e){return Oi(e,!1,Nr,Wr,rs)}function ls(e){return Oi(e,!0,Lr,$r,cs)}function Oi(e,t,n,i,o){if(!G(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=o.get(e);if(s)return s;const r=Vr(e);if(r===0)return e;const a=new Proxy(e,r===2?i:n);return o.set(e,a),a}function vt(e){return Et(e)?vt(e.__v_raw):!!(e&&e.__v_isReactive)}function Et(e){return!!(e&&e.__v_isReadonly)}function gn(e){return!!(e&&e.__v_isShallow)}function us(e){return vt(e)||Et(e)}function W(e){const t=e&&e.__v_raw;return t?W(t):e}function fs(e){return mn(e,"__v_skip",!0),e}const Vt=e=>G(e)?In(e):e,Pi=e=>G(e)?ls(e):e;function ds(e){Ye&&ve&&(e=W(e),ts(e.dep||(e.dep=vi())))}function hs(e,t){e=W(e);const n=e.dep;n&&ei(n)}function ue(e){return!!(e&&e.__v_isRef===!0)}function ge(e){return ps(e,!1)}function Xr(e){return ps(e,!0)}function ps(e,t){return ue(e)?e:new Jr(e,t)}class Jr{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:W(t),this._value=n?t:Vt(t)}get value(){return ds(this),this._value}set value(t){const n=this.__v_isShallow||gn(t)||Et(t);t=n?t:W(t),Bt(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:Vt(t),hs(this))}}function qe(e){return ue(e)?e.value:e}const Yr={get:(e,t,n)=>qe(Reflect.get(e,t,n)),set:(e,t,n,i)=>{const o=e[t];return ue(o)&&!ue(n)?(o.value=n,!0):Reflect.set(e,t,n,i)}};function ms(e){return vt(e)?e:new Proxy(e,Yr)}class qr{constructor(t,n,i,o){this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new ki(t,()=>{this._dirty||(this._dirty=!0,hs(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=i}get value(){const t=W(this);return ds(t),(t._dirty||!t._cacheable)&&(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}function Gr(e,t,n=!1){let i,o;const s=U(e);return s?(i=e,o=Ee):(i=e.get,o=e.set),new qr(i,o,s||!o,n)}function Ge(e,t,n,i){let o;try{o=i?e(...i):e()}catch(s){On(s,t,n)}return o}function Ie(e,t,n,i){if(U(e)){const s=Ge(e,t,n,i);return s&&Bo(s)&&s.catch(r=>{On(r,t,n)}),s}const o=[];for(let s=0;s<e.length;s++)o.push(Ie(e[s],t,n,i));return o}function On(e,t,n,i=!0){const o=t?t.vnode:null;if(t){let s=t.parent;const r=t.proxy,a=n;for(;s;){const u=s.ec;if(u){for(let l=0;l<u.length;l++)if(u[l](e,r,a)===!1)return}s=s.parent}const c=t.appContext.config.errorHandler;if(c){Ge(c,null,10,[e,r,a]);return}}Qr(e,n,o,i)}function Qr(e,t,n,i=!0){console.error(e)}let Xt=!1,ti=!1;const ae=[];let Le=0;const kt=[];let Ue=null,st=0;const gs=Promise.resolve();let Ci=null;function ys(e){const t=Ci||gs;return e?t.then(this?e.bind(this):e):t}function ec(e){let t=Le+1,n=ae.length;for(;t<n;){const i=t+n>>>1;Jt(ae[i])<e?t=i+1:n=i}return t}function Si(e){(!ae.length||!ae.includes(e,Xt&&e.allowRecurse?Le+1:Le))&&(e.id==null?ae.push(e):ae.splice(ec(e.id),0,e),ws())}function ws(){!Xt&&!ti&&(ti=!0,Ci=gs.then(bs))}function tc(e){const t=ae.indexOf(e);t>Le&&ae.splice(t,1)}function nc(e){K(e)?kt.push(...e):(!Ue||!Ue.includes(e,e.allowRecurse?st+1:st))&&kt.push(e),ws()}function to(e,t=Xt?Le+1:0){for(;t<ae.length;t++){const n=ae[t];n&&n.pre&&(ae.splice(t,1),t--,n())}}function _s(e){if(kt.length){const t=[...new Set(kt)];if(kt.length=0,Ue){Ue.push(...t);return}for(Ue=t,Ue.sort((n,i)=>Jt(n)-Jt(i)),st=0;st<Ue.length;st++)Ue[st]();Ue=null,st=0}}const Jt=e=>e.id==null?1/0:e.id,ic=(e,t)=>{const n=Jt(e)-Jt(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function bs(e){ti=!1,Xt=!0,ae.sort(ic);const t=Ee;try{for(Le=0;Le<ae.length;Le++){const n=ae[Le];n&&n.active!==!1&&Ge(n,null,14)}}finally{Le=0,ae.length=0,_s(),Xt=!1,Ci=null,(ae.length||kt.length)&&bs()}}function oc(e,t,...n){if(e.isUnmounted)return;const i=e.vnode.props||Y;let o=n;const s=t.startsWith("update:"),r=s&&t.slice(7);if(r&&r in i){const l=`${r==="modelValue"?"model":r}Modifiers`,{number:f,trim:h}=i[l]||Y;h&&(o=n.map(g=>se(g)?g.trim():g)),f&&(o=n.map(fr))}let a,c=i[a=Nn(t)]||i[a=Nn(xt(t))];!c&&s&&(c=i[a=Nn(St(t))]),c&&Ie(c,e,6,o);const u=i[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Ie(u,e,6,o)}}function vs(e,t,n=!1){const i=t.emitsCache,o=i.get(e);if(o!==void 0)return o;const s=e.emits;let r={},a=!1;if(!U(e)){const c=u=>{const l=vs(u,t,!0);l&&(a=!0,oe(r,l))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!s&&!a?(G(e)&&i.set(e,null),null):(K(s)?s.forEach(c=>r[c]=null):oe(r,s),G(e)&&i.set(e,r),r)}function Pn(e,t){return!e||!vn(t)?!1:(t=t.slice(2).replace(/Once$/,""),H(e,t[0].toLowerCase()+t.slice(1))||H(e,St(t))||H(e,t))}let Ne=null,ks=null;function yn(e){const t=Ne;return Ne=e,ks=e&&e.type.__scopeId||null,t}function sc(e,t=Ne,n){if(!t||e._n)return e;const i=(...o)=>{i._d&&fo(-1);const s=yn(t);let r;try{r=e(...o)}finally{yn(s),i._d&&fo(1)}return r};return i._n=!0,i._c=!0,i._d=!0,i}function Fn(e){const{type:t,vnode:n,proxy:i,withProxy:o,props:s,propsOptions:[r],slots:a,attrs:c,emit:u,render:l,renderCache:f,data:h,setupState:g,ctx:w,inheritAttrs:E}=e;let T,I;const v=yn(e);try{if(n.shapeFlag&4){const A=o||i;T=Ae(l.call(A,A,f,s,g,h,w)),I=c}else{const A=t;T=Ae(A.length>1?A(s,{attrs:c,slots:a,emit:u}):A(s,null)),I=t.props?c:rc(c)}}catch(A){Wt.length=0,On(A,e,1),T=ye(Yt)}let L=T;if(I&&E!==!1){const A=Object.keys(I),{shapeFlag:q}=L;A.length&&q&7&&(r&&A.some(mi)&&(I=cc(I,r)),L=It(L,I))}return n.dirs&&(L=It(L),L.dirs=L.dirs?L.dirs.concat(n.dirs):n.dirs),n.transition&&(L.transition=n.transition),T=L,yn(v),T}const rc=e=>{let t;for(const n in e)(n==="class"||n==="style"||vn(n))&&((t||(t={}))[n]=e[n]);return t},cc=(e,t)=>{const n={};for(const i in e)(!mi(i)||!(i.slice(9)in t))&&(n[i]=e[i]);return n};function ac(e,t,n){const{props:i,children:o,component:s}=e,{props:r,children:a,patchFlag:c}=t,u=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return i?no(i,r,u):!!r;if(c&8){const l=t.dynamicProps;for(let f=0;f<l.length;f++){const h=l[f];if(r[h]!==i[h]&&!Pn(u,h))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:i===r?!1:i?r?no(i,r,u):!0:!!r;return!1}function no(e,t,n){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let o=0;o<i.length;o++){const s=i[o];if(t[s]!==e[s]&&!Pn(n,s))return!0}return!1}function lc({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}const uc=e=>e.__isSuspense;function fc(e,t){t&&t.pendingBranch?K(e)?t.effects.push(...e):t.effects.push(e):nc(e)}const an={};function Dt(e,t,n){return xs(e,t,n)}function xs(e,t,{immediate:n,deep:i,flush:o,onTrack:s,onTrigger:r}=Y){var a;const c=vr()===((a=le)==null?void 0:a.scope)?le:null;let u,l=!1,f=!1;if(ue(e)?(u=()=>e.value,l=gn(e)):vt(e)?(u=()=>e,i=!0):K(e)?(f=!0,l=e.some(A=>vt(A)||gn(A)),u=()=>e.map(A=>{if(ue(A))return A.value;if(vt(A))return yt(A);if(U(A))return Ge(A,c,2)})):U(e)?t?u=()=>Ge(e,c,2):u=()=>{if(!(c&&c.isUnmounted))return h&&h(),Ie(e,c,3,[g])}:u=Ee,t&&i){const A=u;u=()=>yt(A())}let h,g=A=>{h=v.onStop=()=>{Ge(A,c,4)}},w;if(Gt)if(g=Ee,t?n&&Ie(t,c,3,[u(),f?[]:void 0,g]):u(),o==="sync"){const A=oa();w=A.__watcherHandles||(A.__watcherHandles=[])}else return Ee;let E=f?new Array(e.length).fill(an):an;const T=()=>{if(v.active)if(t){const A=v.run();(i||l||(f?A.some((q,Q)=>Bt(q,E[Q])):Bt(A,E)))&&(h&&h(),Ie(t,c,3,[A,E===an?void 0:f&&E[0]===an?[]:E,g]),E=A)}else v.run()};T.allowRecurse=!!t;let I;o==="sync"?I=T:o==="post"?I=()=>he(T,c&&c.suspense):(T.pre=!0,c&&(T.id=c.uid),I=()=>Si(T));const v=new ki(u,I);t?n?T():E=v.run():o==="post"?he(v.run.bind(v),c&&c.suspense):v.run();const L=()=>{v.stop(),c&&c.scope&&gi(c.scope.effects,v)};return w&&w.push(L),L}function dc(e,t,n){const i=this.proxy,o=se(e)?e.includes(".")?Es(i,e):()=>i[e]:e.bind(i,i);let s;U(t)?s=t:(s=t.handler,n=t);const r=le;Ot(this);const a=xs(o,s.bind(i),n);return r?Ot(r):lt(),a}function Es(e,t){const n=t.split(".");return()=>{let i=e;for(let o=0;o<n.length&&i;o++)i=i[n[o]];return i}}function yt(e,t){if(!G(e)||e.__v_skip||(t=t||new Set,t.has(e)))return e;if(t.add(e),ue(e))yt(e.value,t);else if(K(e))for(let n=0;n<e.length;n++)yt(e[n],t);else if(Zo(e)||bt(e))e.forEach(n=>{yt(n,t)});else if(Xo(e))for(const n in e)yt(e[n],t);return e}function tt(e,t,n,i){const o=e.dirs,s=t&&t.dirs;for(let r=0;r<o.length;r++){const a=o[r];s&&(a.oldValue=s[r].value);let c=a.dir[i];c&&(Tt(),Ie(c,n,8,[e.el,a,e,t]),Rt())}}function jt(e,t){return U(e)?(()=>oe({name:e.name},t,{setup:e}))():e}const fn=e=>!!e.type.__asyncLoader,Is=e=>e.type.__isKeepAlive;function hc(e,t){Os(e,"a",t)}function pc(e,t){Os(e,"da",t)}function Os(e,t,n=le){const i=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Cn(t,i,n),n){let o=n.parent;for(;o&&o.parent;)Is(o.parent.vnode)&&mc(i,t,n,o),o=o.parent}}function mc(e,t,n,i){const o=Cn(t,e,i,!0);Ps(()=>{gi(i[t],o)},n)}function Cn(e,t,n=le,i=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...r)=>{if(n.isUnmounted)return;Tt(),Ot(n);const a=Ie(t,n,e,r);return lt(),Rt(),a});return i?o.unshift(s):o.push(s),s}}const He=e=>(t,n=le)=>(!Gt||e==="sp")&&Cn(e,(...i)=>t(...i),n),gc=He("bm"),yc=He("m"),wc=He("bu"),_c=He("u"),bc=He("bum"),Ps=He("um"),vc=He("sp"),kc=He("rtg"),xc=He("rtc");function Ec(e,t=le){Cn("ec",e,t)}const Ic=Symbol.for("v-ndc"),ni=e=>e?Fs(e)?Li(e)||e.proxy:ni(e.parent):null,Ht=oe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ni(e.parent),$root:e=>ni(e.root),$emit:e=>e.emit,$options:e=>Ti(e),$forceUpdate:e=>e.f||(e.f=()=>Si(e.update)),$nextTick:e=>e.n||(e.n=ys.bind(e.proxy)),$watch:e=>dc.bind(e)}),Kn=(e,t)=>e!==Y&&!e.__isScriptSetup&&H(e,t),Oc={get({_:e},t){const{ctx:n,setupState:i,data:o,props:s,accessCache:r,type:a,appContext:c}=e;let u;if(t[0]!=="$"){const g=r[t];if(g!==void 0)switch(g){case 1:return i[t];case 2:return o[t];case 4:return n[t];case 3:return s[t]}else{if(Kn(i,t))return r[t]=1,i[t];if(o!==Y&&H(o,t))return r[t]=2,o[t];if((u=e.propsOptions[0])&&H(u,t))return r[t]=3,s[t];if(n!==Y&&H(n,t))return r[t]=4,n[t];ii&&(r[t]=0)}}const l=Ht[t];let f,h;if(l)return t==="$attrs"&&pe(e,"get",t),l(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==Y&&H(n,t))return r[t]=4,n[t];if(h=c.config.globalProperties,H(h,t))return h[t]},set({_:e},t,n){const{data:i,setupState:o,ctx:s}=e;return Kn(o,t)?(o[t]=n,!0):i!==Y&&H(i,t)?(i[t]=n,!0):H(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:i,appContext:o,propsOptions:s}},r){let a;return!!n[r]||e!==Y&&H(e,r)||Kn(t,r)||(a=s[0])&&H(a,r)||H(i,r)||H(Ht,r)||H(o.config.globalProperties,r)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:H(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function io(e){return K(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let ii=!0;function Pc(e){const t=Ti(e),n=e.proxy,i=e.ctx;ii=!1,t.beforeCreate&&oo(t.beforeCreate,e,"bc");const{data:o,computed:s,methods:r,watch:a,provide:c,inject:u,created:l,beforeMount:f,mounted:h,beforeUpdate:g,updated:w,activated:E,deactivated:T,beforeDestroy:I,beforeUnmount:v,destroyed:L,unmounted:A,render:q,renderTracked:Q,renderTriggered:ee,errorCaptured:ne,serverPrefetch:We,expose:Pe,inheritAttrs:$e,components:et,directives:Ce,filters:At}=t;if(u&&Cc(u,i,null),r)for(const X in r){const $=r[X];U($)&&(i[X]=$.bind(n))}if(o){const X=o.call(n,n);G(X)&&(e.data=In(X))}if(ii=!0,s)for(const X in s){const $=s[X],Fe=U($)?$.bind(n,n):U($.get)?$.get.bind(n,n):Ee,Ze=!U($)&&U($.set)?$.set.bind(n):Ee,Se=ke({get:Fe,set:Ze});Object.defineProperty(i,X,{enumerable:!0,configurable:!0,get:()=>Se.value,set:de=>Se.value=de})}if(a)for(const X in a)Cs(a[X],i,n,X);if(c){const X=U(c)?c.call(n):c;Reflect.ownKeys(X).forEach($=>{dn($,X[$])})}l&&oo(l,e,"c");function ie(X,$){K($)?$.forEach(Fe=>X(Fe.bind(n))):$&&X($.bind(n))}if(ie(gc,f),ie(yc,h),ie(wc,g),ie(_c,w),ie(hc,E),ie(pc,T),ie(Ec,ne),ie(xc,Q),ie(kc,ee),ie(bc,v),ie(Ps,A),ie(vc,We),K(Pe))if(Pe.length){const X=e.exposed||(e.exposed={});Pe.forEach($=>{Object.defineProperty(X,$,{get:()=>n[$],set:Fe=>n[$]=Fe})})}else e.exposed||(e.exposed={});q&&e.render===Ee&&(e.render=q),$e!=null&&(e.inheritAttrs=$e),et&&(e.components=et),Ce&&(e.directives=Ce)}function Cc(e,t,n=Ee){K(e)&&(e=oi(e));for(const i in e){const o=e[i];let s;G(o)?"default"in o?s=we(o.from||i,o.default,!0):s=we(o.from||i):s=we(o),ue(s)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s.value,set:r=>s.value=r}):t[i]=s}}function oo(e,t,n){Ie(K(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,n)}function Cs(e,t,n,i){const o=i.includes(".")?Es(n,i):()=>n[i];if(se(e)){const s=t[e];U(s)&&Dt(o,s)}else if(U(e))Dt(o,e.bind(n));else if(G(e))if(K(e))e.forEach(s=>Cs(s,t,n,i));else{const s=U(e.handler)?e.handler.bind(n):t[e.handler];U(s)&&Dt(o,s,e)}}function Ti(e){const t=e.type,{mixins:n,extends:i}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:r}}=e.appContext,a=s.get(t);let c;return a?c=a:!o.length&&!n&&!i?c=t:(c={},o.length&&o.forEach(u=>wn(c,u,r,!0)),wn(c,t,r)),G(t)&&s.set(t,c),c}function wn(e,t,n,i=!1){const{mixins:o,extends:s}=t;s&&wn(e,s,n,!0),o&&o.forEach(r=>wn(e,r,n,!0));for(const r in t)if(!(i&&r==="expose")){const a=Sc[r]||n&&n[r];e[r]=a?a(e[r],t[r]):t[r]}return e}const Sc={data:so,props:ro,emits:ro,methods:Kt,computed:Kt,beforeCreate:fe,created:fe,beforeMount:fe,mounted:fe,beforeUpdate:fe,updated:fe,beforeDestroy:fe,beforeUnmount:fe,destroyed:fe,unmounted:fe,activated:fe,deactivated:fe,errorCaptured:fe,serverPrefetch:fe,components:Kt,directives:Kt,watch:Rc,provide:so,inject:Tc};function so(e,t){return t?e?function(){return oe(U(e)?e.call(this,this):e,U(t)?t.call(this,this):t)}:t:e}function Tc(e,t){return Kt(oi(e),oi(t))}function oi(e){if(K(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function fe(e,t){return e?[...new Set([].concat(e,t))]:t}function Kt(e,t){return e?oe(Object.create(null),e,t):t}function ro(e,t){return e?K(e)&&K(t)?[...new Set([...e,...t])]:oe(Object.create(null),io(e),io(t??{})):t}function Rc(e,t){if(!e)return t;if(!t)return e;const n=oe(Object.create(null),e);for(const i in t)n[i]=fe(e[i],t[i]);return n}function Ss(){return{app:null,config:{isNativeTag:sr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let jc=0;function Ac(e,t){return function(i,o=null){U(i)||(i=oe({},i)),o!=null&&!G(o)&&(o=null);const s=Ss(),r=new Set;let a=!1;const c=s.app={_uid:jc++,_component:i,_props:o,_container:null,_context:s,_instance:null,version:sa,get config(){return s.config},set config(u){},use(u,...l){return r.has(u)||(u&&U(u.install)?(r.add(u),u.install(c,...l)):U(u)&&(r.add(u),u(c,...l))),c},mixin(u){return s.mixins.includes(u)||s.mixins.push(u),c},component(u,l){return l?(s.components[u]=l,c):s.components[u]},directive(u,l){return l?(s.directives[u]=l,c):s.directives[u]},mount(u,l,f){if(!a){const h=ye(i,o);return h.appContext=s,l&&t?t(h,u):e(h,u,f),a=!0,c._container=u,u.__vue_app__=c,Li(h.component)||h.component.proxy}},unmount(){a&&(e(null,c._container),delete c._container.__vue_app__)},provide(u,l){return s.provides[u]=l,c},runWithContext(u){_n=c;try{return u()}finally{_n=null}}};return c}}let _n=null;function dn(e,t){if(le){let n=le.provides;const i=le.parent&&le.parent.provides;i===n&&(n=le.provides=Object.create(i)),n[e]=t}}function we(e,t,n=!1){const i=le||Ne;if(i||_n){const o=i?i.parent==null?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:_n._context.provides;if(o&&e in o)return o[e];if(arguments.length>1)return n&&U(t)?t.call(i&&i.proxy):t}}function Mc(e,t,n,i=!1){const o={},s={};mn(s,Rn,1),e.propsDefaults=Object.create(null),Ts(e,t,o,s);for(const r in e.propsOptions[0])r in o||(o[r]=void 0);n?e.props=i?o:as(o):e.type.props?e.props=o:e.props=s,e.attrs=s}function Lc(e,t,n,i){const{props:o,attrs:s,vnode:{patchFlag:r}}=e,a=W(o),[c]=e.propsOptions;let u=!1;if((i||r>0)&&!(r&16)){if(r&8){const l=e.vnode.dynamicProps;for(let f=0;f<l.length;f++){let h=l[f];if(Pn(e.emitsOptions,h))continue;const g=t[h];if(c)if(H(s,h))g!==s[h]&&(s[h]=g,u=!0);else{const w=xt(h);o[w]=si(c,a,w,g,e,!1)}else g!==s[h]&&(s[h]=g,u=!0)}}}else{Ts(e,t,o,s)&&(u=!0);let l;for(const f in a)(!t||!H(t,f)&&((l=St(f))===f||!H(t,l)))&&(c?n&&(n[f]!==void 0||n[l]!==void 0)&&(o[f]=si(c,a,f,void 0,e,!0)):delete o[f]);if(s!==a)for(const f in s)(!t||!H(t,f))&&(delete s[f],u=!0)}u&&De(e,"set","$attrs")}function Ts(e,t,n,i){const[o,s]=e.propsOptions;let r=!1,a;if(t)for(let c in t){if(un(c))continue;const u=t[c];let l;o&&H(o,l=xt(c))?!s||!s.includes(l)?n[l]=u:(a||(a={}))[l]=u:Pn(e.emitsOptions,c)||(!(c in i)||u!==i[c])&&(i[c]=u,r=!0)}if(s){const c=W(n),u=a||Y;for(let l=0;l<s.length;l++){const f=s[l];n[f]=si(o,c,f,u[f],e,!H(u,f))}}return r}function si(e,t,n,i,o,s){const r=e[n];if(r!=null){const a=H(r,"default");if(a&&i===void 0){const c=r.default;if(r.type!==Function&&!r.skipFactory&&U(c)){const{propsDefaults:u}=o;n in u?i=u[n]:(Ot(o),i=u[n]=c.call(null,t),lt())}else i=c}r[0]&&(s&&!a?i=!1:r[1]&&(i===""||i===St(n))&&(i=!0))}return i}function Rs(e,t,n=!1){const i=t.propsCache,o=i.get(e);if(o)return o;const s=e.props,r={},a=[];let c=!1;if(!U(e)){const l=f=>{c=!0;const[h,g]=Rs(f,t,!0);oe(r,h),g&&a.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}if(!s&&!c)return G(e)&&i.set(e,_t),_t;if(K(s))for(let l=0;l<s.length;l++){const f=xt(s[l]);co(f)&&(r[f]=Y)}else if(s)for(const l in s){const f=xt(l);if(co(f)){const h=s[l],g=r[f]=K(h)||U(h)?{type:h}:oe({},h);if(g){const w=uo(Boolean,g.type),E=uo(String,g.type);g[0]=w>-1,g[1]=E<0||w<E,(w>-1||H(g,"default"))&&a.push(f)}}}const u=[r,a];return G(e)&&i.set(e,u),u}function co(e){return e[0]!=="$"}function ao(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:e===null?"null":""}function lo(e,t){return ao(e)===ao(t)}function uo(e,t){return K(t)?t.findIndex(n=>lo(n,e)):U(t)&&lo(t,e)?0:-1}const js=e=>e[0]==="_"||e==="$stable",Ri=e=>K(e)?e.map(Ae):[Ae(e)],Nc=(e,t,n)=>{if(t._n)return t;const i=sc((...o)=>Ri(t(...o)),n);return i._c=!1,i},As=(e,t,n)=>{const i=e._ctx;for(const o in e){if(js(o))continue;const s=e[o];if(U(s))t[o]=Nc(o,s,i);else if(s!=null){const r=Ri(s);t[o]=()=>r}}},Ms=(e,t)=>{const n=Ri(t);e.slots.default=()=>n},zc=(e,t)=>{if(e.vnode.shapeFlag&32){const n=t._;n?(e.slots=W(t),mn(t,"_",n)):As(t,e.slots={})}else e.slots={},t&&Ms(e,t);mn(e.slots,Rn,1)},Fc=(e,t,n)=>{const{vnode:i,slots:o}=e;let s=!0,r=Y;if(i.shapeFlag&32){const a=t._;a?n&&a===1?s=!1:(oe(o,t),!n&&a===1&&delete o._):(s=!t.$stable,As(t,o)),r=t}else t&&(Ms(e,t),r={default:1});if(s)for(const a in o)!js(a)&&!(a in r)&&delete o[a]};function ri(e,t,n,i,o=!1){if(K(e)){e.forEach((h,g)=>ri(h,t&&(K(t)?t[g]:t),n,i,o));return}if(fn(i)&&!o)return;const s=i.shapeFlag&4?Li(i.component)||i.component.proxy:i.el,r=o?null:s,{i:a,r:c}=e,u=t&&t.r,l=a.refs===Y?a.refs={}:a.refs,f=a.setupState;if(u!=null&&u!==c&&(se(u)?(l[u]=null,H(f,u)&&(f[u]=null)):ue(u)&&(u.value=null)),U(c))Ge(c,a,12,[r,l]);else{const h=se(c),g=ue(c);if(h||g){const w=()=>{if(e.f){const E=h?H(f,c)?f[c]:l[c]:c.value;o?K(E)&&gi(E,s):K(E)?E.includes(s)||E.push(s):h?(l[c]=[s],H(f,c)&&(f[c]=l[c])):(c.value=[s],e.k&&(l[e.k]=c.value))}else h?(l[c]=r,H(f,c)&&(f[c]=r)):g&&(c.value=r,e.k&&(l[e.k]=r))};r?(w.id=-1,he(w,n)):w()}}}const he=fc;function Kc(e){return Uc(e)}function Uc(e,t){const n=Yn();n.__VUE__=!0;const{insert:i,remove:o,patchProp:s,createElement:r,createText:a,createComment:c,setText:u,setElementText:l,parentNode:f,nextSibling:h,setScopeId:g=Ee,insertStaticContent:w}=e,E=(d,p,m,y=null,b=null,k=null,S=!1,O=null,P=!!p.dynamicChildren)=>{if(d===p)return;d&&!Lt(d,p)&&(y=_(d),de(d,b,k,!0),d=null),p.patchFlag===-2&&(P=!1,p.dynamicChildren=null);const{type:x,ref:N,shapeFlag:j}=p;switch(x){case Sn:T(d,p,m,y);break;case Yt:I(d,p,m,y);break;case Un:d==null&&v(p,m,y,S);break;case je:et(d,p,m,y,b,k,S,O,P);break;default:j&1?q(d,p,m,y,b,k,S,O,P):j&6?Ce(d,p,m,y,b,k,S,O,P):(j&64||j&128)&&x.process(d,p,m,y,b,k,S,O,P,C)}N!=null&&b&&ri(N,d&&d.ref,k,p||d,!p)},T=(d,p,m,y)=>{if(d==null)i(p.el=a(p.children),m,y);else{const b=p.el=d.el;p.children!==d.children&&u(b,p.children)}},I=(d,p,m,y)=>{d==null?i(p.el=c(p.children||""),m,y):p.el=d.el},v=(d,p,m,y)=>{[d.el,d.anchor]=w(d.children,p,m,y,d.el,d.anchor)},L=({el:d,anchor:p},m,y)=>{let b;for(;d&&d!==p;)b=h(d),i(d,m,y),d=b;i(p,m,y)},A=({el:d,anchor:p})=>{let m;for(;d&&d!==p;)m=h(d),o(d),d=m;o(p)},q=(d,p,m,y,b,k,S,O,P)=>{S=S||p.type==="svg",d==null?Q(p,m,y,b,k,S,O,P):We(d,p,b,k,S,O,P)},Q=(d,p,m,y,b,k,S,O)=>{let P,x;const{type:N,props:j,shapeFlag:z,transition:F,dirs:D}=d;if(P=d.el=r(d.type,k,j&&j.is,j),z&8?l(P,d.children):z&16&&ne(d.children,P,null,y,b,k&&N!=="foreignObject",S,O),D&&tt(d,null,y,"created"),ee(P,d,d.scopeId,S,y),j){for(const V in j)V!=="value"&&!un(V)&&s(P,V,null,j[V],k,d.children,y,b,re);"value"in j&&s(P,"value",null,j.value),(x=j.onVnodeBeforeMount)&&Re(x,y,d)}D&&tt(d,null,y,"beforeMount");const J=(!b||b&&!b.pendingBranch)&&F&&!F.persisted;J&&F.beforeEnter(P),i(P,p,m),((x=j&&j.onVnodeMounted)||J||D)&&he(()=>{x&&Re(x,y,d),J&&F.enter(P),D&&tt(d,null,y,"mounted")},b)},ee=(d,p,m,y,b)=>{if(m&&g(d,m),y)for(let k=0;k<y.length;k++)g(d,y[k]);if(b){let k=b.subTree;if(p===k){const S=b.vnode;ee(d,S,S.scopeId,S.slotScopeIds,b.parent)}}},ne=(d,p,m,y,b,k,S,O,P=0)=>{for(let x=P;x<d.length;x++){const N=d[x]=O?Xe(d[x]):Ae(d[x]);E(null,N,p,m,y,b,k,S,O)}},We=(d,p,m,y,b,k,S)=>{const O=p.el=d.el;let{patchFlag:P,dynamicChildren:x,dirs:N}=p;P|=d.patchFlag&16;const j=d.props||Y,z=p.props||Y;let F;m&&nt(m,!1),(F=z.onVnodeBeforeUpdate)&&Re(F,m,p,d),N&&tt(p,d,m,"beforeUpdate"),m&&nt(m,!0);const D=b&&p.type!=="foreignObject";if(x?Pe(d.dynamicChildren,x,O,m,y,D,k):S||$(d,p,O,null,m,y,D,k,!1),P>0){if(P&16)$e(O,p,j,z,m,y,b);else if(P&2&&j.class!==z.class&&s(O,"class",null,z.class,b),P&4&&s(O,"style",j.style,z.style,b),P&8){const J=p.dynamicProps;for(let V=0;V<J.length;V++){const te=J[V],_e=j[te],ht=z[te];(ht!==_e||te==="value")&&s(O,te,_e,ht,b,d.children,m,y,re)}}P&1&&d.children!==p.children&&l(O,p.children)}else!S&&x==null&&$e(O,p,j,z,m,y,b);((F=z.onVnodeUpdated)||N)&&he(()=>{F&&Re(F,m,p,d),N&&tt(p,d,m,"updated")},y)},Pe=(d,p,m,y,b,k,S)=>{for(let O=0;O<p.length;O++){const P=d[O],x=p[O],N=P.el&&(P.type===je||!Lt(P,x)||P.shapeFlag&70)?f(P.el):m;E(P,x,N,null,y,b,k,S,!0)}},$e=(d,p,m,y,b,k,S)=>{if(m!==y){if(m!==Y)for(const O in m)!un(O)&&!(O in y)&&s(d,O,m[O],null,S,p.children,b,k,re);for(const O in y){if(un(O))continue;const P=y[O],x=m[O];P!==x&&O!=="value"&&s(d,O,x,P,S,p.children,b,k,re)}"value"in y&&s(d,"value",m.value,y.value)}},et=(d,p,m,y,b,k,S,O,P)=>{const x=p.el=d?d.el:a(""),N=p.anchor=d?d.anchor:a("");let{patchFlag:j,dynamicChildren:z,slotScopeIds:F}=p;F&&(O=O?O.concat(F):F),d==null?(i(x,m,y),i(N,m,y),ne(p.children,m,N,b,k,S,O,P)):j>0&&j&64&&z&&d.dynamicChildren?(Pe(d.dynamicChildren,z,m,b,k,S,O),(p.key!=null||b&&p===b.subTree)&&Ls(d,p,!0)):$(d,p,m,N,b,k,S,O,P)},Ce=(d,p,m,y,b,k,S,O,P)=>{p.slotScopeIds=O,d==null?p.shapeFlag&512?b.ctx.activate(p,m,y,S,P):At(p,m,y,b,k,S,P):ut(d,p,P)},At=(d,p,m,y,b,k,S)=>{const O=d.component=qc(d,y,b);if(Is(d)&&(O.ctx.renderer=C),Gc(O),O.asyncDep){if(b&&b.registerDep(O,ie),!d.el){const P=O.subTree=ye(Yt);I(null,P,p,m)}return}ie(O,d,p,m,b,k,S)},ut=(d,p,m)=>{const y=p.component=d.component;if(ac(d,p,m))if(y.asyncDep&&!y.asyncResolved){X(y,p,m);return}else y.next=p,tc(y.update),y.update();else p.el=d.el,y.vnode=p},ie=(d,p,m,y,b,k,S)=>{const O=()=>{if(d.isMounted){let{next:N,bu:j,u:z,parent:F,vnode:D}=d,J=N,V;nt(d,!1),N?(N.el=D.el,X(d,N,S)):N=D,j&&zn(j),(V=N.props&&N.props.onVnodeBeforeUpdate)&&Re(V,F,N,D),nt(d,!0);const te=Fn(d),_e=d.subTree;d.subTree=te,E(_e,te,f(_e.el),_(_e),d,b,k),N.el=te.el,J===null&&lc(d,te.el),z&&he(z,b),(V=N.props&&N.props.onVnodeUpdated)&&he(()=>Re(V,F,N,D),b)}else{let N;const{el:j,props:z}=p,{bm:F,m:D,parent:J}=d,V=fn(p);if(nt(d,!1),F&&zn(F),!V&&(N=z&&z.onVnodeBeforeMount)&&Re(N,J,p),nt(d,!0),j&&Z){const te=()=>{d.subTree=Fn(d),Z(j,d.subTree,d,b,null)};V?p.type.__asyncLoader().then(()=>!d.isUnmounted&&te()):te()}else{const te=d.subTree=Fn(d);E(null,te,m,y,d,b,k),p.el=te.el}if(D&&he(D,b),!V&&(N=z&&z.onVnodeMounted)){const te=p;he(()=>Re(N,J,te),b)}(p.shapeFlag&256||J&&fn(J.vnode)&&J.vnode.shapeFlag&256)&&d.a&&he(d.a,b),d.isMounted=!0,p=m=y=null}},P=d.effect=new ki(O,()=>Si(x),d.scope),x=d.update=()=>P.run();x.id=d.uid,nt(d,!0),x()},X=(d,p,m)=>{p.component=d;const y=d.vnode.props;d.vnode=p,d.next=null,Lc(d,p.props,y,m),Fc(d,p.children,m),Tt(),to(),Rt()},$=(d,p,m,y,b,k,S,O,P=!1)=>{const x=d&&d.children,N=d?d.shapeFlag:0,j=p.children,{patchFlag:z,shapeFlag:F}=p;if(z>0){if(z&128){Ze(x,j,m,y,b,k,S,O,P);return}else if(z&256){Fe(x,j,m,y,b,k,S,O,P);return}}F&8?(N&16&&re(x,b,k),j!==x&&l(m,j)):N&16?F&16?Ze(x,j,m,y,b,k,S,O,P):re(x,b,k,!0):(N&8&&l(m,""),F&16&&ne(j,m,y,b,k,S,O,P))},Fe=(d,p,m,y,b,k,S,O,P)=>{d=d||_t,p=p||_t;const x=d.length,N=p.length,j=Math.min(x,N);let z;for(z=0;z<j;z++){const F=p[z]=P?Xe(p[z]):Ae(p[z]);E(d[z],F,m,null,b,k,S,O,P)}x>N?re(d,b,k,!0,!1,j):ne(p,m,y,b,k,S,O,P,j)},Ze=(d,p,m,y,b,k,S,O,P)=>{let x=0;const N=p.length;let j=d.length-1,z=N-1;for(;x<=j&&x<=z;){const F=d[x],D=p[x]=P?Xe(p[x]):Ae(p[x]);if(Lt(F,D))E(F,D,m,null,b,k,S,O,P);else break;x++}for(;x<=j&&x<=z;){const F=d[j],D=p[z]=P?Xe(p[z]):Ae(p[z]);if(Lt(F,D))E(F,D,m,null,b,k,S,O,P);else break;j--,z--}if(x>j){if(x<=z){const F=z+1,D=F<N?p[F].el:y;for(;x<=z;)E(null,p[x]=P?Xe(p[x]):Ae(p[x]),m,D,b,k,S,O,P),x++}}else if(x>z)for(;x<=j;)de(d[x],b,k,!0),x++;else{const F=x,D=x,J=new Map;for(x=D;x<=z;x++){const me=p[x]=P?Xe(p[x]):Ae(p[x]);me.key!=null&&J.set(me.key,x)}let V,te=0;const _e=z-D+1;let ht=!1,$i=0;const Mt=new Array(_e);for(x=0;x<_e;x++)Mt[x]=0;for(x=F;x<=j;x++){const me=d[x];if(te>=_e){de(me,b,k,!0);continue}let Te;if(me.key!=null)Te=J.get(me.key);else for(V=D;V<=z;V++)if(Mt[V-D]===0&&Lt(me,p[V])){Te=V;break}Te===void 0?de(me,b,k,!0):(Mt[Te-D]=x+1,Te>=$i?$i=Te:ht=!0,E(me,p[Te],m,null,b,k,S,O,P),te++)}const Zi=ht?Dc(Mt):_t;for(V=Zi.length-1,x=_e-1;x>=0;x--){const me=D+x,Te=p[me],Bi=me+1<N?p[me+1].el:y;Mt[x]===0?E(null,Te,m,Bi,b,k,S,O,P):ht&&(V<0||x!==Zi[V]?Se(Te,m,Bi,2):V--)}}},Se=(d,p,m,y,b=null)=>{const{el:k,type:S,transition:O,children:P,shapeFlag:x}=d;if(x&6){Se(d.component.subTree,p,m,y);return}if(x&128){d.suspense.move(p,m,y);return}if(x&64){S.move(d,p,m,C);return}if(S===je){i(k,p,m);for(let j=0;j<P.length;j++)Se(P[j],p,m,y);i(d.anchor,p,m);return}if(S===Un){L(d,p,m);return}if(y!==2&&x&1&&O)if(y===0)O.beforeEnter(k),i(k,p,m),he(()=>O.enter(k),b);else{const{leave:j,delayLeave:z,afterLeave:F}=O,D=()=>i(k,p,m),J=()=>{j(k,()=>{D(),F&&F()})};z?z(k,D,J):J()}else i(k,p,m)},de=(d,p,m,y=!1,b=!1)=>{const{type:k,props:S,ref:O,children:P,dynamicChildren:x,shapeFlag:N,patchFlag:j,dirs:z}=d;if(O!=null&&ri(O,null,m,d,!0),N&256){p.ctx.deactivate(d);return}const F=N&1&&z,D=!fn(d);let J;if(D&&(J=S&&S.onVnodeBeforeUnmount)&&Re(J,p,d),N&6)tn(d.component,m,y);else{if(N&128){d.suspense.unmount(m,y);return}F&&tt(d,null,p,"beforeUnmount"),N&64?d.type.remove(d,p,m,b,C,y):x&&(k!==je||j>0&&j&64)?re(x,p,m,!1,!0):(k===je&&j&384||!b&&N&16)&&re(P,p,m),y&&ft(d)}(D&&(J=S&&S.onVnodeUnmounted)||F)&&he(()=>{J&&Re(J,p,d),F&&tt(d,null,p,"unmounted")},m)},ft=d=>{const{type:p,el:m,anchor:y,transition:b}=d;if(p===je){dt(m,y);return}if(p===Un){A(d);return}const k=()=>{o(m),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(d.shapeFlag&1&&b&&!b.persisted){const{leave:S,delayLeave:O}=b,P=()=>S(m,k);O?O(d.el,k,P):P()}else k()},dt=(d,p)=>{let m;for(;d!==p;)m=h(d),o(d),d=m;o(p)},tn=(d,p,m)=>{const{bum:y,scope:b,update:k,subTree:S,um:O}=d;y&&zn(y),b.stop(),k&&(k.active=!1,de(S,d,p,m)),O&&he(O,p),he(()=>{d.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},re=(d,p,m,y=!1,b=!1,k=0)=>{for(let S=k;S<d.length;S++)de(d[S],p,m,y,b)},_=d=>d.shapeFlag&6?_(d.component.subTree):d.shapeFlag&128?d.suspense.next():h(d.anchor||d.el),R=(d,p,m)=>{d==null?p._vnode&&de(p._vnode,null,null,!0):E(p._vnode||null,d,p,null,null,null,m),to(),_s(),p._vnode=d},C={p:E,um:de,m:Se,r:ft,mt:At,mc:ne,pc:$,pbc:Pe,n:_,o:e};let M,Z;return t&&([M,Z]=t(C)),{render:R,hydrate:M,createApp:Ac(R,M)}}function nt({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Ls(e,t,n=!1){const i=e.children,o=t.children;if(K(i)&&K(o))for(let s=0;s<i.length;s++){const r=i[s];let a=o[s];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[s]=Xe(o[s]),a.el=r.el),n||Ls(r,a)),a.type===Sn&&(a.el=r.el)}}function Dc(e){const t=e.slice(),n=[0];let i,o,s,r,a;const c=e.length;for(i=0;i<c;i++){const u=e[i];if(u!==0){if(o=n[n.length-1],e[o]<u){t[i]=o,n.push(i);continue}for(s=0,r=n.length-1;s<r;)a=s+r>>1,e[n[a]]<u?s=a+1:r=a;u<e[n[s]]&&(s>0&&(t[i]=n[s-1]),n[s]=i)}}for(s=n.length,r=n[s-1];s-- >0;)n[s]=r,r=t[r];return n}const Hc=e=>e.__isTeleport,je=Symbol.for("v-fgt"),Sn=Symbol.for("v-txt"),Yt=Symbol.for("v-cmt"),Un=Symbol.for("v-stc"),Wt=[];let xe=null;function Tn(e=!1){Wt.push(xe=e?null:[])}function Wc(){Wt.pop(),xe=Wt[Wt.length-1]||null}let qt=1;function fo(e){qt+=e}function Ns(e){return e.dynamicChildren=qt>0?xe||_t:null,Wc(),qt>0&&xe&&xe.push(e),e}function ji(e,t,n,i,o,s){return Ns(jn(e,t,n,i,o,s,!0))}function $c(e,t,n,i,o){return Ns(ye(e,t,n,i,o,!0))}function ci(e){return e?e.__v_isVNode===!0:!1}function Lt(e,t){return e.type===t.type&&e.key===t.key}const Rn="__vInternal",zs=({key:e})=>e??null,hn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?se(e)||ue(e)||U(e)?{i:Ne,r:e,k:t,f:!!n}:e:null);function jn(e,t=null,n=null,i=0,o=null,s=e===je?0:1,r=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&zs(t),ref:t&&hn(t),scopeId:ks,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:i,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Ne};return a?(Ai(c,n),s&128&&e.normalize(c)):n&&(c.shapeFlag|=se(n)?8:16),qt>0&&!r&&xe&&(c.patchFlag>0||s&6)&&c.patchFlag!==32&&xe.push(c),c}const ye=Zc;function Zc(e,t=null,n=null,i=0,o=null,s=!1){if((!e||e===Ic)&&(e=Yt),ci(e)){const a=It(e,t,!0);return n&&Ai(a,n),qt>0&&!s&&xe&&(a.shapeFlag&6?xe[xe.indexOf(e)]=a:xe.push(a)),a.patchFlag|=-2,a}if(na(e)&&(e=e.__vccOpts),t){t=Bc(t);let{class:a,style:c}=t;a&&!se(a)&&(t.class=bi(a)),G(c)&&(us(c)&&!K(c)&&(c=oe({},c)),t.style=_i(c))}const r=se(e)?1:uc(e)?128:Hc(e)?64:G(e)?4:U(e)?2:0;return jn(e,t,n,i,o,r,s,!0)}function Bc(e){return e?us(e)||Rn in e?oe({},e):e:null}function It(e,t,n=!1){const{props:i,ref:o,patchFlag:s,children:r}=e,a=t?Xc(i||{},t):i;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&zs(a),ref:t&&t.ref?n&&o?K(o)?o.concat(hn(t)):[o,hn(t)]:hn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:r,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==je?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&It(e.ssContent),ssFallback:e.ssFallback&&It(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Vc(e=" ",t=0){return ye(Sn,null,e,t)}function Ae(e){return e==null||typeof e=="boolean"?ye(Yt):K(e)?ye(je,null,e.slice()):typeof e=="object"?Xe(e):ye(Sn,null,String(e))}function Xe(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:It(e)}function Ai(e,t){let n=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(K(t))n=16;else if(typeof t=="object")if(i&65){const o=t.default;o&&(o._c&&(o._d=!1),Ai(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!(Rn in t)?t._ctx=Ne:o===3&&Ne&&(Ne.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else U(t)?(t={default:t,_ctx:Ne},n=32):(t=String(t),i&64?(n=16,t=[Vc(t)]):n=8);e.children=t,e.shapeFlag|=n}function Xc(...e){const t={};for(let n=0;n<e.length;n++){const i=e[n];for(const o in i)if(o==="class")t.class!==i.class&&(t.class=bi([t.class,i.class]));else if(o==="style")t.style=_i([t.style,i.style]);else if(vn(o)){const s=t[o],r=i[o];r&&s!==r&&!(K(s)&&s.includes(r))&&(t[o]=s?[].concat(s,r):r)}else o!==""&&(t[o]=i[o])}return t}function Re(e,t,n,i=null){Ie(e,t,7,[n,i])}const Jc=Ss();let Yc=0;function qc(e,t,n){const i=e.type,o=(t?t.appContext:e.appContext)||Jc,s={uid:Yc++,vnode:e,type:i,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,scope:new _r(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Rs(i,o),emitsOptions:vs(i,o),emit:null,emitted:null,propsDefaults:Y,inheritAttrs:i.inheritAttrs,ctx:Y,data:Y,props:Y,attrs:Y,slots:Y,refs:Y,setupState:Y,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=oc.bind(null,s),e.ce&&e.ce(s),s}let le=null,Mi,pt,ho="__VUE_INSTANCE_SETTERS__";(pt=Yn()[ho])||(pt=Yn()[ho]=[]),pt.push(e=>le=e),Mi=e=>{pt.length>1?pt.forEach(t=>t(e)):pt[0](e)};const Ot=e=>{Mi(e),e.scope.on()},lt=()=>{le&&le.scope.off(),Mi(null)};function Fs(e){return e.vnode.shapeFlag&4}let Gt=!1;function Gc(e,t=!1){Gt=t;const{props:n,children:i}=e.vnode,o=Fs(e);Mc(e,n,o,t),zc(e,i);const s=o?Qc(e,t):void 0;return Gt=!1,s}function Qc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=fs(new Proxy(e.ctx,Oc));const{setup:i}=n;if(i){const o=e.setupContext=i.length>1?ta(e):null;Ot(e),Tt();const s=Ge(i,e,0,[e.props,o]);if(Rt(),lt(),Bo(s)){if(s.then(lt,lt),t)return s.then(r=>{po(e,r,t)}).catch(r=>{On(r,e,0)});e.asyncDep=s}else po(e,s,t)}else Ks(e,t)}function po(e,t,n){U(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:G(t)&&(e.setupState=ms(t)),Ks(e,n)}let mo;function Ks(e,t,n){const i=e.type;if(!e.render){if(!t&&mo&&!i.render){const o=i.template||Ti(e).template;if(o){const{isCustomElement:s,compilerOptions:r}=e.appContext.config,{delimiters:a,compilerOptions:c}=i,u=oe(oe({isCustomElement:s,delimiters:a},r),c);i.render=mo(o,u)}}e.render=i.render||Ee}Ot(e),Tt(),Pc(e),Rt(),lt()}function ea(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,n){return pe(e,"get","$attrs"),t[n]}}))}function ta(e){const t=n=>{e.exposed=n||{}};return{get attrs(){return ea(e)},slots:e.slots,emit:e.emit,expose:t}}function Li(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(ms(fs(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ht)return Ht[n](e)},has(t,n){return n in t||n in Ht}}))}function na(e){return U(e)&&"__vccOpts"in e}const ke=(e,t)=>Gr(e,t,Gt);function Us(e,t,n){const i=arguments.length;return i===2?G(t)&&!K(t)?ci(t)?ye(e,null,[t]):ye(e,t):ye(e,null,t):(i>3?n=Array.prototype.slice.call(arguments,2):i===3&&ci(n)&&(n=[n]),ye(e,t,n))}const ia=Symbol.for("v-scx"),oa=()=>we(ia),sa="3.3.4",ra="http://www.w3.org/2000/svg",rt=typeof document<"u"?document:null,go=rt&&rt.createElement("template"),ca={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,i)=>{const o=t?rt.createElementNS(ra,e):rt.createElement(e,n?{is:n}:void 0);return e==="select"&&i&&i.multiple!=null&&o.setAttribute("multiple",i.multiple),o},createText:e=>rt.createTextNode(e),createComment:e=>rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,i,o,s){const r=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===s||!(o=o.nextSibling)););else{go.innerHTML=i?`<svg>${e}</svg>`:e;const a=go.content;if(i){const c=a.firstChild;for(;c.firstChild;)a.appendChild(c.firstChild);a.removeChild(c)}t.insertBefore(a,n)}return[r?r.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function aa(e,t,n){const i=e._vtc;i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function la(e,t,n){const i=e.style,o=se(n);if(n&&!o){if(t&&!se(t))for(const s in t)n[s]==null&&ai(i,s,"");for(const s in n)ai(i,s,n[s])}else{const s=i.display;o?t!==n&&(i.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(i.display=s)}}const yo=/\s*!important$/;function ai(e,t,n){if(K(n))n.forEach(i=>ai(e,t,i));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const i=ua(e,t);yo.test(n)?e.setProperty(St(i),n.replace(yo,""),"important"):e[i]=n}}const wo=["Webkit","Moz","ms"],Dn={};function ua(e,t){const n=Dn[t];if(n)return n;let i=xt(t);if(i!=="filter"&&i in e)return Dn[t]=i;i=Jo(i);for(let o=0;o<wo.length;o++){const s=wo[o]+i;if(s in e)return Dn[t]=s}return t}const _o="http://www.w3.org/1999/xlink";function fa(e,t,n,i,o){if(i&&t.startsWith("xlink:"))n==null?e.removeAttributeNS(_o,t.slice(6,t.length)):e.setAttributeNS(_o,t,n);else{const s=yr(t);n==null||s&&!Yo(n)?e.removeAttribute(t):e.setAttribute(t,s?"":n)}}function da(e,t,n,i,o,s,r){if(t==="innerHTML"||t==="textContent"){i&&r(i,o,s),e[t]=n??"";return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){e._value=n;const u=a==="OPTION"?e.getAttribute("value"):e.value,l=n??"";u!==l&&(e.value=l),n==null&&e.removeAttribute(t);return}let c=!1;if(n===""||n==null){const u=typeof e[t];u==="boolean"?n=Yo(n):n==null&&u==="string"?(n="",c=!0):u==="number"&&(n=0,c=!0)}try{e[t]=n}catch{}c&&e.removeAttribute(t)}function ha(e,t,n,i){e.addEventListener(t,n,i)}function pa(e,t,n,i){e.removeEventListener(t,n,i)}function ma(e,t,n,i,o=null){const s=e._vei||(e._vei={}),r=s[t];if(i&&r)r.value=i;else{const[a,c]=ga(t);if(i){const u=s[t]=_a(i,o);ha(e,a,u,c)}else r&&(pa(e,a,r,c),s[t]=void 0)}}const bo=/(?:Once|Passive|Capture)$/;function ga(e){let t;if(bo.test(e)){t={};let i;for(;i=e.match(bo);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):St(e.slice(2)),t]}let Hn=0;const ya=Promise.resolve(),wa=()=>Hn||(ya.then(()=>Hn=0),Hn=Date.now());function _a(e,t){const n=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=n.attached)return;Ie(ba(i,n.value),t,5,[i])};return n.value=e,n.attached=wa(),n}function ba(e,t){if(K(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(i=>o=>!o._stopped&&i&&i(o))}else return t}const vo=/^on[a-z]/,va=(e,t,n,i,o=!1,s,r,a,c)=>{t==="class"?aa(e,i,o):t==="style"?la(e,n,i):vn(t)?mi(t)||ma(e,t,n,i,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ka(e,t,i,o))?da(e,t,i,s,r,a,c):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),fa(e,t,i,o))};function ka(e,t,n,i){return i?!!(t==="innerHTML"||t==="textContent"||t in e&&vo.test(t)&&U(n)):t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA"||vo.test(t)&&se(n)?!1:t in e}const xa=oe({patchProp:va},ca);let ko;function Ea(){return ko||(ko=Kc(xa))}const Ia=(...e)=>{const t=Ea().createApp(...e),{mount:n}=t;return t.mount=i=>{const o=Oa(i);if(!o)return;const s=t._component;!U(s)&&!s.render&&!s.template&&(s.template=o.innerHTML),o.innerHTML="";const r=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),r},t};function Oa(e){return se(e)?document.querySelector(e):e}const Ds=Symbol("$auth0");function Me(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function"){var o=0;for(i=Object.getOwnPropertySymbols(e);o<i.length;o++)t.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(e,i[o])&&(n[i[o]]=e[i[o]])}return n}var wt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ni(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function zi(e,t){return e(t={exports:{}},t.exports),t.exports}var ot=zi(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function i(){var o=this;this.locked=new Map,this.addToLocked=function(s,r){var a=o.locked.get(s);a===void 0?r===void 0?o.locked.set(s,[]):o.locked.set(s,[r]):r!==void 0&&(a.unshift(r),o.locked.set(s,a))},this.isLocked=function(s){return o.locked.has(s)},this.lock=function(s){return new Promise(function(r,a){o.isLocked(s)?o.addToLocked(s,r):(o.addToLocked(s),r())})},this.unlock=function(s){var r=o.locked.get(s);if(r!==void 0&&r.length!==0){var a=r.pop();o.locked.set(s,r),a!==void 0&&setTimeout(a,0)}else o.locked.delete(s)}}return i.getInstance=function(){return i.instance===void 0&&(i.instance=new i),i.instance},i}();t.default=function(){return n.getInstance()}});Ni(ot);var Pa=Ni(zi(function(e,t){var n=wt&&wt.__awaiter||function(c,u,l,f){return new(l||(l=Promise))(function(h,g){function w(I){try{T(f.next(I))}catch(v){g(v)}}function E(I){try{T(f.throw(I))}catch(v){g(v)}}function T(I){I.done?h(I.value):new l(function(v){v(I.value)}).then(w,E)}T((f=f.apply(c,u||[])).next())})},i=wt&&wt.__generator||function(c,u){var l,f,h,g,w={label:0,sent:function(){if(1&h[0])throw h[1];return h[1]},trys:[],ops:[]};return g={next:E(0),throw:E(1),return:E(2)},typeof Symbol=="function"&&(g[Symbol.iterator]=function(){return this}),g;function E(T){return function(I){return function(v){if(l)throw new TypeError("Generator is already executing.");for(;w;)try{if(l=1,f&&(h=2&v[0]?f.return:v[0]?f.throw||((h=f.return)&&h.call(f),0):f.next)&&!(h=h.call(f,v[1])).done)return h;switch(f=0,h&&(v=[2&v[0],h.value]),v[0]){case 0:case 1:h=v;break;case 4:return w.label++,{value:v[1],done:!1};case 5:w.label++,f=v[1],v=[0];continue;case 7:v=w.ops.pop(),w.trys.pop();continue;default:if(!((h=(h=w.trys).length>0&&h[h.length-1])||v[0]!==6&&v[0]!==2)){w=0;continue}if(v[0]===3&&(!h||v[1]>h[0]&&v[1]<h[3])){w.label=v[1];break}if(v[0]===6&&w.label<h[1]){w.label=h[1],h=v;break}if(h&&w.label<h[2]){w.label=h[2],w.ops.push(v);break}h[2]&&w.ops.pop(),w.trys.pop();continue}v=u.call(c,w)}catch(L){v=[6,L],f=0}finally{l=h=0}if(5&v[0])throw v[1];return{value:v[0]?v[1]:void 0,done:!0}}([T,I])}}};Object.defineProperty(t,"__esModule",{value:!0});var o="browser-tabs-lock-key";function s(c){return new Promise(function(u){return setTimeout(u,c)})}function r(c){for(var u="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz",l="",f=0;f<c;f++)l+=u[Math.floor(Math.random()*u.length)];return l}var a=function(){function c(){this.acquiredIatSet=new Set,this.id=Date.now().toString()+r(15),this.acquireLock=this.acquireLock.bind(this),this.releaseLock=this.releaseLock.bind(this),this.releaseLock__private__=this.releaseLock__private__.bind(this),this.waitForSomethingToChange=this.waitForSomethingToChange.bind(this),this.refreshLockWhileAcquired=this.refreshLockWhileAcquired.bind(this),c.waiters===void 0&&(c.waiters=[])}return c.prototype.acquireLock=function(u,l){return l===void 0&&(l=5e3),n(this,void 0,void 0,function(){var f,h,g,w,E,T;return i(this,function(I){switch(I.label){case 0:f=Date.now()+r(4),h=Date.now()+l,g=o+"-"+u,w=window.localStorage,I.label=1;case 1:return Date.now()<h?[4,s(30)]:[3,8];case 2:return I.sent(),w.getItem(g)!==null?[3,5]:(E=this.id+"-"+u+"-"+f,[4,s(Math.floor(25*Math.random()))]);case 3:return I.sent(),w.setItem(g,JSON.stringify({id:this.id,iat:f,timeoutKey:E,timeAcquired:Date.now(),timeRefreshed:Date.now()})),[4,s(30)];case 4:return I.sent(),(T=w.getItem(g))!==null&&(T=JSON.parse(T)).id===this.id&&T.iat===f?(this.acquiredIatSet.add(f),this.refreshLockWhileAcquired(g,f),[2,!0]):[3,7];case 5:return c.lockCorrector(),[4,this.waitForSomethingToChange(h)];case 6:I.sent(),I.label=7;case 7:return f=Date.now()+r(4),[3,1];case 8:return[2,!1]}})})},c.prototype.refreshLockWhileAcquired=function(u,l){return n(this,void 0,void 0,function(){var f=this;return i(this,function(h){return setTimeout(function(){return n(f,void 0,void 0,function(){var g,w;return i(this,function(E){switch(E.label){case 0:return[4,ot.default().lock(l)];case 1:return E.sent(),this.acquiredIatSet.has(l)?(g=window.localStorage,(w=g.getItem(u))===null?(ot.default().unlock(l),[2]):((w=JSON.parse(w)).timeRefreshed=Date.now(),g.setItem(u,JSON.stringify(w)),ot.default().unlock(l),this.refreshLockWhileAcquired(u,l),[2])):(ot.default().unlock(l),[2])}})})},1e3),[2]})})},c.prototype.waitForSomethingToChange=function(u){return n(this,void 0,void 0,function(){return i(this,function(l){switch(l.label){case 0:return[4,new Promise(function(f){var h=!1,g=Date.now(),w=!1;function E(){if(w||(window.removeEventListener("storage",E),c.removeFromWaiting(E),clearTimeout(T),w=!0),!h){h=!0;var I=50-(Date.now()-g);I>0?setTimeout(f,I):f()}}window.addEventListener("storage",E),c.addToWaiting(E);var T=setTimeout(E,Math.max(0,u-Date.now()))})];case 1:return l.sent(),[2]}})})},c.addToWaiting=function(u){this.removeFromWaiting(u),c.waiters!==void 0&&c.waiters.push(u)},c.removeFromWaiting=function(u){c.waiters!==void 0&&(c.waiters=c.waiters.filter(function(l){return l!==u}))},c.notifyWaiters=function(){c.waiters!==void 0&&c.waiters.slice().forEach(function(u){return u()})},c.prototype.releaseLock=function(u){return n(this,void 0,void 0,function(){return i(this,function(l){switch(l.label){case 0:return[4,this.releaseLock__private__(u)];case 1:return[2,l.sent()]}})})},c.prototype.releaseLock__private__=function(u){return n(this,void 0,void 0,function(){var l,f,h;return i(this,function(g){switch(g.label){case 0:return l=window.localStorage,f=o+"-"+u,(h=l.getItem(f))===null?[2]:(h=JSON.parse(h)).id!==this.id?[3,2]:[4,ot.default().lock(h.iat)];case 1:g.sent(),this.acquiredIatSet.delete(h.iat),l.removeItem(f),ot.default().unlock(h.iat),c.notifyWaiters(),g.label=2;case 2:return[2]}})})},c.lockCorrector=function(){for(var u=Date.now()-5e3,l=window.localStorage,f=Object.keys(l),h=!1,g=0;g<f.length;g++){var w=f[g];if(w.includes(o)){var E=l.getItem(w);E!==null&&((E=JSON.parse(E)).timeRefreshed===void 0&&E.timeAcquired<u||E.timeRefreshed!==void 0&&E.timeRefreshed<u)&&(l.removeItem(w),h=!0)}}h&&c.notifyWaiters()},c.waiters=void 0,c}();t.default=a}));const Ca={timeoutInSeconds:60},Hs={name:"auth0-spa-js",version:"2.1.2"},Ws=()=>Date.now();class ce extends Error{constructor(t,n){super(n),this.error=t,this.error_description=n,Object.setPrototypeOf(this,ce.prototype)}static fromPayload({error:t,error_description:n}){return new ce(t,n)}}class Fi extends ce{constructor(t,n,i,o=null){super(t,n),this.state=i,this.appState=o,Object.setPrototypeOf(this,Fi.prototype)}}class Qt extends ce{constructor(){super("timeout","Timeout"),Object.setPrototypeOf(this,Qt.prototype)}}class Ki extends Qt{constructor(t){super(),this.popup=t,Object.setPrototypeOf(this,Ki.prototype)}}class Ui extends ce{constructor(t){super("cancelled","Popup closed"),this.popup=t,Object.setPrototypeOf(this,Ui.prototype)}}class Di extends ce{constructor(t,n,i){super(t,n),this.mfa_token=i,Object.setPrototypeOf(this,Di.prototype)}}class An extends ce{constructor(t,n){super("missing_refresh_token",`Missing Refresh Token (audience: '${xo(t,["default"])}', scope: '${xo(n)}')`),this.audience=t,this.scope=n,Object.setPrototypeOf(this,An.prototype)}}function xo(e,t=[]){return e&&!t.includes(e)?e:""}const pn=()=>window.crypto,Wn=()=>{const e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.";let t="";return Array.from(pn().getRandomValues(new Uint8Array(43))).forEach(n=>t+=e[n%e.length]),t},Eo=e=>btoa(e),li=e=>{var{clientId:t}=e,n=Me(e,["clientId"]);return new URLSearchParams((i=>Object.keys(i).filter(o=>i[o]!==void 0).reduce((o,s)=>Object.assign(Object.assign({},o),{[s]:i[s]}),{}))(Object.assign({client_id:t},n))).toString()},Io=e=>(t=>decodeURIComponent(atob(t).split("").map(n=>"%"+("00"+n.charCodeAt(0).toString(16)).slice(-2)).join("")))(e.replace(/_/g,"/").replace(/-/g,"+")),Sa=async(e,t)=>{const n=await fetch(e,t);return{ok:n.ok,json:await n.json()}},Ta=async(e,t,n,i,o,s,r=1e4)=>o?(async(a,c,u,l,f,h,g)=>{return w={auth:{audience:c,scope:u},timeout:f,fetchUrl:a,fetchOptions:l,useFormData:g},E=h,new Promise(function(T,I){const v=new MessageChannel;v.port1.onmessage=function(L){L.data.error?I(new Error(L.data.error)):T(L.data),v.port1.close()},E.postMessage(w,[v.port2])});var w,E})(e,t,n,i,r,o,s):(async(a,c,u)=>{const l=new AbortController;let f;return c.signal=l.signal,Promise.race([Sa(a,c),new Promise((h,g)=>{f=setTimeout(()=>{l.abort(),g(new Error("Timeout when executing 'fetch'"))},u)})]).finally(()=>{clearTimeout(f)})})(e,i,r);async function Ra(e,t){var{baseUrl:n,timeout:i,audience:o,scope:s,auth0Client:r,useFormData:a}=e,c=Me(e,["baseUrl","timeout","audience","scope","auth0Client","useFormData"]);const u=a?li(c):JSON.stringify(c);return await async function(l,f,h,g,w,E,T){let I,v=null;for(let ne=0;ne<3;ne++)try{I=await Ta(l,h,g,w,E,T,f),v=null;break}catch(We){v=We}if(v)throw v;const L=I.json,{error:A,error_description:q}=L,Q=Me(L,["error","error_description"]),{ok:ee}=I;if(!ee){const ne=q||`HTTP error. Unable to fetch ${l}`;throw A==="mfa_required"?new Di(A,ne,Q.mfa_token):A==="missing_refresh_token"?new An(h,g):new ce(A||"request_error",ne)}return Q}(`${n}/oauth/token`,i,o||"default",s,{method:"POST",body:u,headers:{"Content-Type":a?"application/x-www-form-urlencoded":"application/json","Auth0-Client":btoa(JSON.stringify(r||Hs))}},t,a)}const ln=(...e)=>{return(t=e.filter(Boolean).join(" ").trim().split(/\s+/),Array.from(new Set(t))).join(" ");var t};class ze{constructor(t,n="@@auth0spajs@@",i){this.prefix=n,this.suffix=i,this.clientId=t.clientId,this.scope=t.scope,this.audience=t.audience}toKey(){return[this.prefix,this.clientId,this.audience,this.scope,this.suffix].filter(Boolean).join("::")}static fromKey(t){const[n,i,o,s]=t.split("::");return new ze({clientId:i,scope:s,audience:o},n)}static fromCacheEntry(t){const{scope:n,audience:i,client_id:o}=t;return new ze({scope:n,audience:i,clientId:o})}}class ja{set(t,n){localStorage.setItem(t,JSON.stringify(n))}get(t){const n=window.localStorage.getItem(t);if(n)try{return JSON.parse(n)}catch{return}}remove(t){localStorage.removeItem(t)}allKeys(){return Object.keys(window.localStorage).filter(t=>t.startsWith("@@auth0spajs@@"))}}class $s{constructor(){this.enclosedCache=function(){let t={};return{set(n,i){t[n]=i},get(n){const i=t[n];if(i)return i},remove(n){delete t[n]},allKeys:()=>Object.keys(t)}}()}}class Aa{constructor(t,n,i){this.cache=t,this.keyManifest=n,this.nowProvider=i||Ws}async setIdToken(t,n,i){var o;const s=this.getIdTokenCacheKey(t);await this.cache.set(s,{id_token:n,decodedToken:i}),await((o=this.keyManifest)===null||o===void 0?void 0:o.add(s))}async getIdToken(t){const n=await this.cache.get(this.getIdTokenCacheKey(t.clientId));if(!n&&t.scope&&t.audience){const i=await this.get(t);return!i||!i.id_token||!i.decodedToken?void 0:{id_token:i.id_token,decodedToken:i.decodedToken}}if(n)return{id_token:n.id_token,decodedToken:n.decodedToken}}async get(t,n=0){var i;let o=await this.cache.get(t.toKey());if(!o){const a=await this.getCacheKeys();if(!a)return;const c=this.matchExistingCacheKey(t,a);c&&(o=await this.cache.get(c))}if(!o)return;const s=await this.nowProvider(),r=Math.floor(s/1e3);return o.expiresAt-n<r?o.body.refresh_token?(o.body={refresh_token:o.body.refresh_token},await this.cache.set(t.toKey(),o),o.body):(await this.cache.remove(t.toKey()),void await((i=this.keyManifest)===null||i===void 0?void 0:i.remove(t.toKey()))):o.body}async set(t){var n;const i=new ze({clientId:t.client_id,scope:t.scope,audience:t.audience}),o=await this.wrapCacheEntry(t);await this.cache.set(i.toKey(),o),await((n=this.keyManifest)===null||n===void 0?void 0:n.add(i.toKey()))}async clear(t){var n;const i=await this.getCacheKeys();i&&(await i.filter(o=>!t||o.includes(t)).reduce(async(o,s)=>{await o,await this.cache.remove(s)},Promise.resolve()),await((n=this.keyManifest)===null||n===void 0?void 0:n.clear()))}async wrapCacheEntry(t){const n=await this.nowProvider();return{body:t,expiresAt:Math.floor(n/1e3)+t.expires_in}}async getCacheKeys(){var t;return this.keyManifest?(t=await this.keyManifest.get())===null||t===void 0?void 0:t.keys:this.cache.allKeys?this.cache.allKeys():void 0}getIdTokenCacheKey(t){return new ze({clientId:t},"@@auth0spajs@@","@@user@@").toKey()}matchExistingCacheKey(t,n){return n.filter(i=>{var o;const s=ze.fromKey(i),r=new Set(s.scope&&s.scope.split(" ")),a=((o=t.scope)===null||o===void 0?void 0:o.split(" "))||[],c=s.scope&&a.reduce((u,l)=>u&&r.has(l),!0);return s.prefix==="@@auth0spajs@@"&&s.clientId===t.clientId&&s.audience===t.audience&&c})[0]}}class Ma{constructor(t,n,i){this.storage=t,this.clientId=n,this.cookieDomain=i,this.storageKey=`a0.spajs.txs.${this.clientId}`}create(t){this.storage.save(this.storageKey,t,{daysUntilExpire:1,cookieDomain:this.cookieDomain})}get(){return this.storage.get(this.storageKey)}remove(){this.storage.remove(this.storageKey,{cookieDomain:this.cookieDomain})}}const Nt=e=>typeof e=="number",La=["iss","aud","exp","nbf","iat","jti","azp","nonce","auth_time","at_hash","c_hash","acr","amr","sub_jwk","cnf","sip_from_tag","sip_date","sip_callid","sip_cseq_num","sip_via_branch","orig","dest","mky","events","toe","txn","rph","sid","vot","vtm"];var ct=zi(function(e,t){var n=wt&&wt.__assign||function(){return n=Object.assign||function(c){for(var u,l=1,f=arguments.length;l<f;l++)for(var h in u=arguments[l])Object.prototype.hasOwnProperty.call(u,h)&&(c[h]=u[h]);return c},n.apply(this,arguments)};function i(c,u){if(!u)return"";var l="; "+c;return u===!0?l:l+"="+u}function o(c,u,l){return encodeURIComponent(c).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/\(/g,"%28").replace(/\)/g,"%29")+"="+encodeURIComponent(u).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent)+function(f){if(typeof f.expires=="number"){var h=new Date;h.setMilliseconds(h.getMilliseconds()+864e5*f.expires),f.expires=h}return i("Expires",f.expires?f.expires.toUTCString():"")+i("Domain",f.domain)+i("Path",f.path)+i("Secure",f.secure)+i("SameSite",f.sameSite)}(l)}function s(c){for(var u={},l=c?c.split("; "):[],f=/(%[\dA-F]{2})+/gi,h=0;h<l.length;h++){var g=l[h].split("="),w=g.slice(1).join("=");w.charAt(0)==='"'&&(w=w.slice(1,-1));try{u[g[0].replace(f,decodeURIComponent)]=w.replace(f,decodeURIComponent)}catch{}}return u}function r(){return s(document.cookie)}function a(c,u,l){document.cookie=o(c,u,n({path:"/"},l))}t.__esModule=!0,t.encode=o,t.parse=s,t.getAll=r,t.get=function(c){return r()[c]},t.set=a,t.remove=function(c,u){a(c,"",n(n({},u),{expires:-1}))}});Ni(ct),ct.encode,ct.parse,ct.getAll;var Na=ct.get,Zs=ct.set,Bs=ct.remove;const mt={get(e){const t=Na(e);if(t!==void 0)return JSON.parse(t)},save(e,t,n){let i={};window.location.protocol==="https:"&&(i={secure:!0,sameSite:"none"}),n!=null&&n.daysUntilExpire&&(i.expires=n.daysUntilExpire),n!=null&&n.cookieDomain&&(i.domain=n.cookieDomain),Zs(e,JSON.stringify(t),i)},remove(e,t){let n={};t!=null&&t.cookieDomain&&(n.domain=t.cookieDomain),Bs(e,n)}},za={get:e=>mt.get(e)||mt.get(`_legacy_${e}`),save(e,t,n){let i={};window.location.protocol==="https:"&&(i={secure:!0}),n!=null&&n.daysUntilExpire&&(i.expires=n.daysUntilExpire),n!=null&&n.cookieDomain&&(i.domain=n.cookieDomain),Zs(`_legacy_${e}`,JSON.stringify(t),i),mt.save(e,t,n)},remove(e,t){let n={};t!=null&&t.cookieDomain&&(n.domain=t.cookieDomain),Bs(e,n),mt.remove(e,t),mt.remove(`_legacy_${e}`,t)}},Fa={get(e){if(typeof sessionStorage>"u")return;const t=sessionStorage.getItem(e);return t!=null?JSON.parse(t):void 0},save(e,t){sessionStorage.setItem(e,JSON.stringify(t))},remove(e){sessionStorage.removeItem(e)}};var $n,Ka=function(e){return $n=$n||function(t,n,i){var o=n===void 0?null:n,s=function(u,l){var f=atob(u);if(l){for(var h=new Uint8Array(f.length),g=0,w=f.length;g<w;++g)h[g]=f.charCodeAt(g);return String.fromCharCode.apply(null,new Uint16Array(h.buffer))}return f}(t,i!==void 0&&i),r=s.indexOf(`
`,10)+1,a=s.substring(r)+(o?"//# sourceMappingURL="+o:""),c=new Blob([a],{type:"application/javascript"});return URL.createObjectURL(c)}("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",null,!1),new Worker($n,e)};const Zn={};class Ua{constructor(t,n){this.cache=t,this.clientId=n,this.manifestKey=this.createManifestKeyFrom(this.clientId)}async add(t){var n;const i=new Set(((n=await this.cache.get(this.manifestKey))===null||n===void 0?void 0:n.keys)||[]);i.add(t),await this.cache.set(this.manifestKey,{keys:[...i]})}async remove(t){const n=await this.cache.get(this.manifestKey);if(n){const i=new Set(n.keys);return i.delete(t),i.size>0?await this.cache.set(this.manifestKey,{keys:[...i]}):await this.cache.remove(this.manifestKey)}}get(){return this.cache.get(this.manifestKey)}clear(){return this.cache.remove(this.manifestKey)}createManifestKeyFrom(t){return`@@auth0spajs@@::${t}`}}const Da={memory:()=>new $s().enclosedCache,localstorage:()=>new ja},Oo=e=>Da[e],Po=e=>{const{openUrl:t,onRedirect:n}=e,i=Me(e,["openUrl","onRedirect"]);return Object.assign(Object.assign({},i),{openUrl:t===!1||t?t:n})},Bn=new Pa;class Ha{constructor(t){let n,i;if(this.userCache=new $s().enclosedCache,this.defaultOptions={authorizationParams:{scope:"openid profile email"},useRefreshTokensFallback:!1,useFormData:!0},this._releaseLockOnPageHide=async()=>{await Bn.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)},this.options=Object.assign(Object.assign(Object.assign({},this.defaultOptions),t),{authorizationParams:Object.assign(Object.assign({},this.defaultOptions.authorizationParams),t.authorizationParams)}),typeof window<"u"&&(()=>{if(!pn())throw new Error("For security reasons, `window.crypto` is required to run `auth0-spa-js`.");if(pn().subtle===void 0)throw new Error(`
      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/master/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.
    `)})(),t.cache&&t.cacheLocation&&console.warn("Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`."),t.cache)i=t.cache;else{if(n=t.cacheLocation||"memory",!Oo(n))throw new Error(`Invalid cache location "${n}"`);i=Oo(n)()}this.httpTimeoutMs=t.httpTimeoutInSeconds?1e3*t.httpTimeoutInSeconds:1e4,this.cookieStorage=t.legacySameSiteCookie===!1?mt:za,this.orgHintCookieName=`auth0.${this.options.clientId}.organization_hint`,this.isAuthenticatedCookieName=(r=>`auth0.${this.options.clientId}.is.authenticated`)(),this.sessionCheckExpiryDays=t.sessionCheckExpiryDays||1;const o=t.useCookiesForTransactions?this.cookieStorage:Fa;var s;this.scope=ln("openid",this.options.authorizationParams.scope,this.options.useRefreshTokens?"offline_access":""),this.transactionManager=new Ma(o,this.options.clientId,this.options.cookieDomain),this.nowProvider=this.options.nowProvider||Ws,this.cacheManager=new Aa(i,i.allKeys?void 0:new Ua(i,this.options.clientId),this.nowProvider),this.domainUrl=(s=this.options.domain,/^https?:\/\//.test(s)?s:`https://${s}`),this.tokenIssuer=((r,a)=>r?r.startsWith("https://")?r:`https://${r}/`:`${a}/`)(this.options.issuer,this.domainUrl),typeof window<"u"&&window.Worker&&this.options.useRefreshTokens&&n==="memory"&&(this.worker=new Ka)}_url(t){const n=encodeURIComponent(btoa(JSON.stringify(this.options.auth0Client||Hs)));return`${this.domainUrl}${t}&auth0Client=${n}`}_authorizeUrl(t){return this._url(`/authorize?${li(t)}`)}async _verifyIdToken(t,n,i){const o=await this.nowProvider();return(r=>{if(!r.id_token)throw new Error("ID token is required but missing");const a=(f=>{const h=f.split("."),[g,w,E]=h;if(h.length!==3||!g||!w||!E)throw new Error("ID token could not be decoded");const T=JSON.parse(Io(w)),I={__raw:f},v={};return Object.keys(T).forEach(L=>{I[L]=T[L],La.includes(L)||(v[L]=T[L])}),{encoded:{header:g,payload:w,signature:E},header:JSON.parse(Io(g)),claims:I,user:v}})(r.id_token);if(!a.claims.iss)throw new Error("Issuer (iss) claim must be a string present in the ID token");if(a.claims.iss!==r.iss)throw new Error(`Issuer (iss) claim mismatch in the ID token; expected "${r.iss}", found "${a.claims.iss}"`);if(!a.user.sub)throw new Error("Subject (sub) claim must be a string present in the ID token");if(a.header.alg!=="RS256")throw new Error(`Signature algorithm of "${a.header.alg}" is not supported. Expected the ID token to be signed with "RS256".`);if(!a.claims.aud||typeof a.claims.aud!="string"&&!Array.isArray(a.claims.aud))throw new Error("Audience (aud) claim must be a string or array of strings present in the ID token");if(Array.isArray(a.claims.aud)){if(!a.claims.aud.includes(r.aud))throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${r.aud}" but was not one of "${a.claims.aud.join(", ")}"`);if(a.claims.aud.length>1){if(!a.claims.azp)throw new Error("Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values");if(a.claims.azp!==r.aud)throw new Error(`Authorized Party (azp) claim mismatch in the ID token; expected "${r.aud}", found "${a.claims.azp}"`)}}else if(a.claims.aud!==r.aud)throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${r.aud}" but found "${a.claims.aud}"`);if(r.nonce){if(!a.claims.nonce)throw new Error("Nonce (nonce) claim must be a string present in the ID token");if(a.claims.nonce!==r.nonce)throw new Error(`Nonce (nonce) claim mismatch in the ID token; expected "${r.nonce}", found "${a.claims.nonce}"`)}if(r.max_age&&!Nt(a.claims.auth_time))throw new Error("Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified");if(a.claims.exp==null||!Nt(a.claims.exp))throw new Error("Expiration Time (exp) claim must be a number present in the ID token");if(!Nt(a.claims.iat))throw new Error("Issued At (iat) claim must be a number present in the ID token");const c=r.leeway||60,u=new Date(r.now||Date.now()),l=new Date(0);if(l.setUTCSeconds(a.claims.exp+c),u>l)throw new Error(`Expiration Time (exp) claim error in the ID token; current time (${u}) is after expiration time (${l})`);if(a.claims.nbf!=null&&Nt(a.claims.nbf)){const f=new Date(0);if(f.setUTCSeconds(a.claims.nbf-c),u<f)throw new Error(`Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${u}) is before ${f}`)}if(a.claims.auth_time!=null&&Nt(a.claims.auth_time)){const f=new Date(0);if(f.setUTCSeconds(parseInt(a.claims.auth_time)+r.max_age+c),u>f)throw new Error(`Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${u}) is after last auth at ${f}`)}if(r.organization){const f=r.organization.trim();if(f.startsWith("org_")){const h=f;if(!a.claims.org_id)throw new Error("Organization ID (org_id) claim must be a string present in the ID token");if(h!==a.claims.org_id)throw new Error(`Organization ID (org_id) claim mismatch in the ID token; expected "${h}", found "${a.claims.org_id}"`)}else{const h=f.toLowerCase();if(!a.claims.org_name)throw new Error("Organization Name (org_name) claim must be a string present in the ID token");if(h!==a.claims.org_name)throw new Error(`Organization Name (org_name) claim mismatch in the ID token; expected "${h}", found "${a.claims.org_name}"`)}}return a})({iss:this.tokenIssuer,aud:this.options.clientId,id_token:t,nonce:n,organization:i,leeway:this.options.leeway,max_age:(s=this.options.authorizationParams.max_age,typeof s!="string"?s:parseInt(s,10)||void 0),now:o});var s}_processOrgHint(t){t?this.cookieStorage.save(this.orgHintCookieName,t,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}):this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain})}async _prepareAuthorizeUrl(t,n,i){const o=Eo(Wn()),s=Eo(Wn()),r=Wn(),a=(l=>{const f=new Uint8Array(l);return(h=>{const g={"+":"-","/":"_","=":""};return h.replace(/[+/=]/g,w=>g[w])})(window.btoa(String.fromCharCode(...Array.from(f))))})(await(async l=>await pn().subtle.digest({name:"SHA-256"},new TextEncoder().encode(l)))(r)),c=((l,f,h,g,w,E,T,I)=>Object.assign(Object.assign(Object.assign({client_id:l.clientId},l.authorizationParams),h),{scope:ln(f,h.scope),response_type:"code",response_mode:I||"query",state:g,nonce:w,redirect_uri:T||l.authorizationParams.redirect_uri,code_challenge:E,code_challenge_method:"S256"}))(this.options,this.scope,t,o,s,a,t.redirect_uri||this.options.authorizationParams.redirect_uri||i,n==null?void 0:n.response_mode),u=this._authorizeUrl(c);return{nonce:s,code_verifier:r,scope:c.scope,audience:c.audience||"default",redirect_uri:c.redirect_uri,state:o,url:u}}async loginWithPopup(t,n){var i;if(t=t||{},!(n=n||{}).popup&&(n.popup=(a=>{const c=window.screenX+(window.innerWidth-400)/2,u=window.screenY+(window.innerHeight-600)/2;return window.open("","auth0:authorize:popup",`left=${c},top=${u},width=400,height=600,resizable,scrollbars=yes,status=1`)})(),!n.popup))throw new Error("Unable to open a popup for loginWithPopup - window.open returned `null`");const o=await this._prepareAuthorizeUrl(t.authorizationParams||{},{response_mode:"web_message"},window.location.origin);n.popup.location.href=o.url;const s=await(a=>new Promise((c,u)=>{let l;const f=setInterval(()=>{a.popup&&a.popup.closed&&(clearInterval(f),clearTimeout(h),window.removeEventListener("message",l,!1),u(new Ui(a.popup)))},1e3),h=setTimeout(()=>{clearInterval(f),u(new Ki(a.popup)),window.removeEventListener("message",l,!1)},1e3*(a.timeoutInSeconds||60));l=function(g){if(g.data&&g.data.type==="authorization_response"){if(clearTimeout(h),clearInterval(f),window.removeEventListener("message",l,!1),a.popup.close(),g.data.response.error)return u(ce.fromPayload(g.data.response));c(g.data.response)}},window.addEventListener("message",l)}))(Object.assign(Object.assign({},n),{timeoutInSeconds:n.timeoutInSeconds||this.options.authorizeTimeoutInSeconds||60}));if(o.state!==s.state)throw new ce("state_mismatch","Invalid state");const r=((i=t.authorizationParams)===null||i===void 0?void 0:i.organization)||this.options.authorizationParams.organization;await this._requestToken({audience:o.audience,scope:o.scope,code_verifier:o.code_verifier,grant_type:"authorization_code",code:s.code,redirect_uri:o.redirect_uri},{nonceIn:o.nonce,organization:r})}async getUser(){var t;const n=await this._getIdTokenFromCache();return(t=n==null?void 0:n.decodedToken)===null||t===void 0?void 0:t.user}async getIdTokenClaims(){var t;const n=await this._getIdTokenFromCache();return(t=n==null?void 0:n.decodedToken)===null||t===void 0?void 0:t.claims}async loginWithRedirect(t={}){var n;const i=Po(t),{openUrl:o,fragment:s,appState:r}=i,a=Me(i,["openUrl","fragment","appState"]),c=((n=a.authorizationParams)===null||n===void 0?void 0:n.organization)||this.options.authorizationParams.organization,u=await this._prepareAuthorizeUrl(a.authorizationParams||{}),{url:l}=u,f=Me(u,["url"]);this.transactionManager.create(Object.assign(Object.assign(Object.assign({},f),{appState:r}),c&&{organization:c}));const h=s?`${l}#${s}`:l;o?await o(h):window.location.assign(h)}async handleRedirectCallback(t=window.location.href){const n=t.split("?").slice(1);if(n.length===0)throw new Error("There are no query params available for parsing.");const{state:i,code:o,error:s,error_description:r}=(f=>{f.indexOf("#")>-1&&(f=f.substring(0,f.indexOf("#")));const h=new URLSearchParams(f);return{state:h.get("state"),code:h.get("code")||void 0,error:h.get("error")||void 0,error_description:h.get("error_description")||void 0}})(n.join("")),a=this.transactionManager.get();if(!a)throw new ce("missing_transaction","Invalid state");if(this.transactionManager.remove(),s)throw new Fi(s,r||s,i,a.appState);if(!a.code_verifier||a.state&&a.state!==i)throw new ce("state_mismatch","Invalid state");const c=a.organization,u=a.nonce,l=a.redirect_uri;return await this._requestToken(Object.assign({audience:a.audience,scope:a.scope,code_verifier:a.code_verifier,grant_type:"authorization_code",code:o},l?{redirect_uri:l}:{}),{nonceIn:u,organization:c}),{appState:a.appState}}async checkSession(t){if(!this.cookieStorage.get(this.isAuthenticatedCookieName)){if(!this.cookieStorage.get("auth0.is.authenticated"))return;this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove("auth0.is.authenticated")}try{await this.getTokenSilently(t)}catch{}}async getTokenSilently(t={}){var n;const i=Object.assign(Object.assign({cacheMode:"on"},t),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),t.authorizationParams),{scope:ln(this.scope,(n=t.authorizationParams)===null||n===void 0?void 0:n.scope)})}),o=await((s,r)=>{let a=Zn[r];return a||(a=s().finally(()=>{delete Zn[r],a=null}),Zn[r]=a),a})(()=>this._getTokenSilently(i),`${this.options.clientId}::${i.authorizationParams.audience}::${i.authorizationParams.scope}`);return t.detailedResponse?o:o==null?void 0:o.access_token}async _getTokenSilently(t){const{cacheMode:n}=t,i=Me(t,["cacheMode"]);if(n!=="off"){const o=await this._getEntryFromCache({scope:i.authorizationParams.scope,audience:i.authorizationParams.audience||"default",clientId:this.options.clientId});if(o)return o}if(n!=="cache-only"){if(!await(async(o,s=3)=>{for(let r=0;r<s;r++)if(await o())return!0;return!1})(()=>Bn.acquireLock("auth0.lock.getTokenSilently",5e3),10))throw new Qt;try{if(window.addEventListener("pagehide",this._releaseLockOnPageHide),n!=="off"){const u=await this._getEntryFromCache({scope:i.authorizationParams.scope,audience:i.authorizationParams.audience||"default",clientId:this.options.clientId});if(u)return u}const o=this.options.useRefreshTokens?await this._getTokenUsingRefreshToken(i):await this._getTokenFromIFrame(i),{id_token:s,access_token:r,oauthTokenScope:a,expires_in:c}=o;return Object.assign(Object.assign({id_token:s,access_token:r},a?{scope:a}:null),{expires_in:c})}finally{await Bn.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)}}}async getTokenWithPopup(t={},n={}){var i;const o=Object.assign(Object.assign({},t),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),t.authorizationParams),{scope:ln(this.scope,(i=t.authorizationParams)===null||i===void 0?void 0:i.scope)})});return n=Object.assign(Object.assign({},Ca),n),await this.loginWithPopup(o,n),(await this.cacheManager.get(new ze({scope:o.authorizationParams.scope,audience:o.authorizationParams.audience||"default",clientId:this.options.clientId}))).access_token}async isAuthenticated(){return!!await this.getUser()}_buildLogoutUrl(t){t.clientId!==null?t.clientId=t.clientId||this.options.clientId:delete t.clientId;const n=t.logoutParams||{},{federated:i}=n,o=Me(n,["federated"]),s=i?"&federated":"";return this._url(`/v2/logout?${li(Object.assign({clientId:t.clientId},o))}`)+s}async logout(t={}){const n=Po(t),{openUrl:i}=n,o=Me(n,["openUrl"]);t.clientId===null?await this.cacheManager.clear():await this.cacheManager.clear(t.clientId||this.options.clientId),this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove(this.isAuthenticatedCookieName,{cookieDomain:this.options.cookieDomain}),this.userCache.remove("@@user@@");const s=this._buildLogoutUrl(o);i?await i(s):i!==!1&&window.location.assign(s)}async _getTokenFromIFrame(t){const n=Object.assign(Object.assign({},t.authorizationParams),{prompt:"none"}),i=this.cookieStorage.get(this.orgHintCookieName);i&&!n.organization&&(n.organization=i);const{url:o,state:s,nonce:r,code_verifier:a,redirect_uri:c,scope:u,audience:l}=await this._prepareAuthorizeUrl(n,{response_mode:"web_message"},window.location.origin);try{if(window.crossOriginIsolated)throw new ce("login_required","The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.");const f=t.timeoutInSeconds||this.options.authorizeTimeoutInSeconds,h=await((w,E,T=60)=>new Promise((I,v)=>{const L=window.document.createElement("iframe");L.setAttribute("width","0"),L.setAttribute("height","0"),L.style.display="none";const A=()=>{window.document.body.contains(L)&&(window.document.body.removeChild(L),window.removeEventListener("message",q,!1))};let q;const Q=setTimeout(()=>{v(new Qt),A()},1e3*T);q=function(ee){if(ee.origin!=E||!ee.data||ee.data.type!=="authorization_response")return;const ne=ee.source;ne&&ne.close(),ee.data.response.error?v(ce.fromPayload(ee.data.response)):I(ee.data.response),clearTimeout(Q),window.removeEventListener("message",q,!1),setTimeout(A,2e3)},window.addEventListener("message",q,!1),window.document.body.appendChild(L),L.setAttribute("src",w)}))(o,this.domainUrl,f);if(s!==h.state)throw new ce("state_mismatch","Invalid state");const g=await this._requestToken(Object.assign(Object.assign({},t.authorizationParams),{code_verifier:a,code:h.code,grant_type:"authorization_code",redirect_uri:c,timeout:t.authorizationParams.timeout||this.httpTimeoutMs}),{nonceIn:r,organization:n.organization});return Object.assign(Object.assign({},g),{scope:u,oauthTokenScope:g.scope,audience:l})}catch(f){throw f.error==="login_required"&&this.logout({openUrl:!1}),f}}async _getTokenUsingRefreshToken(t){const n=await this.cacheManager.get(new ze({scope:t.authorizationParams.scope,audience:t.authorizationParams.audience||"default",clientId:this.options.clientId}));if(!(n&&n.refresh_token||this.worker)){if(this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(t);throw new An(t.authorizationParams.audience||"default",t.authorizationParams.scope)}const i=t.authorizationParams.redirect_uri||this.options.authorizationParams.redirect_uri||window.location.origin,o=typeof t.timeoutInSeconds=="number"?1e3*t.timeoutInSeconds:null;try{const s=await this._requestToken(Object.assign(Object.assign(Object.assign({},t.authorizationParams),{grant_type:"refresh_token",refresh_token:n&&n.refresh_token,redirect_uri:i}),o&&{timeout:o}));return Object.assign(Object.assign({},s),{scope:t.authorizationParams.scope,oauthTokenScope:s.scope,audience:t.authorizationParams.audience||"default"})}catch(s){if((s.message.indexOf("Missing Refresh Token")>-1||s.message&&s.message.indexOf("invalid refresh token")>-1)&&this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(t);throw s}}async _saveEntryInCache(t){const{id_token:n,decodedToken:i}=t,o=Me(t,["id_token","decodedToken"]);this.userCache.set("@@user@@",{id_token:n,decodedToken:i}),await this.cacheManager.setIdToken(this.options.clientId,t.id_token,t.decodedToken),await this.cacheManager.set(o)}async _getIdTokenFromCache(){const t=this.options.authorizationParams.audience||"default",n=await this.cacheManager.getIdToken(new ze({clientId:this.options.clientId,audience:t,scope:this.scope})),i=this.userCache.get("@@user@@");return n&&n.id_token===(i==null?void 0:i.id_token)?i:(this.userCache.set("@@user@@",n),n)}async _getEntryFromCache({scope:t,audience:n,clientId:i}){const o=await this.cacheManager.get(new ze({scope:t,audience:n,clientId:i}),60);if(o&&o.access_token){const{access_token:s,oauthTokenScope:r,expires_in:a}=o,c=await this._getIdTokenFromCache();return c&&Object.assign(Object.assign({id_token:c.id_token,access_token:s},r?{scope:r}:null),{expires_in:a})}}async _requestToken(t,n){const{nonceIn:i,organization:o}=n||{},s=await Ra(Object.assign({baseUrl:this.domainUrl,client_id:this.options.clientId,auth0Client:this.options.auth0Client,useFormData:this.options.useFormData,timeout:this.httpTimeoutMs},t),this.worker),r=await this._verifyIdToken(s.id_token,i,o);return await this._saveEntryInCache(Object.assign(Object.assign(Object.assign(Object.assign({},s),{decodedToken:r,scope:t.scope,audience:t.audience||"default"}),s.scope?{oauthTokenScope:s.scope}:null),{client_id:this.options.clientId})),this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this._processOrgHint(o||r.claims.org_id),Object.assign(Object.assign({},s),{decodedToken:r})}}function Ut(e){e!=null&&e.redirect_uri&&(console.warn("Using `redirect_uri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `redirectUri` will be no longer supported in a future version"),e.authorizationParams=e.authorizationParams||{},e.authorizationParams.redirect_uri=e.redirect_uri,delete e.redirect_uri)}const it=()=>{console.error("Please ensure Auth0's Vue plugin is correctly installed.")},Wa={isLoading:ge(!1),isAuthenticated:ge(!1),user:ge(void 0),idTokenClaims:ge(void 0),error:ge(null),loginWithPopup:it,loginWithRedirect:it,getAccessTokenSilently:it,getAccessTokenWithPopup:it,logout:it,checkSession:it,handleRedirectCallback:it},$a=ge(Wa);class Za{constructor(t,n){var i,o;this.clientOptions=t,this.pluginOptions=n,this.isLoading=ge(!0),this.isAuthenticated=ge(!1),this.user=ge({}),this.idTokenClaims=ge(),this.error=ge(null),i=this,o=["constructor"],Object.getOwnPropertyNames(Object.getPrototypeOf(i)).filter(s=>!o.includes(s)).forEach(s=>i[s]=i[s].bind(i))}install(t){this._client=new Ha(Object.assign(Object.assign({},this.clientOptions),{auth0Client:{name:"auth0-vue",version:"2.3.1"}})),this.__checkSession(t.config.globalProperties.$router),t.config.globalProperties.$auth0=this,t.provide(Ds,this),$a.value=this}async loginWithRedirect(t){return Ut(t),this._client.loginWithRedirect(t)}async loginWithPopup(t,n){return Ut(t),this.__proxy(()=>this._client.loginWithPopup(t,n))}async logout(t){return t!=null&&t.openUrl||(t==null?void 0:t.openUrl)===!1?this.__proxy(()=>this._client.logout(t)):this._client.logout(t)}async getAccessTokenSilently(t={}){return Ut(t),this.__proxy(()=>this._client.getTokenSilently(t))}async getAccessTokenWithPopup(t,n){return Ut(t),this.__proxy(()=>this._client.getTokenWithPopup(t,n))}async checkSession(t){return this.__proxy(()=>this._client.checkSession(t))}async handleRedirectCallback(t){return this.__proxy(()=>this._client.handleRedirectCallback(t))}async __checkSession(t){var n,i,o;const s=window.location.search;try{if((s.includes("code=")||s.includes("error="))&&s.includes("state=")&&!(!((n=this.pluginOptions)===null||n===void 0)&&n.skipRedirectCallback)){const r=await this.handleRedirectCallback(),a=r==null?void 0:r.appState,c=(i=a==null?void 0:a.target)!==null&&i!==void 0?i:"/";return window.history.replaceState({},"","/"),t&&t.push(c),r}await this.checkSession()}catch{window.history.replaceState({},"","/"),t&&t.push(((o=this.pluginOptions)===null||o===void 0?void 0:o.errorPath)||"/")}}async __refreshState(){this.isAuthenticated.value=await this._client.isAuthenticated(),this.user.value=await this._client.getUser(),this.idTokenClaims.value=await this._client.getIdTokenClaims(),this.isLoading.value=!1}async __proxy(t,n=!0){let i;try{i=await t(),this.error.value=null}catch(o){throw this.error.value=o,o}finally{n&&await this.__refreshState()}return i}}function Ba(e,t){return Ut(e),new Za(e,t)}function Vs(){return we(Ds)}/*!
  * vue-router v4.2.4
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */const gt=typeof window<"u";function Va(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const B=Object.assign;function Vn(e,t){const n={};for(const i in t){const o=t[i];n[i]=Oe(o)?o.map(e):e(o)}return n}const $t=()=>{},Oe=Array.isArray,Xa=/\/$/,Ja=e=>e.replace(Xa,"");function Xn(e,t,n="/"){let i,o={},s="",r="";const a=t.indexOf("#");let c=t.indexOf("?");return a<c&&a>=0&&(c=-1),c>-1&&(i=t.slice(0,c),s=t.slice(c+1,a>-1?a:t.length),o=e(s)),a>-1&&(i=i||t.slice(0,a),r=t.slice(a,t.length)),i=Qa(i??t,n),{fullPath:i+(s&&"?")+s+r,path:i,query:o,hash:r}}function Ya(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Co(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function qa(e,t,n){const i=t.matched.length-1,o=n.matched.length-1;return i>-1&&i===o&&Pt(t.matched[i],n.matched[o])&&Xs(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Pt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Xs(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Ga(e[n],t[n]))return!1;return!0}function Ga(e,t){return Oe(e)?So(e,t):Oe(t)?So(t,e):e===t}function So(e,t){return Oe(t)?e.length===t.length&&e.every((n,i)=>n===t[i]):e.length===1&&e[0]===t}function Qa(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),i=e.split("/"),o=i[i.length-1];(o===".."||o===".")&&i.push("");let s=n.length-1,r,a;for(r=0;r<i.length;r++)if(a=i[r],a!==".")if(a==="..")s>1&&s--;else break;return n.slice(0,s).join("/")+"/"+i.slice(r-(r===i.length?1:0)).join("/")}var en;(function(e){e.pop="pop",e.push="push"})(en||(en={}));var Zt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Zt||(Zt={}));function el(e){if(!e)if(gt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Ja(e)}const tl=/^[^#]+#/;function nl(e,t){return e.replace(tl,"#")+t}function il(e,t){const n=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-n.left-(t.left||0),top:i.top-n.top-(t.top||0)}}const Mn=()=>({left:window.pageXOffset,top:window.pageYOffset});function ol(e){let t;if("el"in e){const n=e.el,i=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?i?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=il(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.pageXOffset,t.top!=null?t.top:window.pageYOffset)}function To(e,t){return(history.state?history.state.position-t:-1)+e}const ui=new Map;function sl(e,t){ui.set(e,t)}function rl(e){const t=ui.get(e);return ui.delete(e),t}let cl=()=>location.protocol+"//"+location.host;function Js(e,t){const{pathname:n,search:i,hash:o}=t,s=e.indexOf("#");if(s>-1){let a=o.includes(e.slice(s))?e.slice(s).length:1,c=o.slice(a);return c[0]!=="/"&&(c="/"+c),Co(c,"")}return Co(n,e)+i+o}function al(e,t,n,i){let o=[],s=[],r=null;const a=({state:h})=>{const g=Js(e,location),w=n.value,E=t.value;let T=0;if(h){if(n.value=g,t.value=h,r&&r===w){r=null;return}T=E?h.position-E.position:0}else i(g);o.forEach(I=>{I(n.value,w,{delta:T,type:en.pop,direction:T?T>0?Zt.forward:Zt.back:Zt.unknown})})};function c(){r=n.value}function u(h){o.push(h);const g=()=>{const w=o.indexOf(h);w>-1&&o.splice(w,1)};return s.push(g),g}function l(){const{history:h}=window;h.state&&h.replaceState(B({},h.state,{scroll:Mn()}),"")}function f(){for(const h of s)h();s=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:c,listen:u,destroy:f}}function Ro(e,t,n,i=!1,o=!1){return{back:e,current:t,forward:n,replaced:i,position:window.history.length,scroll:o?Mn():null}}function ll(e){const{history:t,location:n}=window,i={value:Js(e,n)},o={value:t.state};o.value||s(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(c,u,l){const f=e.indexOf("#"),h=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+c:cl()+e+c;try{t[l?"replaceState":"pushState"](u,"",h),o.value=u}catch(g){console.error(g),n[l?"replace":"assign"](h)}}function r(c,u){const l=B({},t.state,Ro(o.value.back,c,o.value.forward,!0),u,{position:o.value.position});s(c,l,!0),i.value=c}function a(c,u){const l=B({},o.value,t.state,{forward:c,scroll:Mn()});s(l.current,l,!0);const f=B({},Ro(i.value,c,null),{position:l.position+1},u);s(c,f,!1),i.value=c}return{location:i,state:o,push:a,replace:r}}function ul(e){e=el(e);const t=ll(e),n=al(e,t.state,t.location,t.replace);function i(s,r=!0){r||n.pauseListeners(),history.go(s)}const o=B({location:"",base:e,go:i,createHref:nl.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function fl(e){return typeof e=="string"||e&&typeof e=="object"}function Ys(e){return typeof e=="string"||typeof e=="symbol"}const Ve={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},qs=Symbol("");var jo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(jo||(jo={}));function Ct(e,t){return B(new Error,{type:e,[qs]:!0},t)}function Ke(e,t){return e instanceof Error&&qs in e&&(t==null||!!(e.type&t))}const Ao="[^/]+?",dl={sensitive:!1,strict:!1,start:!0,end:!0},hl=/[.+*?^${}()[\]/\\]/g;function pl(e,t){const n=B({},dl,t),i=[];let o=n.start?"^":"";const s=[];for(const u of e){const l=u.length?[]:[90];n.strict&&!u.length&&(o+="/");for(let f=0;f<u.length;f++){const h=u[f];let g=40+(n.sensitive?.25:0);if(h.type===0)f||(o+="/"),o+=h.value.replace(hl,"\\$&"),g+=40;else if(h.type===1){const{value:w,repeatable:E,optional:T,regexp:I}=h;s.push({name:w,repeatable:E,optional:T});const v=I||Ao;if(v!==Ao){g+=10;try{new RegExp(`(${v})`)}catch(A){throw new Error(`Invalid custom RegExp for param "${w}" (${v}): `+A.message)}}let L=E?`((?:${v})(?:/(?:${v}))*)`:`(${v})`;f||(L=T&&u.length<2?`(?:/${L})`:"/"+L),T&&(L+="?"),o+=L,g+=20,T&&(g+=-8),E&&(g+=-20),v===".*"&&(g+=-50)}l.push(g)}i.push(l)}if(n.strict&&n.end){const u=i.length-1;i[u][i[u].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const r=new RegExp(o,n.sensitive?"":"i");function a(u){const l=u.match(r),f={};if(!l)return null;for(let h=1;h<l.length;h++){const g=l[h]||"",w=s[h-1];f[w.name]=g&&w.repeatable?g.split("/"):g}return f}function c(u){let l="",f=!1;for(const h of e){(!f||!l.endsWith("/"))&&(l+="/"),f=!1;for(const g of h)if(g.type===0)l+=g.value;else if(g.type===1){const{value:w,repeatable:E,optional:T}=g,I=w in u?u[w]:"";if(Oe(I)&&!E)throw new Error(`Provided param "${w}" is an array but it is not repeatable (* or + modifiers)`);const v=Oe(I)?I.join("/"):I;if(!v)if(T)h.length<2&&(l.endsWith("/")?l=l.slice(0,-1):f=!0);else throw new Error(`Missing required param "${w}"`);l+=v}}return l||"/"}return{re:r,score:i,keys:s,parse:a,stringify:c}}function ml(e,t){let n=0;for(;n<e.length&&n<t.length;){const i=t[n]-e[n];if(i)return i;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function gl(e,t){let n=0;const i=e.score,o=t.score;for(;n<i.length&&n<o.length;){const s=ml(i[n],o[n]);if(s)return s;n++}if(Math.abs(o.length-i.length)===1){if(Mo(i))return 1;if(Mo(o))return-1}return o.length-i.length}function Mo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const yl={type:0,value:""},wl=/[a-zA-Z0-9_]/;function _l(e){if(!e)return[[]];if(e==="/")return[[yl]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${u}": ${g}`)}let n=0,i=n;const o=[];let s;function r(){s&&o.push(s),s=[]}let a=0,c,u="",l="";function f(){u&&(n===0?s.push({type:0,value:u}):n===1||n===2||n===3?(s.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:u,regexp:l,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),u="")}function h(){u+=c}for(;a<e.length;){if(c=e[a++],c==="\\"&&n!==2){i=n,n=4;continue}switch(n){case 0:c==="/"?(u&&f(),r()):c===":"?(f(),n=1):h();break;case 4:h(),n=i;break;case 1:c==="("?n=2:wl.test(c)?h():(f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--);break;case 2:c===")"?l[l.length-1]=="\\"?l=l.slice(0,-1)+c:n=3:l+=c;break;case 3:f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&a--,l="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),r(),o}function bl(e,t,n){const i=pl(_l(e.path),n),o=B(i,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function vl(e,t){const n=[],i=new Map;t=zo({strict:!1,end:!0,sensitive:!1},t);function o(l){return i.get(l)}function s(l,f,h){const g=!h,w=kl(l);w.aliasOf=h&&h.record;const E=zo(t,l),T=[w];if("alias"in l){const L=typeof l.alias=="string"?[l.alias]:l.alias;for(const A of L)T.push(B({},w,{components:h?h.record.components:w.components,path:A,aliasOf:h?h.record:w}))}let I,v;for(const L of T){const{path:A}=L;if(f&&A[0]!=="/"){const q=f.record.path,Q=q[q.length-1]==="/"?"":"/";L.path=f.record.path+(A&&Q+A)}if(I=bl(L,f,E),h?h.alias.push(I):(v=v||I,v!==I&&v.alias.push(I),g&&l.name&&!No(I)&&r(l.name)),w.children){const q=w.children;for(let Q=0;Q<q.length;Q++)s(q[Q],I,h&&h.children[Q])}h=h||I,(I.record.components&&Object.keys(I.record.components).length||I.record.name||I.record.redirect)&&c(I)}return v?()=>{r(v)}:$t}function r(l){if(Ys(l)){const f=i.get(l);f&&(i.delete(l),n.splice(n.indexOf(f),1),f.children.forEach(r),f.alias.forEach(r))}else{const f=n.indexOf(l);f>-1&&(n.splice(f,1),l.record.name&&i.delete(l.record.name),l.children.forEach(r),l.alias.forEach(r))}}function a(){return n}function c(l){let f=0;for(;f<n.length&&gl(l,n[f])>=0&&(l.record.path!==n[f].record.path||!Gs(l,n[f]));)f++;n.splice(f,0,l),l.record.name&&!No(l)&&i.set(l.record.name,l)}function u(l,f){let h,g={},w,E;if("name"in l&&l.name){if(h=i.get(l.name),!h)throw Ct(1,{location:l});E=h.record.name,g=B(Lo(f.params,h.keys.filter(v=>!v.optional).map(v=>v.name)),l.params&&Lo(l.params,h.keys.map(v=>v.name))),w=h.stringify(g)}else if("path"in l)w=l.path,h=n.find(v=>v.re.test(w)),h&&(g=h.parse(w),E=h.record.name);else{if(h=f.name?i.get(f.name):n.find(v=>v.re.test(f.path)),!h)throw Ct(1,{location:l,currentLocation:f});E=h.record.name,g=B({},f.params,l.params),w=h.stringify(g)}const T=[];let I=h;for(;I;)T.unshift(I.record),I=I.parent;return{name:E,path:w,params:g,matched:T,meta:El(T)}}return e.forEach(l=>s(l)),{addRoute:s,resolve:u,removeRoute:r,getRoutes:a,getRecordMatcher:o}}function Lo(e,t){const n={};for(const i of t)i in e&&(n[i]=e[i]);return n}function kl(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:xl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function xl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const i in e.components)t[i]=typeof n=="object"?n[i]:n;return t}function No(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function El(e){return e.reduce((t,n)=>B(t,n.meta),{})}function zo(e,t){const n={};for(const i in e)n[i]=i in t?t[i]:e[i];return n}function Gs(e,t){return t.children.some(n=>n===e||Gs(e,n))}const Qs=/#/g,Il=/&/g,Ol=/\//g,Pl=/=/g,Cl=/\?/g,er=/\+/g,Sl=/%5B/g,Tl=/%5D/g,tr=/%5E/g,Rl=/%60/g,nr=/%7B/g,jl=/%7C/g,ir=/%7D/g,Al=/%20/g;function Hi(e){return encodeURI(""+e).replace(jl,"|").replace(Sl,"[").replace(Tl,"]")}function Ml(e){return Hi(e).replace(nr,"{").replace(ir,"}").replace(tr,"^")}function fi(e){return Hi(e).replace(er,"%2B").replace(Al,"+").replace(Qs,"%23").replace(Il,"%26").replace(Rl,"`").replace(nr,"{").replace(ir,"}").replace(tr,"^")}function Ll(e){return fi(e).replace(Pl,"%3D")}function Nl(e){return Hi(e).replace(Qs,"%23").replace(Cl,"%3F")}function zl(e){return e==null?"":Nl(e).replace(Ol,"%2F")}function bn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}function Fl(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<i.length;++o){const s=i[o].replace(er," "),r=s.indexOf("="),a=bn(r<0?s:s.slice(0,r)),c=r<0?null:bn(s.slice(r+1));if(a in t){let u=t[a];Oe(u)||(u=t[a]=[u]),u.push(c)}else t[a]=c}return t}function Fo(e){let t="";for(let n in e){const i=e[n];if(n=Ll(n),i==null){i!==void 0&&(t+=(t.length?"&":"")+n);continue}(Oe(i)?i.map(s=>s&&fi(s)):[i&&fi(i)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+n,s!=null&&(t+="="+s))})}return t}function Kl(e){const t={};for(const n in e){const i=e[n];i!==void 0&&(t[n]=Oe(i)?i.map(o=>o==null?null:""+o):i==null?i:""+i)}return t}const Ul=Symbol(""),Ko=Symbol(""),Ln=Symbol(""),Wi=Symbol(""),di=Symbol("");function zt(){let e=[];function t(i){return e.push(i),()=>{const o=e.indexOf(i);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Je(e,t,n,i,o){const s=i&&(i.enterCallbacks[o]=i.enterCallbacks[o]||[]);return()=>new Promise((r,a)=>{const c=f=>{f===!1?a(Ct(4,{from:n,to:t})):f instanceof Error?a(f):fl(f)?a(Ct(2,{from:t,to:f})):(s&&i.enterCallbacks[o]===s&&typeof f=="function"&&s.push(f),r())},u=e.call(i&&i.instances[o],t,n,c);let l=Promise.resolve(u);e.length<3&&(l=l.then(c)),l.catch(f=>a(f))})}function Jn(e,t,n,i){const o=[];for(const s of e)for(const r in s.components){let a=s.components[r];if(!(t!=="beforeRouteEnter"&&!s.instances[r]))if(Dl(a)){const u=(a.__vccOpts||a)[t];u&&o.push(Je(u,n,i,s,r))}else{let c=a();o.push(()=>c.then(u=>{if(!u)return Promise.reject(new Error(`Couldn't resolve component "${r}" at "${s.path}"`));const l=Va(u)?u.default:u;s.components[r]=l;const h=(l.__vccOpts||l)[t];return h&&Je(h,n,i,s,r)()}))}}return o}function Dl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Uo(e){const t=we(Ln),n=we(Wi),i=ke(()=>t.resolve(qe(e.to))),o=ke(()=>{const{matched:c}=i.value,{length:u}=c,l=c[u-1],f=n.matched;if(!l||!f.length)return-1;const h=f.findIndex(Pt.bind(null,l));if(h>-1)return h;const g=Do(c[u-2]);return u>1&&Do(l)===g&&f[f.length-1].path!==g?f.findIndex(Pt.bind(null,c[u-2])):h}),s=ke(()=>o.value>-1&&Zl(n.params,i.value.params)),r=ke(()=>o.value>-1&&o.value===n.matched.length-1&&Xs(n.params,i.value.params));function a(c={}){return $l(c)?t[qe(e.replace)?"replace":"push"](qe(e.to)).catch($t):Promise.resolve()}return{route:i,href:ke(()=>i.value.href),isActive:s,isExactActive:r,navigate:a}}const Hl=jt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Uo,setup(e,{slots:t}){const n=In(Uo(e)),{options:i}=we(Ln),o=ke(()=>({[Ho(e.activeClass,i.linkActiveClass,"router-link-active")]:n.isActive,[Ho(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const s=t.default&&t.default(n);return e.custom?s:Us("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},s)}}}),Wl=Hl;function $l(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Zl(e,t){for(const n in t){const i=t[n],o=e[n];if(typeof i=="string"){if(i!==o)return!1}else if(!Oe(o)||o.length!==i.length||i.some((s,r)=>s!==o[r]))return!1}return!0}function Do(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ho=(e,t,n)=>e??t??n,Bl=jt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const i=we(di),o=ke(()=>e.route||i.value),s=we(Ko,0),r=ke(()=>{let u=qe(s);const{matched:l}=o.value;let f;for(;(f=l[u])&&!f.components;)u++;return u}),a=ke(()=>o.value.matched[r.value]);dn(Ko,ke(()=>r.value+1)),dn(Ul,a),dn(di,o);const c=ge();return Dt(()=>[c.value,a.value,e.name],([u,l,f],[h,g,w])=>{l&&(l.instances[f]=u,g&&g!==l&&u&&u===h&&(l.leaveGuards.size||(l.leaveGuards=g.leaveGuards),l.updateGuards.size||(l.updateGuards=g.updateGuards))),u&&l&&(!g||!Pt(l,g)||!h)&&(l.enterCallbacks[f]||[]).forEach(E=>E(u))},{flush:"post"}),()=>{const u=o.value,l=e.name,f=a.value,h=f&&f.components[l];if(!h)return Wo(n.default,{Component:h,route:u});const g=f.props[l],w=g?g===!0?u.params:typeof g=="function"?g(u):g:null,T=Us(h,B({},w,t,{onVnodeUnmounted:I=>{I.component.isUnmounted&&(f.instances[l]=null)},ref:c}));return Wo(n.default,{Component:T,route:u})||T}}});function Wo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const or=Bl;function Vl(e){const t=vl(e.routes,e),n=e.parseQuery||Fl,i=e.stringifyQuery||Fo,o=e.history,s=zt(),r=zt(),a=zt(),c=Xr(Ve);let u=Ve;gt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=Vn.bind(null,_=>""+_),f=Vn.bind(null,zl),h=Vn.bind(null,bn);function g(_,R){let C,M;return Ys(_)?(C=t.getRecordMatcher(_),M=R):M=_,t.addRoute(M,C)}function w(_){const R=t.getRecordMatcher(_);R&&t.removeRoute(R)}function E(){return t.getRoutes().map(_=>_.record)}function T(_){return!!t.getRecordMatcher(_)}function I(_,R){if(R=B({},R||c.value),typeof _=="string"){const m=Xn(n,_,R.path),y=t.resolve({path:m.path},R),b=o.createHref(m.fullPath);return B(m,y,{params:h(y.params),hash:bn(m.hash),redirectedFrom:void 0,href:b})}let C;if("path"in _)C=B({},_,{path:Xn(n,_.path,R.path).path});else{const m=B({},_.params);for(const y in m)m[y]==null&&delete m[y];C=B({},_,{params:f(m)}),R.params=f(R.params)}const M=t.resolve(C,R),Z=_.hash||"";M.params=l(h(M.params));const d=Ya(i,B({},_,{hash:Ml(Z),path:M.path})),p=o.createHref(d);return B({fullPath:d,hash:Z,query:i===Fo?Kl(_.query):_.query||{}},M,{redirectedFrom:void 0,href:p})}function v(_){return typeof _=="string"?Xn(n,_,c.value.path):B({},_)}function L(_,R){if(u!==_)return Ct(8,{from:R,to:_})}function A(_){return ee(_)}function q(_){return A(B(v(_),{replace:!0}))}function Q(_){const R=_.matched[_.matched.length-1];if(R&&R.redirect){const{redirect:C}=R;let M=typeof C=="function"?C(_):C;return typeof M=="string"&&(M=M.includes("?")||M.includes("#")?M=v(M):{path:M},M.params={}),B({query:_.query,hash:_.hash,params:"path"in M?{}:_.params},M)}}function ee(_,R){const C=u=I(_),M=c.value,Z=_.state,d=_.force,p=_.replace===!0,m=Q(C);if(m)return ee(B(v(m),{state:typeof m=="object"?B({},Z,m.state):Z,force:d,replace:p}),R||C);const y=C;y.redirectedFrom=R;let b;return!d&&qa(i,M,C)&&(b=Ct(16,{to:y,from:M}),Se(M,M,!0,!1)),(b?Promise.resolve(b):Pe(y,M)).catch(k=>Ke(k)?Ke(k,2)?k:Ze(k):$(k,y,M)).then(k=>{if(k){if(Ke(k,2))return ee(B({replace:p},v(k.to),{state:typeof k.to=="object"?B({},Z,k.to.state):Z,force:d}),R||y)}else k=et(y,M,!0,p,Z);return $e(y,M,k),k})}function ne(_,R){const C=L(_,R);return C?Promise.reject(C):Promise.resolve()}function We(_){const R=dt.values().next().value;return R&&typeof R.runWithContext=="function"?R.runWithContext(_):_()}function Pe(_,R){let C;const[M,Z,d]=Xl(_,R);C=Jn(M.reverse(),"beforeRouteLeave",_,R);for(const m of M)m.leaveGuards.forEach(y=>{C.push(Je(y,_,R))});const p=ne.bind(null,_,R);return C.push(p),re(C).then(()=>{C=[];for(const m of s.list())C.push(Je(m,_,R));return C.push(p),re(C)}).then(()=>{C=Jn(Z,"beforeRouteUpdate",_,R);for(const m of Z)m.updateGuards.forEach(y=>{C.push(Je(y,_,R))});return C.push(p),re(C)}).then(()=>{C=[];for(const m of d)if(m.beforeEnter)if(Oe(m.beforeEnter))for(const y of m.beforeEnter)C.push(Je(y,_,R));else C.push(Je(m.beforeEnter,_,R));return C.push(p),re(C)}).then(()=>(_.matched.forEach(m=>m.enterCallbacks={}),C=Jn(d,"beforeRouteEnter",_,R),C.push(p),re(C))).then(()=>{C=[];for(const m of r.list())C.push(Je(m,_,R));return C.push(p),re(C)}).catch(m=>Ke(m,8)?m:Promise.reject(m))}function $e(_,R,C){a.list().forEach(M=>We(()=>M(_,R,C)))}function et(_,R,C,M,Z){const d=L(_,R);if(d)return d;const p=R===Ve,m=gt?history.state:{};C&&(M||p?o.replace(_.fullPath,B({scroll:p&&m&&m.scroll},Z)):o.push(_.fullPath,Z)),c.value=_,Se(_,R,C,p),Ze()}let Ce;function At(){Ce||(Ce=o.listen((_,R,C)=>{if(!tn.listening)return;const M=I(_),Z=Q(M);if(Z){ee(B(Z,{replace:!0}),M).catch($t);return}u=M;const d=c.value;gt&&sl(To(d.fullPath,C.delta),Mn()),Pe(M,d).catch(p=>Ke(p,12)?p:Ke(p,2)?(ee(p.to,M).then(m=>{Ke(m,20)&&!C.delta&&C.type===en.pop&&o.go(-1,!1)}).catch($t),Promise.reject()):(C.delta&&o.go(-C.delta,!1),$(p,M,d))).then(p=>{p=p||et(M,d,!1),p&&(C.delta&&!Ke(p,8)?o.go(-C.delta,!1):C.type===en.pop&&Ke(p,20)&&o.go(-1,!1)),$e(M,d,p)}).catch($t)}))}let ut=zt(),ie=zt(),X;function $(_,R,C){Ze(_);const M=ie.list();return M.length?M.forEach(Z=>Z(_,R,C)):console.error(_),Promise.reject(_)}function Fe(){return X&&c.value!==Ve?Promise.resolve():new Promise((_,R)=>{ut.add([_,R])})}function Ze(_){return X||(X=!_,At(),ut.list().forEach(([R,C])=>_?C(_):R()),ut.reset()),_}function Se(_,R,C,M){const{scrollBehavior:Z}=e;if(!gt||!Z)return Promise.resolve();const d=!C&&rl(To(_.fullPath,0))||(M||!C)&&history.state&&history.state.scroll||null;return ys().then(()=>Z(_,R,d)).then(p=>p&&ol(p)).catch(p=>$(p,_,R))}const de=_=>o.go(_);let ft;const dt=new Set,tn={currentRoute:c,listening:!0,addRoute:g,removeRoute:w,hasRoute:T,getRoutes:E,resolve:I,options:e,push:A,replace:q,go:de,back:()=>de(-1),forward:()=>de(1),beforeEach:s.add,beforeResolve:r.add,afterEach:a.add,onError:ie.add,isReady:Fe,install(_){const R=this;_.component("RouterLink",Wl),_.component("RouterView",or),_.config.globalProperties.$router=R,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>qe(c)}),gt&&!ft&&c.value===Ve&&(ft=!0,A(o.location).catch(Z=>{}));const C={};for(const Z in Ve)Object.defineProperty(C,Z,{get:()=>c.value[Z],enumerable:!0});_.provide(Ln,R),_.provide(Wi,as(C)),_.provide(di,c);const M=_.unmount;dt.add(_),_.unmount=function(){dt.delete(_),dt.size<1&&(u=Ve,Ce&&Ce(),Ce=null,c.value=Ve,ft=!1,X=!1),M()}}};function re(_){return _.reduce((R,C)=>R.then(()=>We(C)),Promise.resolve())}return tn}function Xl(e,t){const n=[],i=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let r=0;r<s;r++){const a=t.matched[r];a&&(e.matched.find(u=>Pt(u,a))?i.push(a):n.push(a));const c=e.matched[r];c&&(t.matched.find(u=>Pt(u,c))||o.push(c))}return[n,i,o]}function Jl(){return we(Ln)}function Yl(){return we(Wi)}const ql=jt({__name:"App",setup(e){return(t,n)=>(Tn(),$c(qe(or)))}});const Gl=jn("h1",null,"Loading...",-1),Ql=jt({__name:"Auth0CallbackView",setup(e){const{isAuthenticated:t}=Vs(),n=Jl();return Dt(t,i=>{i&&n.push("/")}),(i,o)=>(Tn(),ji(je,null,[Gl,jn("p",null,'',1)],64))}}),$o=jt({__name:"HomeView",setup(e){const t=navigator.language,n={en:"en","en-US":"en"},i=Yl(),{pathMatch:o}=i.params;let s="";return Array.isArray(o)?s=o.join("/"):o&&(s=o),Object.keys(n).filter(r=>r.toLowerCase()===t.toLowerCase()).length===0?window.location.href=`${window.location.origin}/en/index.html#/`+s:window.location.href=`${window.location.origin}/${n[t]}/index.html#/`+s,(r,a)=>(Tn(),ji("p",null,"Loading..."))}}),eu=jt({__name:"LoginView",setup(e){const{loginWithRedirect:t}=Vs();return t(),(n,i)=>(Tn(),ji("p",null,"One moment..."))}}),tu=Vl({history:ul("/"),routes:[{path:"/",name:"home",component:$o},{path:"/auth0-callback",name:"auth0-callback",component:Ql},{path:"/login",name:"login",component:eu},{path:"/:pathMatch(.*)*",name:"catch-all",component:$o}]}),hi=Ia(ql);hi.use(tu);async function nu(){const e=location.origin+(location.origin.indexOf(":5173")!==-1?"/src/assets":"/en/assets/data")+"/appConfig.json";return await(await fetch(e)).json()}const iu=async()=>{try{const e=await nu();hi.use(Ba({...e.auth0,authorizationParams:{...e.auth0.authorizationParams,redirect_uri:window.location.origin+"/auth0-callback"},cacheLocation:"localstorage"}))}catch(e){console.error(e)}hi.mount("#app")};iu().then(()=>console.info("app mounted"));
