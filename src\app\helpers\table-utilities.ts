import { RowClassParams } from 'ag-grid-community';

export class TableUtilities {
  static getRowStripedStyle(params: RowClassParams) {
    if (params.node?.rowIndex !== null && params.node.rowIndex % 2 !== 0) {
      return {
        background: 'var(--grey-40)',
        borderBottom: '1px solid var(--grey-60)',
        borderTop: '1px solid var(--grey-60)',
      };
    }

    return {
      background: 'white',
      borderBottom: 'none',
      borderTop: 'none',
    };
  }
}
