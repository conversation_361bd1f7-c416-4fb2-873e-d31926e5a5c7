import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { ChangeDetectorRef, Component, ElementRef, TemplateRef, ViewChild } from '@angular/core';
import { Observable, filter, map } from 'rxjs';
import { DialogService } from '../services/dialog.service';
import { EMPTY } from '../constant';
import { EventAbsComponent } from '../services/event-communicate/component-evt-base';
import { EventParam } from '../models/event-items';
import { EventUpdateAppInitStatus } from '../models/event-params';
import { LiveNotifyContent } from '../services/message-management.service';
import { LoadingService } from '../services/loading.service';
import { MatDialogConfig } from '@angular/material/dialog';
import { RealTimeNotifyService } from '../services/realtime-notify.service';
import { SecurityService } from '../services/security.service';
import { User } from '../interfaces/user';
import { UserService } from '../services/user.service';

@Component({
  selector: 'app-bootstrap',
  templateUrl: './bootstrap.component.html',
  styleUrls: ['./bootstrap.component.scss'],
})
export class BootstrapComponent extends EventAbsComponent {
  windowWidth = EMPTY;
  showSplash = true;
  termsAndConditions!: string;
  dialogConfig!: MatDialogConfig;

  ingnoredRoutes = ['access-denied'];
  skipTermsAndConditions = true;

  @ViewChild('dialogContent') dialogContent: TemplateRef<unknown> | undefined;
  @ViewChild('termsConditionsContent') termsConditionsContent: ElementRef | undefined;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly loadingService: LoadingService,
    private readonly dialogService: DialogService,
    private readonly userService: UserService,
    private readonly security: SecurityService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly realtimeNotifyService: RealTimeNotifyService<LiveNotifyContent>,
  ) {
    super();
    this.loadingService.forceDisplayLoading(false);
  }

  override onInit(): void {
    this.subscribeEvent(EventUpdateAppInitStatus.CLASS_NAME);

    this.realtimeNotifyService.startConnection();

    this.getCurrentRoute().subscribe((isIgnoredRoute) => {
      if (this.showSplash) {
        this.showSplash = !isIgnoredRoute;
      }
    });
  }

  override onDestroy(): void {
    this.loadingService.forceDisplayLoading(true);
    this.windowWidth = '-' + window.innerWidth + 'px';
    this.showSplash = false;

    this.realtimeNotifyService.stopConnection();
  }

  override onEvent(params: EventParam): void {
    params.objs.forEach((obj) => {
      if (obj instanceof EventUpdateAppInitStatus && !obj.loadingStatus) {
        this.handleAppInitStatus(obj);
      }
    });
  }

  public getCurrentRoute(): Observable<boolean> {
    return this.router.events.pipe(
      filter((event) => event instanceof NavigationEnd),
      map(() => {
        const currentRoute = this.activatedRoute.snapshot.firstChild?.routeConfig?.path;

        return this.ingnoredRoutes.includes(currentRoute ?? EMPTY);
      }),
    );
  }

  private handleAppInitStatus(obj: EventUpdateAppInitStatus): void {
    this.initializeUser().subscribe((user) => {
      if ((user.isAcceptedTermsConditions ?? !user.termsConditions) || this.skipTermsAndConditions) {
        this.showSplash = false;

        this.startUserSession(obj.loadingStatus);
      } else {
        this.showTermsAndConditions(user.termsConditions.termsConditionsContent);
      }
    });
  }

  private startUserSession(loadingStatus: boolean): void {
    this.loadingService.forceDisplayLoading(true);
    this.windowWidth = '-' + window.innerWidth + 'px';
    this.showSplash = loadingStatus;
    this.cdr.detectChanges();
  }

  private showTermsAndConditions(content: string): void {
    this.termsAndConditions = content;
    this.openTermsConditionsDialog();
  }

  private initializeUser(): Observable<User> {
    return this.userService.getUser().pipe(
      map((user) => ({
        ...user,
      })),
    );
  }

  updateTermsAndConditionsAcceptance(): void {
    this.userService.updateUserTermsAndConditionsAcceptance().subscribe(() => {
      this.termsAndConditions = EMPTY;
      this.dialogService.closeDialog$.next(undefined);
      this.windowWidth = '-' + window.innerWidth + 'px';
      this.showSplash = false;
    });
  }

  async logOut(): Promise<void> {
    await this.security.logout();
  }

  public handleScrollOnTermsConditions(): void {
    const primaryActionIndex = 1;
    const termsElement = this.termsConditionsContent?.nativeElement;
    let lastScrollTop = 0;
    const resolutionFixedValue = 50;

    const enableAgreeButton = () => {
      this.dialogConfig.data.actions[primaryActionIndex].disable = false;
      this.dialogService.updateDialogData(this.dialogConfig.data);
    };

    if (!termsElement || termsElement.scrollHeight <= termsElement.clientHeight) {
      enableAgreeButton();

      return;
    }

    termsElement.onscroll = () => {
      if (termsElement.scrollTop < lastScrollTop) {
        return;
      }

      lastScrollTop = termsElement.scrollTop <= 0 ? 0 : termsElement.scrollTop;

      if (termsElement.scrollTop + termsElement.offsetHeight >= termsElement.scrollHeight - resolutionFixedValue) {
        enableAgreeButton();
      }
    };
  }

  private openTermsConditionsDialog(): void {
    this.dialogConfig = {
      data: {
        title: $localize`:title:Terms & Conditions`,
        content: this.dialogContent,
        actions: [
          {
            label: $localize`:label:Cancel`,
            skipPermission: true,
            onClick: () => this.logOut(),
          },
          {
            label: $localize`:label:Agree`,
            color: 'primary',
            disable: true,
            skipPermission: true,
            onClick: () => this.updateTermsAndConditionsAcceptance(),
          },
        ],
      },
      disableClose: true,
    };
    this.dialogService.openDialog$.next(this.dialogConfig);
    setTimeout(() => {
      this.handleScrollOnTermsConditions();
    });
  }
}
