import moment from 'moment';

import { EMPTY } from '../constant';

export class DateTimeUtilities {
  static formatDate(dateString: string, format = 'MM/DD/YYYY HH:mm:ss'): string {
    const date = moment(dateString);

    if (!date.isValid()) {
      return 'Invalid Date';
    }

    return date.format(format);
  }

  static addDays(date: Date, numberOfDays: number): Date {
    return moment(date).add(numberOfDays, 'days').toDate();
  }

  static addMonths(date: Date, numberOfMonths: number): Date {
    return moment(date).add(numberOfMonths, 'months').toDate();
  }

  static addYears(date: Date, numberOfYears: number): Date {
    return moment(date).add(numberOfYears, 'years').toDate();
  }

  static getDateWithTimezone(date: Date): string {
    const dateWithTimezone = moment(date).format('YYYY-MM-DDTHH:mm:ss');

    return `${dateWithTimezone}${this.getLocalTimezoneOffset()}`;
  }

  static getLocalTimezoneOffset() {
    const offset = new Date().getTimezoneOffset();
    const sign = offset > 0 ? '-' : '+';
    const hours = Math.floor(Math.abs(offset) / 60)
      .toString()
      .padStart(2, '0');
    const minutes = (Math.abs(offset) % 60).toString().padStart(2, '0');

    return `${sign}${hours}:${minutes}`;
  }

  static convertDateRangeToUTC(
    startDateString: string,
    endDateString: string
  ): {
    startDate: string;
    endDate: string;
  } {
    return {
      startDate: this.convertStartDateToUTC(startDateString),
      endDate: this.convertEndDateToUTC(endDateString),
    };
  }

  static convertStartDateToUTC(startDateString: string): string {
    const startDate = new Date(startDateString);

    if (!startDateString) {
      return EMPTY;
    }

    if (isNaN(startDate.getTime())) {
      return EMPTY;
    }

    return new Date(
      startDate.getFullYear(), startDate.getMonth(), startDate.getDate()
    ).toISOString();
  }

  static convertEndDateToUTC(endDateString: string): string {
    const endDate = new Date(endDateString);
    const now = new Date();

    if (!endDateString) {
      return EMPTY;
    }

    if (isNaN(endDate.getTime())) {
      return EMPTY;
    }

    let convertedEndDate: string;

    if (
      endDate.getFullYear() === now.getFullYear() &&
      endDate.getMonth() === now.getMonth() &&
      endDate.getDate() === now.getDate()
    ) {
      // If endDate is today, add 1 second to the current time
      const currentTimePlusOneSecond = new Date(now.getTime() + 1000);

      convertedEndDate = currentTimePlusOneSecond.toISOString();
    } else {
      // If endDate is not today, set to start of the next day in UTC
      const nextDay = new Date(
        endDate.getFullYear(),
        endDate.getMonth(),
        endDate.getDate() + 1
      );

      convertedEndDate = nextDay.toISOString();
    }

    return convertedEndDate;
  }

  /**
   * Converts a UTC date string to a specific timezone offset.
   * @param utcDateString Date string in UTC (e.g., '2025-05-31T05:40:00Z')
   * @param timezoneOffset Timezone offset in format '+HH:mm' or '-HH:mm' (e.g., '+02:00')
   * @returns Date string in the target timezone, formatted as 'YYYY-MM-DDTHH:mm:ss'
   */
  static convertUTCToSpecificTimezone(utcDateString: string, timezoneOffset: string): string {
    if (!utcDateString || !timezoneOffset) {
      return utcDateString;
    }

    // Remove 'UTC' prefix if present
    const tz = timezoneOffset.replace(/^UTC/, '');

    // Remove 'Z' if present to avoid double conversion
    const cleanUtc = utcDateString.endsWith('Z')
      ? utcDateString.slice(0, -1)
      : utcDateString;

    // Parse as UTC, then add the offset
    const date = moment.utc(cleanUtc).utcOffset(tz);

    return date.format('YYYY-MM-DDTHH:mm:ss');
  }
}
