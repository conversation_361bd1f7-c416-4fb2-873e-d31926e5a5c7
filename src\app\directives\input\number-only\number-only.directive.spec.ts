import { ElementRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { TestBed } from '@angular/core/testing';

import { EMPTY } from 'src/app/constant';
import { NumbersOnlyDirective } from './number-only.directive';

describe('NumbersOnlyDirective', () => {
  let directive: NumbersOnlyDirective;
  let mockElementRef: ElementRef;
  let mockNgControl: NgControl;

  beforeEach(() => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    mockNgControl = { control: { patchValue: jest.fn() } } as unknown as NgControl;
    mockElementRef = { nativeElement: { attributes: {} } };

    TestBed.configureTestingModule({
      providers: [
        NumbersOnlyDirective,
        {
          provide: NgControl,
          useValue: mockNgControl,
        },
        {
          provide: ElementRef,
          useValue: mockElementRef,
        },
      ],
    });

    directive = TestBed.inject(NumbersOnlyDirective);
  });

  it('should create an instance', () => {
    expect(directive).toBeTruthy();
  });

  it('should validate pasted value', () => {
    const pastedValue = '456';

    jest.spyOn(directive, 'validateValueOnPaste');

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const event = {
      clipboardData: { getData: () => pastedValue },
      preventDefault: jest.fn(),
    } as unknown as ClipboardEvent;

    directive.onPaste(event);

    expect(directive.validateValueOnPaste).toHaveBeenCalledWith(pastedValue);
  });

  it('should allow valid keys on keydown event', () => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const event = {
      key: '1',
      target: {
        selectionStart: 0,
        value: '123',
      },
      ctrlKey: false,
      metaKey: false,
      preventDefault: jest.fn(),
    } as unknown as KeyboardEvent;

    directive.onKeyDown(event);

    expect(event.preventDefault).not.toHaveBeenCalled();
  });

  it('should validate values correctly based on input settings', () => {
    const testCases = [
      {
        value: '123',
        allowDecimals: false,
        allowSign: false,
        expected: '123',
      },
      {
        value: '123.45',
        allowDecimals: true,
        allowSign: false,
        expected: '123.45',
      },
      {
        value: '-123',
        allowDecimals: false,
        allowSign: true,
        expected: '-123',
      },
      {
        value: '123.',
        allowDecimals: true,
        allowSign: false,
        expected: '123.0',
      },
    ];

    testCases.forEach(({ value, allowDecimals, allowSign, expected }) => {
      directive.allowDecimals = allowDecimals;

      directive.allowSign = allowSign;

      jest.spyOn(directive, 'removeThousandSeparator').mockReturnValue(value);

      jest.spyOn(directive, 'isNumberValue').mockReturnValue({
        valid: true,
        value: expected,
      });

      directive.validateValue(value);

      expect(mockNgControl.control!.patchValue).toHaveBeenCalledWith(expected);
    });
  });

  it('should validate number values correctly', () => {
    // Test case for valid unsigned integer
    const resultUnsignedInt = directive.isNumberValue('123', false, false);

    expect(resultUnsignedInt).toEqual({
      valid: true,
      value: '123',
    });

    // Test case for valid signed integer
    const resultSignedInt = directive.isNumberValue('-123', false, true);

    expect(resultSignedInt).toEqual({
      valid: true,
      value: '-123',
    });

    // Test case for valid unsigned decimal
    const resultUnsignedDecimal = directive.isNumberValue('123.45', true, false);

    expect(resultUnsignedDecimal).toEqual({
      valid: true,
      value: '123.45',
    });

    // Test case for valid signed decimal
    const resultSignedDecimal = directive.isNumberValue('-123.45', true, true);

    expect(resultSignedDecimal).toEqual({
      valid: true,
      value: '-123.45',
    });

    // Test case for invalid number with non-numeric characters
    const resultInvalidWithChars = directive.isNumberValue('123a.45', true, false);

    expect(resultInvalidWithChars).toEqual({
      valid: false,
      value: EMPTY,
    });

    // Test case for number with incorrect decimal separator
    const resultInvalidWithComma = directive.isNumberValue('123,45', true, false);

    expect(resultInvalidWithComma).toEqual({
      valid: true,
      value: '123,45',
    });

    // Test case for number starting with decimal separator
    const resultStartsWithDecimal = directive.isNumberValue('.123', true, false);

    expect(resultStartsWithDecimal).toEqual({
      valid: true,
      value: '0.123',
    });

    // Test case for number ending with decimal separator
    const resultEndsWithDecimal = directive.isNumberValue('123.', true, false);

    expect(resultEndsWithDecimal).toEqual({
      valid: true,
      value: '123.0',
    });

    // Test case for empty string
    const resultEmptyString = directive.isNumberValue('', true, true);

    expect(resultEmptyString).toEqual({
      valid: false,
      value: EMPTY,
    });

    // Test case for value with multiple decimal separators (invalid)
    const resultMultipleDecimals = directive.isNumberValue('123.45.67', true, false);

    expect(resultMultipleDecimals).toEqual({
      valid: false,
      value: EMPTY,
    });
  });

  it('should validate value on paste correctly', () => {
    directive.allowNull = true;
    directive.allowDecimals = false;
    directive.allowSign = false;

    jest.spyOn(directive, 'isNumberValue').mockReturnValue({
      valid: false,
      value: EMPTY,
    });

    directive.validateValueOnPaste(EMPTY);

    expect(mockNgControl.control!.patchValue).toHaveBeenCalledWith(EMPTY);
  });

  it('should remove thousand separators correctly', () => {
    expect(directive.removeThousandSeparator('1.000.000', '.')).toBe('1000000');
  });
});
