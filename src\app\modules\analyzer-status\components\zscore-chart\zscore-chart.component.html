@if (hasData) {
  <div class="flex chart flex-wrap xl:flex-nowrap">
    <div class="w-full flex chart-item">
      <div class="flex-none w-14 content-center scale-slider-toggle">
        <mat-slider
          class="-rotate-90 -translate-x"
          id="chart-Vslider-{{ parametersDateRange.parameterCode }}"
          [min]="1"
          [max]="6"
          [step]="1"
          (change)="updateYAxisScaleChart(scaleYValue)"
        >
          <input matSliderThumb [(ngModel)]="scaleYValue" />
        </mat-slider>
      </div>
      <div class="flex-1 w-64 content-center">
        <div class="absolute mt-[17px] z-10">
          <p
            class="font-bold"
            [innerHtml]="parametersDateRange.parameterDisplayName + ' (' + parametersDateRange.unitDisplay + ')'"
          ></p>
        </div>
        @for (chart of charts; track chart) {
          <div [chart]="chart" id="chart-{{ parametersDateRange.parameterCode }}"></div>
        }
      </div>
    </div>
    @if (toggleInteract.hasStatisticTables) {
      <div class="w-full xl:w-auto flex justify-end advanced-lj-chart-table-item">
        <div class="w-1/2 xl:w-full flex justify-end">
          <app-statistic-table
            [statisticTableHeaderConfig]="statisticHeaderConfig"
            [statisticDataConfig]="statisticDataConfig"
            [statisticTableData]="statisticTableData"
            [breakPointConfig]="breakPointConfig"
          ></app-statistic-table>
        </div>
      </div>
    }
  </div>
}

<app-advanced-lj-comment-dialog
  [openDialog]="openCommentDialog$ | async"
  [isDisplayManageTab]="true"
></app-advanced-lj-comment-dialog>

<app-reagent-dialog [openDialog]="chartsEventService.openReagentDialog$ | async"></app-reagent-dialog>

<app-service-histories-dialog
  [openDialog]="chartsEventService.openServiceHistoriesDialog$ | async"
></app-service-histories-dialog>
