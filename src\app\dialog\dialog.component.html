<ng-container>
  <div class="flex justify-between items-center {{ data.headerClass }}">
    <h2 mat-dialog-title class="text-[var(--grey-200)] font-bold">{{ data.title }}</h2>
    @if (data.showCloseIcon) {
      <button
        [disabled]="data.isDisableClose"
        [ngClass]="{ 'opacity-50': data.isDisableClose }"
        mat-icon-button
        [mat-dialog-close]="true"
        class="mr-2"
        (click)="data.onCloseIconClick ? data.onCloseIconClick() : true"
      >
        <mat-icon svgIcon="close" color="primary"></mat-icon>
      </button>
    }
  </div>
  <mat-dialog-content class="mat-typography">
    <ng-container *ngTemplateOutlet="data.content; context: messages.permission"></ng-container>
  </mat-dialog-content>
  @if (data.actions.length) {
    <ng-container [appAclWrapper]="parentPageCode">
      <mat-dialog-actions [align]="data.align || 'end'" class="!mb-4" [appAcl]="pageCode" #acl="AppAcl">
        <ng-container *ngFor="let action of data.actions">
          @if ((acl.permission$ | async)?.write || action.skipPermission) {
            @if (action.className) {
              <button
                mat-raised-button
                [className]="action.className"
                [class]="action.class"
                [color]="action.color"
                (click)="action.onClick ? action.onClick() : true"
                [disabled]="action.disable"
              >
                {{ action.label }}
              </button>
            } @else {
              <button
                mat-raised-button
                [class]="action.class"
                [color]="action.color"
                (click)="action.onClick ? action.onClick() : true"
                [disabled]="action.disable"
              >
                {{ action.label }}
              </button>
            }
          }
        </ng-container>
      </mat-dialog-actions>
    </ng-container>
  }
</ng-container>
