import { Directive, EventEmitter, HostBinding, HostListener, Output } from '@angular/core';

@Directive({
  selector: '[appDropZone]',
  standalone: true,
})
export class DropZoneDirective {
  styleConfig = {
    defaultBackgroundColor: '#FFFFFF',
    onDragOverBackgroundColor: '#EFEFEF',
  };
  @Output() fileDrop = new EventEmitter<File[]>();

  @HostBinding('style.background') private background = this.styleConfig.defaultBackgroundColor;

  @HostListener('dragover', ['$event']) public onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();

    this.background = this.styleConfig.onDragOverBackgroundColor;
  }

  @HostListener('dragleave', ['$event']) public onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();

    this.background = this.styleConfig.defaultBackgroundColor;
  }

  @HostListener('drop', ['$event']) public onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();

    this.background = this.styleConfig.defaultBackgroundColor;

    const files: File[] = [];

    if (event.dataTransfer) {
      const fileList = event.dataTransfer.files;
      const filesArray = Array.from(fileList);

      for (const file of filesArray) {
        files.push(file);
      }
    }

    if (files.length > 0) {
      this.fileDrop.emit(files);
    }
  }
}
