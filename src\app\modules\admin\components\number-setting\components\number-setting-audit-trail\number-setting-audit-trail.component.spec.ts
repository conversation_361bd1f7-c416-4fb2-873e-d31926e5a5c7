import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogService } from 'src/app/services/dialog.service';
import { NumberSettingAuditTrailComponent } from './number-setting-audit-trail.component';
import { RuleConfigurationService } from 'src/app/modules/admin/services/rule-configuration.service';

describe('NumberSettingAuditTrailComponent', () => {
  let component: NumberSettingAuditTrailComponent;
  let fixture: ComponentFixture<NumberSettingAuditTrailComponent>;

  const mockRuleConfigurationService = {
    getRuleConfigurationAuditTrailLog: jest.fn(),
  };

  const mockDialogService = {
    openDialog$: { next: jest.fn() },
    dialogRef: { afterClosed: jest.fn() },
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        {
          provide: RuleConfigurationService,
          useValue: mockRuleConfigurationService,
        },
        {
          provide: DialogService,
          useValue: mockDialogService,
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(NumberSettingAuditTrailComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('switch statement for ruleCode', () => {
    it('should set dialogTitle and columnDefs for rule6', () => {
      component.ruleCode = 'rule6';
      component.sharedColumnDefs = [
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
      ];

      component.componentOnInit();

      expect(component.dialogTitle).toBe('Number of Consecutive Troubleshooting Events Log');
      expect(component.columnDefs).toEqual([
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
        {
          field: 'consecutive_troubleshooting_events',
          headerName: 'Number of Consecutive Troubleshooting Events',
          cellClass: 'text-right',
          minWidth: 320,
        },
      ]);
    });

    it('should set dialogTitle and columnDefs for rule7', () => {
      component.ruleCode = 'rule7';
      component.sharedColumnDefs = [
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
      ];

      component.componentOnInit();

      expect(component.dialogTitle).toBe('Number of Troubleshooting Events & Consecutive Control Run Log');
      expect(component.columnDefs).toEqual([
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
        {
          field: 'troubleshooting_events',
          headerName: 'Number of Troubleshooting Events',
          cellClass: 'text-right',
          minWidth: 240,
          headerTooltip: 'Number of Troubleshooting Events',
        },
        {
          field: 'consecutive_control_run_sets',
          headerName: 'Number of Consecutive Control Runs',
          cellClass: 'text-right',
          minWidth: 250,
          headerTooltip: 'Number of Consecutive Control Runs',
        },
      ]);
    });

    it('should set dialogTitle and columnDefs for rule8', () => {
      component.ruleCode = 'rule8';
      component.sharedColumnDefs = [
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
      ];

      component.componentOnInit();

      expect(component.dialogTitle).toBe('Number of Troubleshooting Events & Consecutive Days Log');
      expect(component.columnDefs).toEqual([
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
        {
          field: 'troubleshooting_events',
          headerName: 'Number of Troubleshooting Events',
          cellClass: 'text-right',
          minWidth: 300,
        },
        {
          field: 'consecutive_days',
          headerName: 'Number of Consecutive Days',
          cellClass: 'text-right',
          minWidth: 250,
        },
      ]);
    });

    it('should set dialogTitle and columnDefs for rule9', () => {
      component.ruleCode = 'rule9';
      component.sharedColumnDefs = [
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
      ];

      component.componentOnInit();

      expect(component.dialogTitle).toBe('Number of Recent Runs & Difference Allowable Log');
      expect(component.columnDefs).toEqual([
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
        {
          field: 'recent_runs',
          headerName: 'Number of Recent Runs',
          cellClass: 'text-right',
          minWidth: 200,
        },
        {
          field: 'difference_allowable_percentage',
          headerName: 'Difference Allowable (%)',
          cellClass: 'text-right',
          minWidth: 200,
        },
      ]);
    });

    it('should set dialogTitle and columnDefs for rule13', () => {
      component.ruleCode = 'rule13';
      component.sharedColumnDefs = [
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
      ];

      component.componentOnInit();

      expect(component.dialogTitle).toBe('Number of Recent Runs Log');
      expect(component.columnDefs).toEqual([
        {
          field: 'shared',
          headerName: 'Shared Column',
        },
        {
          field: 'recent_runs',
          headerName: 'Number of Recent Runs',
          cellClass: 'text-right',
          minWidth: 200,
        },
      ]);
    });
  });
});
