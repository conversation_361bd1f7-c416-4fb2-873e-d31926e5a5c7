import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';
import { Directive, EventEmitter, Input, OnDestroy, Output } from '@angular/core';

import { CellValueChangedEvent } from 'ag-grid-community';

import { EditableInputCellParams, NumberSettingInput } from '../interfaces/number-setting';
import { EMPTY } from 'src/app/constant';
import { NumberInputConstraints } from '../interfaces/ag-grid-editable-cell';
import { RULES } from '../constants';
import { Utils } from 'src/app/helpers/UtilFunctions';

@Directive({
  selector: '[appRuleConfigGridInputValidator]',
  standalone: true,
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: RuleConfigGridInputValidator,
      multi: true,
    },
  ],
})
export class RuleConfigGridInputValidator implements Validator, OnDestroy {
  @Input() constraints: NumberInputConstraints = { allowDecimals: false };

  @Output() isValid = new EventEmitter<boolean>();

  @Input() lowerLimit?: number;

  @Input() upperLimit?: number;

  @Input() rowIndex = -1;

  @Input() cocParameterCode?: string;

  @Input() editingField: string | undefined;

  @Input() ruleCode: string = EMPTY;

  @Input() inputValues!: NumberSettingInput[];

  @Input() required = true;

  @Input() set params(params: EditableInputCellParams) {
    if (this.paramsValue) {
      this.paramsValue.api.removeEventListener('cellValueChanged', this.eventListener);
    }

    this.paramsValue = params;

    this.eventListener = this.onCellValueChanged.bind(this);

    if (this.paramsValue?.api) {
      this.paramsValue.api.addEventListener('cellValueChanged', this.eventListener);
    }
  }

  paramsValue: EditableInputCellParams | undefined;

  lowerLimitValue: number | undefined;

  upperLimitValue: number | undefined;

  absControl: AbstractControl | undefined;

  rowIndexValue = -1;

  private eventListener!: (event: CellValueChangedEvent<unknown, unknown>) => void;

  validate(control: AbstractControl): ValidationErrors | null {
    if (!this.absControl) {
      this.absControl = control;
    }

    if (!control.touched || !control.dirty) {
      return null;
    }

    const value = control.value;

    const error = this.checkInvalid(value);

    this.isValid.emit(Utils.isNull(error));

    return error;
  }

  onCellValueChanged(event: CellValueChangedEvent): void {
    const rowNode = event.node;

    this.rowIndexValue = rowNode.rowIndex!;

    this.lowerLimitValue = parseFloat(this.paramsValue?.node.data.lowerLimit);

    this.upperLimitValue = parseFloat(this.paramsValue?.node.data.upperLimit);

    this.absControl?.markAsTouched();

    this.absControl?.markAsDirty();

    this.absControl?.updateValueAndValidity();
  }

  checkInvalid(value: string): ValidationErrors | null {
    if (!this.constraints) {
      return null;
    }

    // To handle case: 0 or '0', we cannot use condition !value here, because !0 will be true
    if (value === null || value === undefined || value === EMPTY) {
      return this.required ? { required: true } : null;
    }

    const numberValue = Number(value);

    if (this.isRangeError(numberValue)) {
      return { rangeError: true };
    }

    if (isNaN(numberValue) || Utils.isExceedDecimals(numberValue, this.constraints.maxDecimals)) {
      return { invalidFormat: true };
    }

    if (this.cocParameterCode && this.lowerLimitValue! >= this.upperLimitValue!) {
      return { lowerUpperCocError: true };
    }

    if (!this.cocParameterCode && this.lowerLimitValue! >= this.upperLimitValue!) {
      return { lowerUpperError: true };
    }

    const numberSettingsCrossFieldError = this.numberSettingsCrossFieldValidate();

    if (numberSettingsCrossFieldError) {
      return numberSettingsCrossFieldError;
    }

    return null;
  }

  isRangeError(value: number): boolean {
    const minValue = this.constraints.min ?? -Infinity;

    const maxValue = this.constraints.max ?? Infinity;

    const minCondition = !this.constraints.minIncluded ? value <= minValue : value < minValue;

    const maxCondition = !this.constraints.maxIncluded ? value >= maxValue : value > maxValue;

    return minCondition || maxCondition;
  }

  numberSettingsCrossFieldValidate(): ValidationErrors | null {
    if (this.ruleCode === RULES.rule7) {
      if (Number(this.inputValues?.[0]?.value) > Number(this.inputValues?.[1]?.value)) {
        return { rule7CrossFieldError: true };
      }
    }

    return null;
  }

  ngOnDestroy() {
    if (this.paramsValue?.api) {
      this.paramsValue.api.removeEventListener('cellValueChanged', this.eventListener);
    }
  }
}
