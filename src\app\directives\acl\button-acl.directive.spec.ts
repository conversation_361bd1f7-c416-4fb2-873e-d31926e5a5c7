import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DEFAULT_PERMISSION, FIRST_INDEX } from 'src/app/constant';
import { ActivatedRoute } from '@angular/router';
import { By } from '@angular/platform-browser';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of } from 'rxjs';

import { AclModule } from './acl.module';
import { AclWrapperDirective } from './acl-wrapper.directive';
import { ButtonAclDirective } from './button-acl.directive';
import { MOCK_PMS_RESULTS } from './mock/permission-response-mock';
import { UserService } from 'src/app/services/user.service';
import { UserStoreService } from 'src/app/services/user-store.service';

@Component({
  template: `
    <div appAclWrapper="">
      <div appAcl=""></div>
    </div>
  `,
})
export class EmptyParentComponent {}

@Component({
  template: `
    <div appAclWrapper="Analyzer Status">
      <div appAcl="Summary"></div>
    </div>
  `,
})
export class AnalyzerStatusParentComponent {}

describe('ButtonAclDirective', () => {
  let component: EmptyParentComponent;

  let elementsAclWrapper: DebugElement[] = [];

  let elementsButtonAcl: DebugElement[] = [];

  let mockUserService: jest.Mocked<UserService>;

  beforeEach(async () => {
    const userServiceMock = {
      getUser: jest.fn().mockReturnValue(of({ navigatorItems: MOCK_PMS_RESULTS })),
    };

    const activatedRouteMock = {
      params: of({ id: '123' }),
      queryParams: of({ search: 'test' }),
      data: of({ pagecode: '123' }),
    };

    await TestBed.configureTestingModule({
      providers: [
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: UserStoreService,
          useValue: userServiceMock,
        },
        {
          provide: ActivatedRoute,
          useValue: activatedRouteMock,
        },
      ],
      declarations: [EmptyParentComponent, AnalyzerStatusParentComponent, AclWrapperDirective, ButtonAclDirective],
      imports: [HttpClientTestingModule, AclModule],
    }).compileComponents();

    const fixture: ComponentFixture<EmptyParentComponent> = TestBed.createComponent(EmptyParentComponent);

    component = fixture.componentInstance;

    elementsAclWrapper = fixture.debugElement.queryAll(By.directive(AclWrapperDirective));

    elementsButtonAcl = fixture.debugElement.queryAll(By.directive(ButtonAclDirective));

    fixture.detectChanges();
  });

  it('should get empty parent', () => {
    const aclWrapperDirective = elementsAclWrapper[FIRST_INDEX].injector.get(AclWrapperDirective);

    expect(aclWrapperDirective.parentCode).toBeUndefined();

    const response = aclWrapperDirective.getGroupPages(aclWrapperDirective.parentCode);

    expect(response).toEqual([]);
  });

  it('should get analyzer status parent', () => {
    const aclWrapperDirective = elementsAclWrapper[FIRST_INDEX].injector.get(AclWrapperDirective);

    expect(aclWrapperDirective).toBeDefined();

    const response = aclWrapperDirective.getGroupPages(aclWrapperDirective.parentCode);

    expect(response).toBeDefined();
  });

  it('should get user has permissions at Analyzer Status page', () => {
    const hasPermissions = {
      read: true,
      write: false,
    };

    const buttonAclDirective = elementsButtonAcl[FIRST_INDEX].injector.get(ButtonAclDirective);

    const aclWrapperDirective = elementsAclWrapper[FIRST_INDEX].injector.get(AclWrapperDirective);

    const response = aclWrapperDirective.getPermissionByProperty(buttonAclDirective.aclProperty);

    expect(response).toEqual(hasPermissions);
  });

  it('should get user no permissions', () => {
    const noPermissions = {
      read: DEFAULT_PERMISSION.read,
      write: DEFAULT_PERMISSION.write,
    };

    const buttonAclDirective = elementsButtonAcl[FIRST_INDEX].injector.get(ButtonAclDirective);

    const aclWrapperDirective = elementsAclWrapper[FIRST_INDEX].injector.get(AclWrapperDirective);

    const response = aclWrapperDirective.getPermissionByProperty(buttonAclDirective.aclProperty);

    expect(response).toEqual(noPermissions);
  });
});
