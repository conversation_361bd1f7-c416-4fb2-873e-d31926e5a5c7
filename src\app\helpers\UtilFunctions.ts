/* eslint-disable @typescript-eslint/consistent-type-assertions */
import {
  AssayLimit,
  CustomerLimit,
  TableAssayLimit,
  TableCustomerLimit,
} from '../modules/settings/interfaces/customer-limits';
import { CHART_LEVELS_COLOR, EMPTY } from '../constant';
import { LimitDataPoint, QcResult } from '../modules/analyzer-status/interfaces/qc-data';
import { POINT_MARKER, SORT_DIRECTION } from '../enums';

import { CHART_MODE, ZERO_VALUE } from '../modules/analyzer-status/constants';
import { SortCondition } from '../interfaces/sortCondition';
import { UserStoreService } from '../services/user-store.service';
import { XbarmResult } from '../modules/analyzer-status/interfaces/xbarm';
import moment from 'moment';

import { GlobalVariables } from './constant/global-variable';

type StatisticType = 'mean' | 'sd' | 'cv' | 'biasPercent' | 'sdi' | 'zscore';

export class Utils {
  static isNullOrEmptyString(value: string) {
    return null === value || EMPTY === value || value.length === 0;
  }

  static isNullishNumber(value: number | undefined) {
    return Object.is(value, null) || Object.is(value, undefined);
  }

  static isNumber(value: number) {
    return value === +value;
  }

  static isNull(value: unknown) {
    return Object.is(value, null);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static deepClone(obj: any) {
    // return value is input is not an Object or Array.
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let clone: Record<string, any>;

    if (Array.isArray(obj)) {
      clone = obj.slice(); // unlink Array reference.
    } else {
      clone = Object.assign({}, obj); // Unlink Object reference.
    }

    const keys = Object.keys(clone);

    for (const key of keys) {
      clone[key] = Utils.deepClone(clone[key]); // recursively unlink reference to nested objects.
    }

    return clone; // return unlinked clone.
  }

  static isNullOrEmptyArray<T>(value: T[]) {
    return null == value || (Array.isArray(value) && 0 === value.length);
  }

  static deepArrayEqual(arr1: unknown[], arr2: unknown[]): boolean {
    // Check if lengths are equal
    if (arr1?.length !== arr2?.length) {
      return false;
    }

    // Sort arrays to ensure order-independent comparison
    const compareFunc = (keyA: unknown, keyB: unknown): number => {
      if (typeof keyA === 'string' && typeof keyB === 'string') {
        return keyA.localeCompare(keyB);
      }

      if (typeof keyA === 'number' && typeof keyB === 'number') {
        return keyA - keyB;
      }

      if (typeof keyA === 'object' && typeof keyB === 'object') {
        const strA = JSON.stringify(keyA);
        const strB = JSON.stringify(keyB);

        return strA.localeCompare(strB);
      }

      return 0;
    };

    const sortedArr1 = arr1.slice().sort(compareFunc);
    const sortedArr2 = arr2.slice().sort(compareFunc);

    // Compare each element recursively
    for (let i = 0; i < sortedArr1.length; i++) {
      const obj1 = sortedArr1[i];
      const obj2 = sortedArr2[i];

      // If both elements are objects, recursively compare them
      if (typeof obj1 === 'object' && typeof obj2 === 'object') {
        if (!Utils.deepObjectEqual(obj1, obj2)) {
          return false;
        }
      } else if (obj1 !== obj2) {
        // If elements are not equal, return false
        return false;
      }
    }

    // If all elements are equal, return true
    return true;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static deepObjectEqual(obj1: any, obj2: any): boolean {
    const compareFunc = (keyA: string, keyB: string): number => {
      return keyA.localeCompare(keyB);
    };

    const keys1 = Object.keys(obj1).sort(compareFunc);
    const keys2 = Object.keys(obj2).sort(compareFunc);

    // Check if objects have same keys
    if (!Utils.deepArrayEqual(keys1, keys2)) {
      return false;
    }

    // Compare values for each key
    for (const key of keys1) {
      const val1 = obj1[key];
      const val2 = obj2[key];

      // If both values are objects, recursively compare them
      if (typeof val1 === 'object' && typeof val2 === 'object') {
        if (!Utils.deepObjectEqual(val1, val2)) {
          return false;
        }
      } else if (val1 !== val2) {
        // If values are not equal, return false
        // Treat undefined and null as equivalent
        if (!(val1 == null && val2 == null)) {
          return false;
        }
      }
    }

    // If all key-value pairs are equal, return true
    return true;
  }

  static isArraysEqualOnKeys<T, K>(
    array1: T[],
    array2: K[],
    keys: (keyof T | keyof K)[],
    primaryKey?: string,
  ): boolean {
    if (array1.length !== array2.length) {
      return false;
    }

    array1 = [...array1];
    array2 = [...array2];

    if (primaryKey) {
      array1 = this.sort(array1, [
        {
          prop: primaryKey,
          direction: SORT_DIRECTION.SORT_ASCENDING,
        },
      ]);

      array2 = this.sort(array2, [
        {
          prop: primaryKey,
          direction: SORT_DIRECTION.SORT_ASCENDING,
        },
      ]);
    }

    for (let index = 0; index < array1.length; index++) {
      const item1 = array1[index];
      const item2 = array2[index];

      if (!item2) {
        return false;
      }

      for (const key of keys) {
        const isEqual = this.isObjectsEqualOnKeys(item1, item2, [key]);

        if (!isEqual) {
          return false;
        }
      }
    }

    return true;
  }

  static isObjectsEqualOnKeys<T, K>(obj1: T, obj2: K, keys: (keyof T | keyof K)[]): boolean {
    for (const key of keys) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
      let value1 = obj1 && typeof obj1 === 'object' && key in obj1 ? (obj1 as any)[key] : undefined;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
      let value2 = obj2 && typeof obj2 === 'object' && key in obj2 ? (obj2 as any)[key] : undefined;

      const hasDecimalZero = (value: string) => {
        if (!value?.includes('.')) {
          return false;
        }

        return value.endsWith(ZERO_VALUE.toString());
      };

      value1 = (value1 ?? EMPTY).toString();
      value2 = (value2 ?? EMPTY).toString();

      if (hasDecimalZero(value1)) {
        value1 = Number(value1);
      }

      if (hasDecimalZero(value2)) {
        value2 = Number(value2);
      }

      if (value1.toString() !== value2.toString()) {
        return false;
      }
    }

    return true;
  }

  static sort<T>(data: T[], orderBy: SortCondition[]): T[] {
    orderBy = Array.isArray(orderBy) ? orderBy : [orderBy];

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return data.sort((a: any, b: any) => {
      let i = 0,
        result = 0;

      while (i < orderBy.length && result === 0) {
        const { prop, direction } = orderBy[i];

        const aValue = a[prop] ? a[prop].toString() : '';

        const bValue = b[prop] ? b[prop].toString() : '';

        if (aValue < bValue) {
          result = direction * -1;
        } else if (aValue > bValue) {
          result = direction * 1;
        }

        i++;
      }

      return result;
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static hasNullField(arr: any[]): boolean {
    for (const obj of arr) {
      for (const key in obj) {
        if (obj[key] === null) {
          return true;
        }
      }
    }

    return false;
  }

  static truncateDecimalNumber(value: number | undefined, decimalPoint = 1, nullValueReturn = EMPTY): string {
    if (!value && value !== 0) {
      return nullValueReturn;
    }

    // If decimalPoint is 0, return the integer part of the number
    if (decimalPoint === 0) {
      return Math.trunc(value).toString();
    }

    // Add some decimal places to ensure enough digits to cut (ex: 0.3 ==> 0.3000)
    const stringValue = value.toFixed(decimalPoint + 2);
    const decimalIndex = stringValue.indexOf('.');

    if (decimalIndex === -1) {
      return stringValue;
    } else {
      return stringValue.slice(0, decimalIndex + decimalPoint + 1);
    }
  }

  /**
   * Round number with the given value and decimal point
   * @param value The given value
   * @param decimalPoint The given decimal point
   * @param defaultValue Default value to return
   * @param statisticType
   * @returns string
   */
  static roundedNumber(
    value: number | undefined | null,
    decimalPoint = 2,
    defaultValue = EMPTY,
    statisticType?: StatisticType,
  ): string {
    // With value is empty (null / undefined) or not a number, infinity => return defaultValue
    if ((!value && value !== 0) || isNaN(value) || !isFinite(value)) {
      return defaultValue;
    }

    decimalPoint = !Utils.isNullishNumber(decimalPoint) ? decimalPoint : 2;

    switch (statisticType) {
      case 'mean':
        decimalPoint += 1;

        break;

      case 'sd':
        decimalPoint += 2;

        break;

      case 'cv':
        decimalPoint = 1;

        break;

      case 'biasPercent':
        decimalPoint = 2;

        break;

      case 'sdi':

      case 'zscore':
        decimalPoint = 3;

        break;

      default:
        break;
    }

    const isNegative = value < 0;

    // Shift with exponential notation to avoid floating-point issues.
    const factor = Math.pow(10, decimalPoint);
    const roundedVal = Math.round(Math.abs(value) * factor) / factor;
    const roundedStr = `${isNegative ? '-' : EMPTY}${roundedVal}`;

    return Utils.getNumberStrWithDecimalZero(roundedStr, decimalPoint);
  }

  static getNumberStrWithDecimalZero(value?: string, decimalPoint?: number): string {
    if (!value) {
      return EMPTY;
    }

    decimalPoint = !Utils.isNullishNumber(decimalPoint) ? decimalPoint : 2;

    // With case of the decimalPoint is greater than 0, return the number with decimal point
    if (decimalPoint) {
      const valueArray = value.split('.');
      const integerDigits = valueArray[0];
      let fractionDigits = valueArray[1] || EMPTY;

      // With case of the number of decimal point if not enough, add 0 to the end of the number
      fractionDigits = fractionDigits.padEnd(decimalPoint ?? 2, '0');

      return `${integerDigits}.${fractionDigits}`;
    }

    return value;
  }

  static getUniqueId(parts: number): string {
    const stringArr = [];

    for (let i = 0; i < parts; i++) {
      const S4 = (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);

      stringArr.push(S4);
    }

    return stringArr.join('-');
  }

  static convertNaNValue(value: number | undefined) {
    if (!value) {
      return 0;
    } else {
      return value;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static isEmptyObject(value: any): boolean {
    return typeof value === 'object' && Object.keys(value).length === 0;
  }

  static getCurrentTimeDiff(date: string) {
    const currentDate = moment();

    const dateInput = moment.utc(date).local();

    return moment.duration(dateInput.diff(currentDate));
  }

  static splitTextIntoChunks(text: string, limit: number): string[] {
    const chunks: string[] = [];

    text = text.trim();

    // Remove consecutive whitespaces and trim the text
    text = text.replace(/\s+/g, ' ').trim();

    let start = 0;

    while (start < text.length) {
      // Find the nearest space before the limit
      let end = start + limit;

      if (end >= text.length) {
        chunks.push(text.substring(start));

        break;
      }

      while (end > start && text[end] !== ' ') {
        end--;
      }

      if (end === start) {
        end = start + limit;
      }

      chunks.push(text.substring(start, end));

      start = end;

      // Skip any whitespace after the end
      while (start < text.length && text[start] === ' ') {
        start++;
      }
    }

    return chunks;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static getKeyByValue<T>(object: any, value: T) {
    return Object.keys(object).find((key) => object[key] === value) ?? EMPTY;
  }

  static getSessionStorage<T>(key: string): T {
    const value = sessionStorage.getItem(key);

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return value ? (value as T) : (EMPTY as T);
  }

  static setSessionStorage(key: string, value: string) {
    sessionStorage.setItem(key, value);
  }

  static getDefaultSite(userStoreService: UserStoreService) {
    const storedSiteCode = Utils.getSessionStorage<string>('defaultSelectionSite');

    return storedSiteCode ?? userStoreService?.user?.defaultSite?.siteCode ?? EMPTY;
  }

  static getCommonSystemValue<T>(key: string, defaultValue: T): T {
    const systemConfig = GlobalVariables.getCommonSystems(key);

    if (!systemConfig) {
      return defaultValue;
    }

    const getConfigValue: Record<string, T> = {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      number: Number(systemConfig.value) as T,
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      boolean: (systemConfig.value === 'true') as T,
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      string: systemConfig.value as T,
    };

    return getConfigValue[systemConfig.type.toLowerCase()] ?? defaultValue;
  }

  // common function to remove duplicate items from an array and not known exactly array type
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  static removeArrayDuplicateItems(array: any[], key: string) {
    return array.filter((item, index, self) => index === self.findIndex((t) => t[key] === item[key]));
  }

  static isServiceLevel2(userStoreService: UserStoreService): boolean {
    const defSiteCode = Utils.getDefaultSite(userStoreService);

    const site = userStoreService.user?.access
      .flatMap((access) => access.sites)
      .find((site) => site.siteCode === defSiteCode);

    return site?.isServiceL2 ?? false;
  }

  static replaceText(textReplace: string, data: Record<string, string | number>): string {
    return textReplace.replace(/\$\(([^\\)]+)?\)/g, (_, $2) => data[$2].toString());
  }

  static isExceedDecimals(value: number, maxDecimalPlaces?: number): boolean {
    if (maxDecimalPlaces === undefined || !value) {
      return false;
    }

    const decimalPart = value.toString().split('.')[1];

    return decimalPart?.length > maxDecimalPlaces;
  }

  static isXbarmChart(result: QcResult<LimitDataPoint | undefined> | XbarmResult): result is XbarmResult {
    return result && result.chartType === CHART_MODE.XBARM_CHART;
  }

  static memoize(fn: Function) {
    const cache = new Map<string, unknown>();

    return function (...args: unknown[]) {
      const combinedArgs = [...args];
      const key = JSON.stringify(combinedArgs);

      if (cache.has(key)) {
        return cache.get(key);
      }

      const result = fn(...args);

      cache.set(key, result);

      return result;
    };
  }

  static getFirstAndBlocksOfFour<T>(arr: T[]): T[][] {
    if (arr.length === 0) {
      return [];
    }

    const result: T[][] = [];

    result.push([arr[0]]);

    for (let i = 1; i < arr.length; i += 4) {
      result.push(arr.slice(i, i + 4));
    }

    return result;
  }

  static isRecordEmpty<T>(data: Record<string, T>): boolean {
    return Object.keys(data).length === 0;
  }

  static getChartProperties(
    chartType: 'analyzer' | 'level' | 'xbarm',
    level: number,
    index = 0,
  ): { icon: string; color: string } {
    const colorMaps = {
      analyzer: [
        '#864B99',
        '#DBB62E',
        '#00B8EE',
        '#B57241',
        '#F09DC1',
        '#4093DB',
        '#D28A2B',
        '#CFE47A',
        '#F79F8B',
        '#B577C1',
      ],
      level: [EMPTY, CHART_LEVELS_COLOR.LEVEL_1, CHART_LEVELS_COLOR.LEVEL_2, CHART_LEVELS_COLOR.LEVEL_3],
      xbarm: ['var(--purple-100)'],
    };

    const symbolMap = [EMPTY, POINT_MARKER.CIRCLE, POINT_MARKER.SQUARE, POINT_MARKER.TRIANGLE];

    const selectedColorMap = colorMaps[chartType] || colorMaps.xbarm;

    const getColorMap = (value: number) => {
      return selectedColorMap[value] !== undefined ? value : 0;
    };

    const selectedLevel = chartType === 'analyzer' ? getColorMap(index) : getColorMap(level);

    return {
      icon: symbolMap[level],
      color: selectedColorMap[selectedLevel],
    };
  }

  static getLevelBaseOnFullLotNumber(materialControlId: string): number {
    return Number(materialControlId.charAt(materialControlId.length - 1));
  }

  static getLimitedDateFromEndDate(endDate: Date, limitMonthFromEndDate: number, maximumBackwardYear: number): Date {
    const yearsAgo = moment().subtract(maximumBackwardYear, 'years');

    const limitMonthsAgo = moment(endDate).subtract(limitMonthFromEndDate, 'months');

    return moment.max([yearsAgo, limitMonthsAgo]).startOf('day').toDate();
  }

  static getLimitedDateFromStartDate(startDate: Date, limitMonthFromStartDate: number): Date {
    const currentTime = moment();

    const limitMonthsAfter = moment(startDate).add(limitMonthFromStartDate, 'months');

    return moment.min([currentTime, limitMonthsAfter]).endOf('day').toDate();
  }

  static getChangedProperties<T>(previousState: T, currentState: T): (keyof T)[] {
    const changedProperties: (keyof T)[] = [];

    for (const key in currentState) {
      if (currentState[key] !== previousState[key]) {
        changedProperties.push(key);
      }
    }

    return changedProperties;
  }

  static generateUUID(): string {
    // Generate an array of 16 random bytes
    const randomValues = new Uint8Array(16);

    crypto.getRandomValues(randomValues);

    // Set the version to 4 (random UUID)
    randomValues[6] = (randomValues[6] & 0x0f) | 0x40;
    randomValues[8] = (randomValues[8] & 0x3f) | 0x80;

    // Convert the random values to a UUID string
    return Array.from(randomValues)
      .map((byte, index) => {
        const hex = byte.toString(16).padStart(2, '0');

        // Insert hyphens at the appropriate positions

        if (index === 4 || index === 6 || index === 8 || index === 10) {
          return `-${hex}`;
        }

        return hex;
      })
      .join('');
  }

  static checkPropertiesDifference<T>(array: T[], property: keyof T): boolean {
    const uniqueValues = new Set(array.map((item) => item[property]));

    return uniqueValues.size > 1;
  }

  static getPagingData<T>(data: T[], page: number, numberOfItems: number): T[] {
    const from = page === 1 ? 0 : (page - 1) * numberOfItems;
    const to = from + numberOfItems;

    return from < data.length ? data.slice(from, to) : [];
  }

  static addTimeZoneCharacter(date: string): string {
    if (!date) {
      return EMPTY;
    }

    return date.endsWith('Z') ? date : date.concat('Z');
  }

  static normalizeValue(value: number, max: number, min: number, decimalPoint: number, isPercentage = false) {
    let newValue = value ?? 0;

    if (value > max) {
      newValue = max;
    } else if (value < min) {
      newValue = min;
    }

    return !isPercentage ? +Utils.roundedNumber(newValue, decimalPoint) : newValue;
  }

  static sanitizeFileName(fileName: string): string {
    return fileName.replace(/[^a-zA-Z0-9_\-.]/g, '');
  }

  static simplifyLanguageCode(langCode: string | null): string {
    if (!langCode) {
      return 'en'; // Default to 'en' if langCode is null or undefined
    }

    return langCode.split('-')[0];
  }

  static isCustomerDifferentWithAssay(
    assayLimit: AssayLimit | TableAssayLimit,
    customerLimit: CustomerLimit | TableCustomerLimit,
    colId: string | undefined,
  ): boolean {
    switch (colId) {
      case 'target':

      case 'lowerLimit':

      case 'upperLimit': {
        if (!assayLimit[colId] || !customerLimit[colId]) {
          return false;
        }

        return Number(assayLimit[colId]) !== Number(customerLimit[colId]);
      }

      default:
        return false;
    }
  }
}
