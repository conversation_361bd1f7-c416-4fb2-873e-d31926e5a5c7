import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';

import { ActivatedRoute } from '@angular/router';
import { DesignSystemModule } from '@sysmex/design-system';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTabsModule } from '@angular/material/tabs';

import { NumberSetting, NumberSettingInput } from '../../interfaces/number-setting';
import { DialogService } from 'src/app/services/dialog.service';
import { EMPTY } from 'src/app/constant';
import { EventFocusRuleComponent } from 'src/app/models/event-params';
import { EventParam } from 'src/app/models/event-items';
import { NumberSettingComponent } from './number-setting.component';
import { RuleConfigurationService } from '../../services/rule-configuration.service';
import { SnackBarService } from 'src/app/services/snackbar.service';
import { UserStoreService } from 'src/app/services/user-store.service';
import { Utils } from 'src/app/helpers/UtilFunctions';

describe('NumberSettingComponent', () => {
  let component: NumberSettingComponent;

  let fixture: ComponentFixture<NumberSettingComponent>;

  let dialogService: DialogService;

  let matDialog: MatDialog;

  let snackBarService: SnackBarService;

  const mockUserStoreService = {};

  let fetchDataMock: jest.Mock;

  const ruleConfigurationServiceMock = {
    getNumberSettings: jest.fn().mockReturnValue(of([])),
    updateNumberSettings: jest.fn().mockReturnValue(of({})),
  };

  beforeEach(async () => {
    const activatedRouteMock = {
      params: of({ id: '123' }),
      queryParams: of({ search: 'test' }),
      data: of({ pagecode: '123' }),
    };

    fetchDataMock = jest.fn();

    const dialogServiceMock = {
      openDialog$: {
        next: jest.fn(),
      },
    };

    const snackbarServiceMock = {
      displayMsg: jest.fn(),
    };

    const matDialogMock = {
      closeAll: jest.fn(),
    };

    await TestBed.configureTestingModule({
      imports: [DesignSystemModule, MatButtonModule, MatCardModule, MatIconModule, MatSlideToggleModule, MatTabsModule],
      providers: [
        {
          provide: DialogService,
          useValue: dialogServiceMock,
        },
        {
          provide: RuleConfigurationService,
          useValue: ruleConfigurationServiceMock,
        },
        {
          provide: SnackBarService,
          useValue: snackbarServiceMock,
        },
        {
          provide: MatDialog,
          useValue: matDialogMock,
        },
        {
          provide: UserStoreService,
          useValue: mockUserStoreService,
        },
        {
          provide: ActivatedRoute,
          useValue: activatedRouteMock,
        },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NumberSettingComponent);

    component = fixture.componentInstance;

    fixture.detectChanges();

    dialogService = TestBed.inject(DialogService);

    matDialog = TestBed.inject(MatDialog);

    snackBarService = TestBed.inject(SnackBarService);

    component.numberInputConstraints = {
      min: 0,
      max: 100,
      allowDecimals: false,
    };

    component.percentInputConstraints = {
      min: 0,
      max: 100,
      allowDecimals: false,
    };
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle empty data', () => {
    component.formatResponse([]);

    expect(component.inputValues).toEqual([]);
  });

  it('should transform inputValues to the correct format', () => {
    component.inputValues = [
      {
        id: 'key1',
        value: '1',
        label: 'label1',
        type: 'type1',
        order: 1,
        min: 0,
        max: 10,
      },
      {
        id: 'key2',
        value: '2',
        label: 'label2',
        type: 'type2',
        order: 2,
        min: 0,
        max: 10,
      },
    ];

    const result = component.getDataToSave();

    expect(result).toEqual([
      {
        settingKey: 'key1',
        settingValue: 1,
      },
      {
        settingKey: 'key2',
        settingValue: 2,
      },
    ]);
  });

  it('should call openSaveConfirmDialog when call onSave', () => {
    jest.spyOn(component, 'isAllInputValid').mockReturnValue(true);
    jest.spyOn(component, 'openSaveConfirmDialog');
    jest.spyOn(Utils, 'isArraysEqualOnKeys').mockReturnValue(false);

    component.onSave();

    expect(component.isEditMode).toBe(false);
    expect(component.openSaveConfirmDialog).toHaveBeenCalled();
  });

  it('should not call openSaveConfirmDialog when data unchanged', () => {
    jest.spyOn(component, 'isAllInputValid').mockReturnValue(true);
    jest.spyOn(Utils, 'isArraysEqualOnKeys').mockReturnValue(true);

    component.onSave();

    expect(snackBarService.displayMsg).toHaveBeenCalled();
  });

  it('should display error message if input is invalid', () => {
    jest.spyOn(component, 'isAllInputValid').mockReturnValue(false);

    component.onSave();

    expect(component.isAllInputValid).toHaveBeenCalled();
    expect(snackBarService.displayMsg).toHaveBeenCalled();
  });

  it('should save data correctly', () => {
    const mockGetDataToSave = jest.spyOn(component, 'getDataToSave');

    component.saveData();

    expect(mockGetDataToSave).toHaveBeenCalled();
  });

  it('should display an error message and perform the correct actions on error', () => {
    ruleConfigurationServiceMock.updateNumberSettings.mockReturnValue(throwError(() => new Error('Error occurred')));

    component.saveData();

    expect(snackBarService.displayMsg).toHaveBeenCalledWith(
      $localize`:app-app_message-text:The system cannot perform this action. Please try again.`,
      {
        type: 'error',
      },
    );
  });

  it('should filter out data that does not match NUMBER_SETTINGS', () => {
    const data = [
      {
        settingKey: 'non_existing_key',
        settingValue: 10,
      },
    ];

    component.ruleCode = 'rule1';

    component.formatResponse(data);

    expect(component.inputValues).toEqual([]);
  });

  it('should set isEditMode to false and call backupData when focusIdentifierType is view', async () => {
    const params: EventParam = {
      objs: [new EventFocusRuleComponent('view')],
      className: EMPTY,
    };

    // Act
    await component.onEvent(params);

    expect(component.isEditMode).toBe(false);
  });

  it('should create a NumberSettingInput with type "number"', () => {
    const inputNumSetting: Partial<NumberSettingInput> = {
      id: '1',
      label: 'Test Number',
      type: 'number',
      order: 1,
    };

    const numSetting: NumberSetting = {
      settingValue: 50,
      settingKey: '',
    };

    const result = component.createSettingInputNumber(inputNumSetting, numSetting);

    expect(result).toEqual({
      id: '1',
      label: 'Test Number',
      type: 'number',
      value: 50,
      order: 1,
      min: 0,
      max: 100,
    });
  });
});
