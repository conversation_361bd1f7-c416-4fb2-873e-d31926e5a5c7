import { ChartModule, StockChart } from 'angular-highcharts';
import { Component, Input } from '@angular/core';

import * as Highcharts from 'highcharts';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import HighchartsBoost from 'highcharts/modules/boost';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSliderModule } from '@angular/material/slider';

import { Observable, concatMap, from, map, toArray } from 'rxjs';

import {
  AdvancedChartConfig,
  CHART_MODE,
  ONE_DAY_IN_MILLISECONDS,
  ZSCORE_STATISTIC_CONFIG,
  normalizeValue,
} from '../../constants';
import {
  AnalyzerParameter,
  ChartEventInfo,
  ChartFunctions,
  ParameterDateRangeInfo,
  Range,
} from '../../interfaces/analyzer';
import {
  BreakPointConfig,
  ChartSeriesByKey,
  ChartsPnt,
  StatisticTableData,
  StatisticTableHeaderConfig,
} from '../../interfaces/qc-data';
import {
  COMMENT_SVG_STRING,
  OUT_OF_BOUND_SVG_STRING,
  SPECIFIC_OUT_OF_BOUND_SVG_STRING,
  UNMANAGED_COMMENT_SVG_STRING,
} from 'src/assets/svg-string';
import { ChartCustomPnt, ChartEvent, ChartSeriesItem, CustomDataPoint } from '../../interfaces/chart';
import { ChartsEventService } from '../../services/charts-event.service';

import { AdvancedLjCommentDialogComponent } from '../advanced-lj-comment-dialog/advanced-lj-comment-dialog.component';
import { ChartBaseComponent } from '../../services/chart/chart';
import { ChartEventGenerationService } from '../../services/chart/chart-event-generator.service';
import { ChartGenerationService } from '../../services/chart/chart-generator.service';
import { EMPTY } from 'src/app/constant';
import { LOT_LEVEL } from 'src/app/enums';
import { MultiRulesLimitSetting } from '../../interfaces/limit-setting';
import { ReagentDialogComponent } from '../reagent-dialog/reagent-dialog.component';
import { SeriesInfo } from '../../interfaces/qc-comparison-data';
import { ServiceHistoriesDialogComponent } from '../service-histories-dialog/service-histories-dialog.component';
import { StatisticTableComponent } from '../statistic-table/statistic-table.component';
import { StatisticTableService } from '../../services/statistic-table.service';
import { Utils } from 'src/app/helpers/UtilFunctions';

HighchartsBoost(Highcharts);

@Component({
  selector: 'app-zscore-chart',
  standalone: true,
  providers: [ChartGenerationService, StatisticTableService, ChartEventGenerationService],
  imports: [
    CommonModule,
    FormsModule,
    ChartModule,
    MatProgressSpinnerModule,
    MatProgressBarModule,
    MatSliderModule,
    AdvancedLjCommentDialogComponent,
    ReagentDialogComponent,
    StatisticTableComponent,
    ServiceHistoriesDialogComponent,
  ],
  templateUrl: './zscore-chart.component.html',
  styleUrl: './zscore-chart.component.scss',
})
export class ZScoreChartChartComponent extends ChartBaseComponent {
  override numberOfCharts = 1;

  override chartContainEvents = {
    hasComments: true,
    hasStatisticTables: true,
    hasCalibrationEvents: true,
    hasServiceEvents: true,
    hasReagentChanges: true,
  };
  override chartMode = CHART_MODE.ZSCORE_CHART;

  defaultDayMilliseconds = AdvancedChartConfig.defaultDayRange * ONE_DAY_IN_MILLISECONDS;
  scaleRange: Range[] = AdvancedChartConfig.yAxis.scaleLevel;
  scaleYValue: number = AdvancedChartConfig.vZoomFrom;
  hasLimitData = false;

  // Chart Configuration
  charts: StockChart[] = [];
  chartRefs: Observable<Highcharts.Chart>[] = [];

  chartData!: ChartSeriesByKey<ChartsPnt>;

  statisticHeaderConfig: StatisticTableHeaderConfig[] = [];
  statisticDataConfig = ZSCORE_STATISTIC_CONFIG;
  statisticTableData: StatisticTableData[] = [];
  breakPointConfig: BreakPointConfig = {
    hasBreakPoint: false,
    breakPoint: 0,
    firstLabel: EMPTY,
    secondLabel: EMPTY,
  };

  toggleInteract: ChartFunctions = {
    hasComments: true,
    hasCalibrationEvents: true,
    hasServiceEvents: true,
    hasReagentChanges: true,
  };

  @Input()
  set basicCriteria(basicCriteria: ParameterDateRangeInfo) {
    if (!Utils.deepObjectEqual(this.parametersDateRange, basicCriteria)) {
      basicCriteria.plotLines = this.plotLines;

      this.parametersDateRange = { ...basicCriteria };

      if (basicCriteria.limitsData) {
        this.hasLimitData = !!basicCriteria.limitsData;
        this.scaleRange = this.chartGenerationService.calculateChartScaleLevels(basicCriteria.limitsData);
      }
    }
  }

  get basicCriteria() {
    return this.parametersDateRange;
  }

  @Input()
  set seriesData(data: ChartSeriesByKey<ChartsPnt>[]) {
    if (Utils.isRecordEmpty(data[0])) {
      this.hasData = false;

      return;
    }

    this.hasData = true;
    this.chartData = data[0];
  }

  @Input()
  set displayFunctions(displayFunctions: ChartFunctions) {
    const changes = this.updateDisplayFunctions(displayFunctions);

    this.detectEventChanges(changes);
  }

  get displayFunctions() {
    return this.toggleInteract;
  }

  @Input() paramMultiRulesLimitSetting!: MultiRulesLimitSetting;
  @Input() analyzerParameter?: AnalyzerParameter;

  plotLines = [
    {
      value: 3,
      color: '#C62D37',
      label: '+3SD',
    },
    {
      value: 2,
      color: '#FFDA3F',
      label: '+2SD',
    },
    {
      value: 0,
      color: '#0DAC67',
      label: $localize`:label:Mean`,
    },
    {
      value: -2,
      color: '#FFDA3F',
      label: '-2SD',
    },
    {
      value: -3,
      color: '#C62D37',
      label: '-3SD',
    },
  ];

  constructor(
    private readonly chartEventGenerationService: ChartEventGenerationService,
    public override chartsEventService: ChartsEventService,
    private readonly statisticTableService: StatisticTableService,
    override chartGenerationService: ChartGenerationService,
  ) {
    super(chartGenerationService, chartsEventService);
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  onDestroy(): void {}

  onInit(): void {
    this.subscribeEvents();

    const subscription = this.chartGenerationService
      .initializeChartWithRetry(
        this.analyzerParameter!,
        this.scaleRange,
        this.hasLimitData,
        this.basicCriteria,
        this.parametersDateRange,
        Array(this.chartData),
        this.mouseOverPoint.bind(this),
        this.chartIndex,
        this.chartMode,
        this.numberOfCharts,
        true,
        true,
      )
      .subscribe((chartObjs) => {
        chartObjs.forEach((chartObj, index) => {
          if (chartObj.chart) {
            this.charts[index] = chartObj.chart;
            this.chartGenerationService.setSeriesToChart(this, index, this.basicCriteria.allSeriesInfo, chartObj.data);

            this.chartEventGenerationService.setDataConfig(this.analyzerParameter!);

            this.prepareStatisticTable();

            this.getLastStatisticData();
          }
        });
      });

    this.subscriptions.add(subscription);
  }

  // Renamed from detectChanges to detectEventChanges for consistency
  detectEventChanges(changes: (keyof ChartFunctions)[]) {
    if (changes) {
      this.chartEventGenerationService.setEventStatus(this.toggleInteract);
    }

    if (
      changes.includes('hasCalibrationEvents') ||
      changes.includes('hasServiceEvents') ||
      changes.includes('hasReagentChanges')
    ) {
      this.removeSeries();
    }

    if (changes.includes('hasComments')) {
      this.chartGenerationService.redrawChart();
    }

    if (changes.includes('hasStatisticTables')) {
      this.prepareStatisticTable();
    }
  }

  getIndicatorInfo(isPassAssayLimit = true, isPassedMultiRules = true) {
    if (!isPassAssayLimit) {
      return {
        isOutOfBound: true,
        indicatorIcon: OUT_OF_BOUND_SVG_STRING,
      };
    }

    if (!isPassedMultiRules) {
      return {
        isOutOfBound: true,
        indicatorIcon: SPECIFIC_OUT_OF_BOUND_SVG_STRING,
      };
    }

    return {
      isOutOfBound: false,
      indicatorIcon: EMPTY,
    };
  }

  subscribeEvents() {
    const subscription = this.chartsEventService.events$.subscribe((event) => {
      this.callbackEvents(event.type, event.id, event.level);
    });

    this.subscriptions.add(subscription);
  }

  callbackEvents(type: ChartEvent, id: string, level?: LOT_LEVEL) {
    switch (type) {
      case ChartEvent.COMMENT:
        if (level) {
          this.addCommentIcon(id);
        }

        break;

      case ChartEvent.MANAGED:

      case ChartEvent.UNMANAGED:
        if (level) {
          this.handleManagedStatus(id, type === ChartEvent.MANAGED, level);
        }

        break;
    }
  }

  addCommentIcon(qcRunDetailId: string) {
    if (this.chartData) {
      Object.values(this.chartData).forEach((series) => {
        const point = series.pnts.find((point) => point.runDetailId === qcRunDetailId) ?? undefined;

        if (point) {
          point.isCommented = true;

          this.chartGenerationService.redrawChart();
        }
      });
    }
  }

  handleManagedStatus(runId: string, managedStatus: boolean, level: LOT_LEVEL) {
    if (this.chartData) {
      Object.entries(this.chartData).forEach(([key, series]) => {
        const point = series.pnts.find((point) => point.runId === runId) ?? undefined;

        if (point) {
          point.isMngd = managedStatus;
          point.isCommented = true;

          this.charts[0].ref$.subscribe((chart) => {
            const seriesPoints = chart.series.filter((_series) => _series.name === $localize`Level ${key}`);

            from(seriesPoints)
              .pipe(
                concatMap((seriesPoint) => this.updateSeriesPointData(seriesPoint, series.pnts, level)),
                toArray(),
              )
              .subscribe(() => this.chartGenerationService.redrawChart());
          });
        }
      });
    }
  }

  updateSeriesPointData(seriesPoint: Highcharts.Series, pnts: ChartsPnt[], level: LOT_LEVEL) {
    return this.processSeriesDataChunked(pnts, level, this.chartIndex, seriesPoint.type === 'line').pipe(
      map((data) => seriesPoint.setData(data, false)),
    );
  }

  onHoverPointData(pointData: ChartCustomPnt) {
    this.getLastStatisticData(pointData);
  }

  createPointData(pointSeries: ChartsPnt, level: LOT_LEVEL): CustomDataPoint {
    const { decimalPoint, unitDisplay } = this.parametersDateRange;
    const {
      origVal,
      isMngd,
      isCommented,
      yValue,
      isPsdAsL,
      isPsdCuL,
      isPsdCoL,
      isPsdMulrL,
      qcSet,
      qcDt,
      runDetailId,
      runId,
      lotNumber,
      isAutoUnmanaged,
      zscoreAssayLowerLimit,
      zscoreAssayMean,
      zscoreAssayUpperLimit,
    } = pointSeries;

    const self = this;

    const { isOutOfBound, indicatorIcon } = this.getIndicatorInfo(isPsdAsL, isPsdMulrL);
    const isAdjustCommentIconPos = (yValue ?? 0) > 3;
    const verticalAlignValue = isAdjustCommentIconPos ? 'top' : 'bottom';

    return {
      x: new Date(qcSet!).getTime(),
      y: normalizeValue(yValue, decimalPoint),
      originalLowerLimit: 0,
      originalUpperLimit: 0,
      originalTarget: 0,
      qcRunDetailId: runDetailId,
      qcRunId: runId,
      level: level,
      isCommented: isCommented,
      zScorePoint: yValue,
      value: origVal,
      decimalPoint: decimalPoint,
      advancedData: { isManaged: isMngd },
      unitDisplay: unitDisplay,
      rawData: origVal,
      lotNumber: lotNumber,
      qcSetInNumber: new Date(qcSet!).getTime(),
      isPassedMultiRulesLimit: isPsdMulrL !== false,
      isPassedAssayLimit: isPsdAsL !== false,
      multiRules: this.paramMultiRulesLimitSetting?.assayLimit,
      zscoreAssayLowerLimit: zscoreAssayLowerLimit,
      zscoreAssayMean: zscoreAssayMean,
      zscoreAssayUpperLimit: zscoreAssayUpperLimit,
      isAutoUnmanaged: isAutoUnmanaged,
      dataLabels: {
        useHTML: true,
        enabled: true,
        y: this.adjustCommentIconY(isOutOfBound, isAdjustCommentIconPos),
        verticalAlign: verticalAlignValue,
        formatter: function (this: Highcharts.PointLabelObject) {
          const commentIcon = isMngd ? COMMENT_SVG_STRING : UNMANAGED_COMMENT_SVG_STRING;

          return self.createDataLabelFormatter(
            self.toggleInteract.hasComments ?? false,
            pointSeries.isCommented ?? false,
            isOutOfBound,
            isAdjustCommentIconPos,
            commentIcon,
            indicatorIcon,
          );
        },
      },
      qcDateTime: qcDt,
      qcDateTimeInNumber: new Date(qcDt).getTime(),
      isPassed: isPsdAsL !== false,
      isPassedCustomerLimit: isPsdCuL!,
      isPassedCountryLimit: isPsdCoL!,
    };
  }

  wrapCreateSeries(
    seriesInfo: SeriesInfo,
    chartIndex: number,
    seriesIndex: number,
    seriesDataPoint: ChartSeriesByKey<ChartsPnt>,
  ): Observable<Highcharts.SeriesOptionsType>[] {
    const pointInfo = seriesDataPoint[`${seriesInfo.name}`];

    if (!pointInfo) {
      return [];
    }

    const { color, pointIcon, level, label } = seriesInfo;
    const model: ChartSeriesItem = {
      series: pointInfo,
      name: label,
      color,
      icon: pointIcon,
      level,
      chartIndex,
      filterPointFunc: (pointSeries: ChartsPnt, includeManagedPoints: boolean) =>
        pointSeries.isMngd === includeManagedPoints,
    };

    return [
      this.createSeries({
        ...model,
        isManaged: true,
      }),
      this.createSeries({
        ...model,
        isManaged: false,
      }),
    ];
  }

  wrapCreateEventsSeries(analyzerEvents: ChartEventInfo): Observable<Highcharts.SeriesOptionsType[]> {
    return this.chartEventGenerationService.wrapCreateEventsSeries(analyzerEvents);
  }

  removeSeries() {
    this.chartEventGenerationService.removeSeries(this.chartContainEvents, this.chartData);
  }

  prepareStatisticTable() {
    this.statisticHeaderConfig = this.statisticTableService.getStatisticTableHeaderConfig(
      this.basicCriteria.allSeriesInfo,
      this.toggleInteract.hasStatisticTables,
    );
  }

  getLastStatisticData(pointData?: ChartCustomPnt) {
    this.statisticTableData = this.statisticTableService.transformStatisticalTable(
      this.parametersDateRange,
      this.chartData,
      false,
      pointData,
    );
  }
}
