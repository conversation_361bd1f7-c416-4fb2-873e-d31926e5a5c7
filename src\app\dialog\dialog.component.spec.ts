import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AclService } from '../services/acl.service';
import { DialogComponent } from './dialog.component';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';

describe('DialogComponent', () => {
  let component: DialogComponent;
  let fixture: ComponentFixture<DialogComponent>;
  const mockAclService = {
    pageCode: 'test-page-code',
    parentCode: 'test-parent-code',
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogComponent],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            title: 'Test Dialog',
            content: null,
            actions: [],
          },
        },
        {
          provide: AclService,
          useValue: mockAclService,
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
