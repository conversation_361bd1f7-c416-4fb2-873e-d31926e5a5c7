import { Chart, Series } from 'highcharts';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Subject, of } from 'rxjs';
import { DatePipe } from '@angular/common';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { StockChart } from 'angular-highcharts';

import { ChartFunctions, CustomDataPoint, ParameterDateRangeInfo, TargetLimit } from '../../interfaces/analyzer';
import { ChartSeriesByKey, ChartsPnt } from '../../interfaces/qc-data';
import { OUT_OF_BOUND_SVG_STRING, SPECIFIC_OUT_OF_BOUND_SVG_STRING } from 'src/assets/svg-string';

import { AdvancedChartConfig } from '../../constants';
import { ChartEvent } from '../../interfaces/chart';
import { ChartEventGenerationService } from '../../services/chart/chart-event-generator.service';
import { ChartGenerationService } from '../../services/chart/chart-generator.service';
import { ChartsEventService } from '../../services/charts-event.service';
import { EMPTY } from 'src/app/constant';
import { LOT_LEVEL } from 'src/app/enums';
import { StatisticTableService } from '../../services/statistic-table.service';
import { SysmexService } from 'src/app/services/sysmex.service';
import { UserStoreService } from 'src/app/services/user-store.service';
import { Utils } from 'src/app/helpers/UtilFunctions';
import { ZScoreChartChartComponent } from './zscore-chart.component';

describe('ZScoreChartChartComponent', () => {
  let component: ZScoreChartChartComponent;
  let fixture: ComponentFixture<ZScoreChartChartComponent>;

  const mockChartsEventService = {
    events$: new Subject<{ type: ChartEvent; id: string }>(),
    mapEventsByDate: jest.fn(),
    mapCalibrationEvents: jest.fn(),
    mapServiceEvents: jest.fn(),
    mapReagentEvents: jest.fn(),
  };

  const chartGenerationServiceMock = {
    redrawChart: jest.fn(),
    calculateChartScaleLevels: jest.fn(),
    initializeChartWithRetry: jest.fn(),
    setSeriesToChart: jest.fn(),
    mapEventsByDate: jest.fn(),
    mapCalibrationEvents: jest.fn(),
    mapServiceEvents: jest.fn(),
    mapReagentEvents: jest.fn(),
    executeChartWithCallback: jest.fn().mockImplementation((chartIdx, callback) => of(callback(chartIdx))),
    setEventsToChart: jest.fn(),
    chartContainEvents: {
      hasCalibrationEvents: true,
      hasReagentChanges: true,
      hasServiceEvents: true,
      hasComments: true,
    },
    updateYAxisScale: jest.fn(),
    addChartTargetHoverTooltipListener: jest.fn(),
  };

  const statisticTableServiceMock = {
    getStatisticTableHeaderConfig: jest.fn().mockImplementation((seriesInfo, isShow) =>
      seriesInfo.map((series: { label: string; color: string; isBold: boolean }, index: number) => {
        return {
          title: series.label,
          color: series.color,
          isPrimary: series.isBold,
          isShow: isShow,
          index,
        };
      }),
    ),
    getBreakPointConfig: jest.fn(),
    getLastRunData: jest.fn(),
    transformStatisticalTable: jest.fn(),
    triggerPointHoverEvent: jest.fn(),
  };

  const mockRouter = {
    navigate: jest.fn().mockReturnValue(Promise.resolve(true)),
  };

  const mockUserStoreService = {
    user: jest.fn(),
  };

  const chartEventGenerationServiceMock = {
    toggleInteract: {
      hasComments: false,
      hasCalibrationEvents: false,
      hasServiceEvents: false,
      hasReagentChanges: false,
    },
    openCommentDialog$: new Subject(),
    openReagentDialog$: new Subject(),
    openServiceHistoriesDialog$: new Subject(),
    setEventStatus: jest.fn(),
    removeSeries: jest.fn(),
  };

  const mockFormatCollection = {
    DATE_MONTH_YEAR: 'MM/dd/YYYY',
    DATE_MONTH_YEAR_REPORT: 'MMDDYYYY',
    FORMAT_1: 'HH:mm:ss',
    FORMAT_4: 'MM/dd/yyyy hh:mm:ss',
    API_FORMAT: 'YYYY-MM-dd',
  };

  const mockSysmex = {
    formatDate: jest.fn().mockImplementation((format, date) => {
      const datePipe = new DatePipe('en-US');

      return datePipe.transform(date, format);
    }),
    DATES: mockFormatCollection,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [],
      providers: [
        {
          provide: ChartsEventService,
          useValue: mockChartsEventService,
        },
        {
          provide: Router,
          useValue: mockRouter,
        },
        {
          provide: ChartGenerationService,
          useValue: chartGenerationServiceMock,
        },
        {
          provide: StatisticTableService,
          useValue: statisticTableServiceMock,
        },
        {
          provide: UserStoreService,
          useValue: mockUserStoreService,
        },
        {
          provider: ChartEventGenerationService,
          useValue: chartEventGenerationServiceMock,
        },
        {
          provide: SysmexService,
          useValue: mockSysmex,
        },
      ],
      imports: [ZScoreChartChartComponent],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ZScoreChartChartComponent);
    component = fixture.componentInstance;
    AdvancedChartConfig.vZoomFrom = 2;
    component.scaleRange = [
      {
        from: 0,
        to: 10,
      },
      {
        from: 10,
        to: 20,
      },
    ];

    component.parameterCode = 'testCode';

    component.basicCriteria.channelId = 1;

    component.basicCriteria.parameterCode = 'param';

    fixture.detectChanges();

    component.charts = [
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      {
        destroy: jest.fn(),
        options: {},
        refSubject: new Subject(),
        ref$: new Subject(),
        ref: {},
        init: jest.fn(),
      } as unknown as StockChart,
    ];
  });

  afterEach(() => {
    fixture.destroy();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should process series data chunked', () => {
    const series: ChartsPnt[] = [
      {
        runDetailId: '1',
        runId: '1',
        varPct: 10,
        origVal: 1,
        origUpp: 2,
        origLow: 0,
        origTgt: 1.5,
        qcDt: '2023-01-01',
        isMngd: true,
        isPsd: true,
        isPsdAsL: true,
        isPsdCuL: false,
        isPsdCoL: false,
        groupIndex: -1,
      },
      {
        runDetailId: '2',
        runId: '2',
        varPct: 20,
        origVal: 2,
        origUpp: 3,
        origLow: 1,
        origTgt: 2.5,
        qcDt: '2023-01-02',
        isMngd: false,
        isPsd: false,
        isPsdAsL: true,
        isPsdCuL: false,
        isPsdCoL: false,
        groupIndex: -1,
      },
    ];

    const result = component.processSeriesDataChunked(series, 0, 1, true);

    expect(result).toBeDefined();

    result.subscribe((data) => {
      expect(data[0]).toEqual(
        expect.objectContaining({
          x: new Date('2023-01-01').getTime(),
          y: 0.1,
          originalLowerLimit: 0,
          originalUpperLimit: 2,
          originalTarget: 1.5,
          qcRunDetailId: '1',
          qcRunId: '1',
          value: 1,
          decimalPoint: component.parametersDateRange.decimalPoint,
          advancedData: {
            isManaged: true,
          },
          unitDisplay: component.parametersDateRange.unitDisplay,
          rawData: 10,
          qcSetInNumber: new Date('2023-01-01').getTime(),
          isPassed: true,
          isPassedAssayLimit: true,
          isPassedCustomerLimit: false,
          isPassedCountryLimit: false,
        }),
      );
    });
  });

  it('should process series data in chunks and return an observable of point options objects', () => {
    const seriesMock: ChartsPnt[] = [
      {
        varPct: 1.05,
        isMngd: true,
        isPsd: true,
        isPsdAsL: true,
        isPsdCuL: false,
        isPsdCoL: false,
        origVal: 100,
        qcDt: '2020-01-01',
        origLow: 90,
        origUpp: 110,
        origTgt: 100,
        runDetailId: '1',
        runId: '1',
        isCommented: false,
        groupIndex: -1,
      },
      // Add more mock series data as needed
    ];
    const includeManagedPoints = true;
    const chunkSize = 1; // To simplify testing, use a small chunk size
    const level = 2;

    component.parametersDateRange = {
      conversionRate: 1,
      decimalPoint: 2,
      unitDisplay: 'unit',
      channelId: 1,
      parameterUnitId: 2,
      parameterId: 2,
      parameterCode: '',
      allSeriesInfo: [],
      criteriaDateRange: {
        start: '2020-01-01',
        end: '2020-01-02',
      },
      limitsData: {
        assay: {
          upper: 110,
          lower: 90,
          target: 100,
        },
        country: {
          upper: 120,
          lower: 80,
          target: 100,
        },
        customer: {
          upper: 130,
          lower: 70,
          target: 100,
        },
      },
      parameterDisplayName: '',
      siteCode: '',
      modelGroup: '',
      analyzerSerial: '',
      isCountryLimitChart: false,
    };
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    component.basicCriteria = { limitsData: true } as unknown as ParameterDateRangeInfo;

    const result$ = component.processSeriesDataChunked(
      seriesMock,
      level,
      0,
      includeManagedPoints,
      (pointSeries: ChartsPnt, includeManagedPoints: boolean) => pointSeries.isMngd === includeManagedPoints,
      false,
      chunkSize,
    );

    result$.subscribe({
      next: (result) => {
        expect(result.length).toBeGreaterThan(0);

        const firstPoint = result[0];

        expect(firstPoint).toHaveProperty('x');
        expect(firstPoint).toHaveProperty('y');
        expect(firstPoint).toHaveProperty('dataLabels');
        expect(typeof firstPoint.dataLabels!).toBe('object');
      },
    });
  });

  it('should set hasData to false if data is empty', () => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const data: ChartSeriesByKey<ChartsPnt>[] = [{} as ChartSeriesByKey<ChartsPnt>];

    jest.spyOn(Utils, 'isRecordEmpty').mockReturnValue(true);

    component.seriesData = data;

    expect(component.hasData).toBe(false);
  });

  it('should set hasData to true and assign chartData if data is not empty', () => {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const data: ChartSeriesByKey<ChartsPnt>[] = [{ someKey: { pnts: [] } } as ChartSeriesByKey<ChartsPnt>];

    jest.spyOn(Utils, 'isRecordEmpty').mockReturnValue(false);

    component.seriesData = data;

    expect(component.hasData).toBe(true);
    expect(component.chartData).toBe(data[0]);
  });

  it('should not update toggleInteract or call setEventStatus if displayFunctions does not change', () => {
    const displayFunctions: ChartFunctions = {
      hasComments: true,
      hasStatisticTables: true,
      hasCalibrationEvents: true,
      hasServiceEvents: true,
      hasReagentChanges: true,
    };

    jest.spyOn(Utils, 'deepObjectEqual').mockReturnValue(true);
    jest.spyOn(Utils, 'getChangedProperties').mockReturnValue([]);
    jest.spyOn(component, 'detectEventChanges');

    component.displayFunctions = displayFunctions;

    expect(component.toggleInteract).not.toEqual(displayFunctions);
    expect(component.detectEventChanges).toHaveBeenCalled();
  });
  it('should not call setEventStatus, prepareStatisticTable if displayFunctions does not change', () => {
    const setEventSpy = jest.spyOn(chartEventGenerationServiceMock, 'setEventStatus');
    const redDrawSpy = jest.spyOn(chartGenerationServiceMock, 'redrawChart');
    const removeSeriesSpy = jest.spyOn(component, 'removeSeries');
    const prepareStatisticTableSpy = jest.spyOn(component, 'prepareStatisticTable');

    component.detectEventChanges([]);

    expect(setEventSpy).not.toHaveBeenCalled();
    expect(redDrawSpy).not.toHaveBeenCalled();
    expect(removeSeriesSpy).not.toHaveBeenCalled();
    expect(prepareStatisticTableSpy).not.toHaveBeenCalled();
  });

  it('should call setEventStatus, prepareStatisticTable if displayFunctions is changed', () => {
    component.chartData = {
      ...component.chartData,
      reagentEvent: {
        evtPnts: undefined,
        pnts: [],
      },
    };

    const removeSeriesSpy = jest.spyOn(component, 'removeSeries');
    const prepareStatisticTableSpy = jest.spyOn(component, 'prepareStatisticTable');

    component.detectEventChanges(['hasCalibrationEvents', 'hasComments', 'hasStatisticTables']);

    expect(removeSeriesSpy).toHaveBeenCalled();
    expect(prepareStatisticTableSpy).toHaveBeenCalled();
  });

  it('should subscribe to chart events and call callbackEvents on event emission', () => {
    const event = {
      type: ChartEvent.COMMENT,
      id: '1',
      level: LOT_LEVEL.LEVEL_1,
    };
    const callbackEventsSpy = jest.spyOn(component, 'callbackEvents');

    component.subscribeEvents();
    mockChartsEventService.events$.next(event);

    expect(callbackEventsSpy).toHaveBeenCalledWith(event.type, event.id, event.level);
  });

  it('should call addCommentIcon if event type is COMMENT and level is defined', () => {
    const addCommentIconSpy = jest.spyOn(component, 'addCommentIcon');
    const event = {
      type: ChartEvent.COMMENT,
      id: '1',
      level: LOT_LEVEL.LEVEL_1,
    };

    component.callbackEvents(event.type, event.id, event.level);

    expect(addCommentIconSpy).toHaveBeenCalledWith(event.id);
  });

  it('should not call addCommentIcon if event type is not COMMENT', () => {
    const addCommentIconSpy = jest.spyOn(component, 'addCommentIcon');
    const event = {
      type: 0,
      id: '1',
      level: LOT_LEVEL.LEVEL_1,
    };

    component.callbackEvents(event.type, event.id, event.level);

    expect(addCommentIconSpy).not.toHaveBeenCalled();
  });

  it('should not call addCommentIcon if event type is COMMENT but level is undefined', () => {
    const addCommentIconSpy = jest.spyOn(component, 'addCommentIcon');
    const event = {
      type: ChartEvent.COMMENT,
      id: '1',
    };

    component.callbackEvents(event.type, event.id);

    expect(addCommentIconSpy).not.toHaveBeenCalled();
  });

  it('should set isCommented to true and redraw chart if point is found in addCommentIcon', () => {
    const redrawChartSpy = jest.spyOn(component.chartGenerationService, 'redrawChart');

    component.chartData = {
      series1: {
        pnts: [
          {
            runDetailId: '1',
            isCommented: false,
            varPct: 0,
            origVal: 0,
            origUpp: 0,
            origLow: 0,
            origTgt: 0,
            qcDt: '',
            isMngd: false,
            isPsd: false,
            isPsdAsL: false,
            isPsdCuL: false,
            isPsdCoL: false,
            groupIndex: -1,
          },
        ],
      },
    };

    component.addCommentIcon('1');

    expect(component.chartData['series1'].pnts[0].isCommented).toBe(true);
    expect(redrawChartSpy).toHaveBeenCalled();
  });

  it('should not call redrawChart if point is not found in addCommentIcon', () => {
    const redrawChartSpy = jest.spyOn(component.chartGenerationService, 'redrawChart');

    component.chartData = {
      series1: {
        pnts: [
          {
            runDetailId: '2',
            isCommented: false,
            varPct: 0,
            origVal: 0,
            origUpp: 0,
            origLow: 0,
            origTgt: 0,
            qcDt: '',
            isMngd: false,
            isPsd: false,
            isPsdAsL: false,
            isPsdCuL: false,
            isPsdCoL: false,
            groupIndex: -1,
          },
        ],
      },
    };

    component.addCommentIcon('1');

    expect(redrawChartSpy).not.toHaveBeenCalled();
  });

  it('should call processSeriesDataChunked and setData on seriesPoint', () => {
    const seriesPointMock = {
      setData: jest.fn(),
      type: 'line',
    };

    const pntsMock: ChartsPnt[] = [
      {
        runId: '1',
        isMngd: false,
        varPct: 0,
        origVal: 0,
        origUpp: 0,
        origLow: 0,
        origTgt: 0,
        qcDt: '',
        isPsd: false,
        isPsdAsL: false,
        isPsdCuL: false,
        isPsdCoL: false,
        groupIndex: -1,
      },
    ];

    const result$ = component.updateSeriesPointData(
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      seriesPointMock as unknown as Series,
      pntsMock,
      LOT_LEVEL.LEVEL_1,
    );

    result$.subscribe(() => {
      expect(seriesPointMock.setData).toHaveBeenCalled();
    });
  });

  it('should call handleManagesdStatus if event type is MANAGED and level is defined', () => {
    const handleManagedStatusSpy = jest.spyOn(component, 'handleManagedStatus');
    const event = {
      type: ChartEvent.MANAGED,
      id: '1',
      level: LOT_LEVEL.LEVEL_1,
    };

    component.callbackEvents(event.type, event.id, event.level);

    expect(handleManagedStatusSpy).toHaveBeenCalledWith(event.id, true, event.level);
  });

  it('should call handleManagedStatus if event type is UNMANAGED and level is defined', () => {
    const handleManagedStatusSpy = jest.spyOn(component, 'handleManagedStatus');
    const event = {
      type: ChartEvent.UNMANAGED,
      id: '1',
      level: LOT_LEVEL.LEVEL_1,
    };

    component.callbackEvents(event.type, event.id, event.level);

    expect(handleManagedStatusSpy).toHaveBeenCalledWith(event.id, false, event.level);
  });

  it('should not call handleManagedStatus if event type is MANAGED but level is undefined', () => {
    const handleManagedStatusSpy = jest.spyOn(component, 'handleManagedStatus');
    const event = {
      type: ChartEvent.MANAGED,
      id: '1',
    };

    component.callbackEvents(event.type, event.id);

    expect(handleManagedStatusSpy).not.toHaveBeenCalled();
  });

  it('should not call handleManagedStatus if event type is UNMANAGED but level is undefined', () => {
    const handleManagedStatusSpy = jest.spyOn(component, 'handleManagedStatus');
    const event = {
      type: ChartEvent.UNMANAGED,
      id: '1',
    };

    component.callbackEvents(event.type, event.id);

    expect(handleManagedStatusSpy).not.toHaveBeenCalled();
  });

  it('should return out of bound info with OUT_OF_BOUND_SVG_STRING if isPassAssayLimit is false', () => {
    const result = component.getIndicatorInfo(false, true);

    expect(result).toEqual({
      isOutOfBound: true,
      indicatorIcon: OUT_OF_BOUND_SVG_STRING,
    });
  });

  it('should return out of bound info with SPECIFIC_OUT_OF_BOUND_SVG_STRING if isPassedMultiRules is false', () => {
    const result = component.getIndicatorInfo(true, false);

    expect(result).toEqual({
      isOutOfBound: true,
      indicatorIcon: SPECIFIC_OUT_OF_BOUND_SVG_STRING,
    });
  });

  it('should return not out of bound info with EMPTY if both isPassAssayLimit and isPassedMultiRules are true', () => {
    const result = component.getIndicatorInfo(true, true);

    expect(result).toEqual({
      isOutOfBound: false,
      indicatorIcon: EMPTY,
    });
  });

  it('should return OUT_OF_BOUND_SVG_STRING if both isPassAssayLimit and isPassedMultiRules are false', () => {
    const result = component.getIndicatorInfo(false, false);

    expect(result).toEqual({
      isOutOfBound: true,
      indicatorIcon: OUT_OF_BOUND_SVG_STRING,
    });
  });

  describe('createPointData', () => {
    it('should return the correct CustomDataPoint object', () => {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const pointSeries = {
        origVal: 50,
        isMngd: true,
        isCommented: true,
        yValue: 2.95,
        isPsdAsL: true,
        isPsdCuL: true,
        isPsdCoL: true,
        isPsdMulrL: true,
        qcSet: '2023-01-01T00:00:00Z',
        qcDt: '2023-01-01T00:00:00Z',
        runDetailId: '1',
        runId: '1',
        lotNumber: 'lot123',
        isAutoUnmanaged: false,
        zscoreAssayLowerLimit: 30,
        zscoreAssayMean: 50,
        zscoreAssayUpperLimit: 70,
      } as ChartsPnt;

      const level = LOT_LEVEL.LEVEL_1;

      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      component.parametersDateRange = {
        decimalPoint: 2,
        unitDisplay: 'unit',
      } as ParameterDateRangeInfo;

      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      component.basicCriteria.limitsData = {
        assay: {
          upper: 4,
          target: 0,
          lower: -4,
        },
      } as TargetLimit;

      jest.spyOn(component, 'getIndicatorInfo');
      jest.spyOn(component, 'adjustCommentIconY');
      jest.spyOn(component, 'createDataLabelFormatter');

      const result = component.createPointData(pointSeries, level);

      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      const expected = {
        x: new Date('2023-01-01T00:00:00Z').getTime(),
        y: 2.95,
        originalLowerLimit: 0,
        originalUpperLimit: 0,
        originalTarget: 0,
        qcRunDetailId: '1',
        qcRunId: '1',
        level: level,
        isCommented: true,
        zScorePoint: 2.95,
        value: 50,
        decimalPoint: 2,
        advancedData: { isManaged: true },
        unitDisplay: 'unit',
        rawData: 50,
        lotNumber: 'lot123',
        qcSetInNumber: new Date('2023-01-01T00:00:00Z').getTime(),
        isPassedMultiRulesLimit: true,
        isPassedAssayLimit: true,
        multiRules: component.paramMultiRulesLimitSetting?.assayLimit,
        zscoreAssayLowerLimit: 30,
        zscoreAssayMean: 50,
        zscoreAssayUpperLimit: 70,
        isAutoUnmanaged: false,
        dataLabels: {
          useHTML: true,
          enabled: true,
          y: 0,
          verticalAlign: 'bottom',
          formatter: expect.any(Function),
        },
        qcDateTime: '2023-01-01T00:00:00Z',
        qcDateTimeInNumber: new Date('2023-01-01T00:00:00Z').getTime(),
        isPassed: true,
        isPassedCustomerLimit: true,
        isPassedCountryLimit: true,
      } as unknown as CustomDataPoint;

      expect(result).toEqual(expected);
      expect(component.getIndicatorInfo).toHaveBeenCalledWith(true, true);
      expect(component.adjustCommentIconY).toHaveBeenCalledWith(false, false);
    });
  });

  it('should update point managed status and set isCommented to true', () => {
    // Arrange
    const runId = 'run1';
    const managedStatus = true;
    const level = LOT_LEVEL.LEVEL_1;

    component.chartData = {
      1: {
        pnts: [
          {
            runId: 'run1',
            runDetailId: '1',
            isCommented: false,
            varPct: 0,
            origVal: 0,
            origUpp: 0,
            origLow: 0,
            origTgt: 0,
            qcDt: '',
            isMngd: false,
            isPsd: false,
            isPsdAsL: false,
            isPsdCuL: false,
            isPsdCoL: false,
            groupIndex: -1,
          },
        ],
      },
    };

    // Create a mock chart object that simulates the Highcharts API
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const mockChartObject = {
      series: [
        {
          name: 'Level 1',
          update: jest.fn(),
        },
      ],
    } as unknown as Chart;

    // Create an observable that will emit the mock chart object
    // Set up the component's charts array with the mock ref$
    component.charts[0].ref$ = of(mockChartObject);

    // Spy on the updateSeriesPointData method
    jest.spyOn(component.charts[0].ref$, 'subscribe');
    jest.spyOn(component, 'updateSeriesPointData');

    // Act
    component.handleManagedStatus(runId, managedStatus, level);

    // Assert
    expect(component.chartData['1'].pnts[0].isMngd).toBe(true);
    expect(component.chartData['1'].pnts[0].isCommented).toBe(true);
    expect(component.charts[0].ref$.subscribe).toHaveBeenCalled();
    expect(component.updateSeriesPointData).toHaveBeenCalled();
  });
});
