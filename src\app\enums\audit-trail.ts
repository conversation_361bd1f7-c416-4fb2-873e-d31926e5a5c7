export enum AuditTrailLogType {
  QcParameterSetting = 'QcParameterSetting',
  XbarmParameterSetting = 'XbarmParameterSetting',
  XbarmValueParameter = 'XbarmValueParameter',
  AssayLimitParameter = 'AssayLimitParameter',
  AffiliatedPeerGroupErrorJudgement = 'AffiliatedPeerGroupErrorJudgement',
  Rule = 'Rule',
  RuleNumberSetting = 'RuleNumberSetting',
  Rule1ConstantSetting = 'Rule1ConstantSetting',
  Rule2To5ConstantSetting = 'Rule2To5ConstantSetting',
  RuleCocParameter = 'RuleCocParameter',
  RuleParameter = 'RuleParameter',
  PeerGroupSize = 'PeerGroupSize',
  ReportSetting = 'ReportSetting'
}
