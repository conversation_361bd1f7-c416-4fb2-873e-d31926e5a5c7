import { ColDef } from 'ag-grid-community';

import { BalanceTEAs, MultiplierAndClinicalTEAs, UpperAndLowerLimits } from './rule-param-setting';
import { PeerGroupSizeRowMappingData, PeerGroupSizeSettings } from './admin-parameters';
import {
  Rule1ConstantSetting,
  Rule1ConstantSettingsRowMappingData,
  Rule2To5ConstantSetting,
  Rule2To5ConstantSettingsRowMappingData,
} from './constant-setting';

import { CocParameter } from './coc-parameters';
import { NumberInputConstraints } from './ag-grid-editable-cell';

export interface RuleConfig {
  ruleCode: string;

  componentsConfig: RuleComponentsConfig;

  constantSettingsTable?: RuleParametersTable;

  multiplierTeaTable?: RuleParametersTable;

  balanceTeaTable?: RuleParametersTable;

  upperLowerLimitTable?: RuleParametersTable;

  cocParameterTable?: RuleParametersTable;
}

export interface RuleComponentsConfig {
  hasPeerGroupSize?: boolean;

  hasCreateTicket?: boolean;

  hasNumberSettings?: boolean;

  hasConstantSettings?: boolean;

  hasMultiplierTea?: boolean;

  hasBalanceTea?: boolean;

  hasUpperLowerLimit?: boolean;

  hasCocParameters?: boolean;
}

export interface RuleParametersTable {
  editableColumns: RuleEditableParameter[];

  dataMappingFunc: RuleDataMappingFunc;
}

export interface RuleEditableParameter {
  id: string;

  label: string;

  constraints?: NumberInputConstraints;
}

export interface RuleTableSetting {
  columnDefs: ColDef[];

  dataMappingFuncKey: RuleDataMappingFunc | undefined;
}

export type Rule1ConstantSettingsDataMappingFunc = (data: Rule1ConstantSetting) => Rule1ConstantSettingsRowMappingData;

export type Rule1PeerGroupSize = (data: PeerGroupSizeSettings) => PeerGroupSizeRowMappingData;

export type Rule2To5ConstantSettingsDataMappingFunc = (
  data: Rule2To5ConstantSetting,
) => Rule2To5ConstantSettingsRowMappingData;

export type CocParametersDataMappingFunc = (data: CocParameter) => CocParameter;

export type MultiplierAndClinicalTEAsDataMappingFunc = (data: MultiplierAndClinicalTEAs) => MultiplierAndClinicalTEAs;

export type BalanceTEAsDataMappingFunc = (data: BalanceTEAs) => BalanceTEAs;

export type UpperAndLowerLimitsDataMappingFunc = (data: UpperAndLowerLimits) => UpperAndLowerLimits;

export type RuleDataMappingFunc =
  | Rule1ConstantSettingsDataMappingFunc
  | Rule2To5ConstantSettingsDataMappingFunc
  | CocParametersDataMappingFunc
  | MultiplierAndClinicalTEAsDataMappingFunc
  | BalanceTEAsDataMappingFunc
  | UpperAndLowerLimitsDataMappingFunc
  | Rule1PeerGroupSize;

export type RuleRowMappingData =
  | PeerGroupSizeRowMappingData
  | Rule1ConstantSettingsRowMappingData
  | Rule2To5ConstantSettingsRowMappingData
  | CocParameter
  | MultiplierAndClinicalTEAs
  | BalanceTEAs
  | UpperAndLowerLimits;

export type RulePutRequestData = PeerGroupSizeRowMappingData[] &
  Rule1ConstantSettingsRowMappingData[] &
  Rule2To5ConstantSettingsRowMappingData[] &
  CocParameter[] &
  MultiplierAndClinicalTEAs[] &
  BalanceTEAs[] &
  UpperAndLowerLimits[];

export type RuleTableData =
  | PeerGroupSizeSettings
  | Rule1ConstantSetting
  | Rule2To5ConstantSetting
  | MultiplierAndClinicalTEAs
  | BalanceTEAs
  | UpperAndLowerLimits
  | CocParameter;
