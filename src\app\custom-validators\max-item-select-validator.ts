import { AbstractControl, NG_VALIDATORS, ValidationErrors, Validator } from '@angular/forms';
import { Directive, Input } from '@angular/core';

@Directive({
  selector: '[appMaxItemSelectValidator]',
  providers: [
    {
      provide: NG_VALIDATORS,
      useExisting: MaxItemSelectValidator,
      multi: true,
    },
  ],
  standalone: true,
})
export class MaxItemSelectValidator implements Validator {
  @Input() maxItemSelect = 5;

  validate(control: AbstractControl): ValidationErrors | null {
    if (control.value) {
      const selectedItems = control.value.length;

      if (selectedItems > this.maxItemSelect) {
        return { maxItem: true };
      }
    }

    return null;
  }
}
